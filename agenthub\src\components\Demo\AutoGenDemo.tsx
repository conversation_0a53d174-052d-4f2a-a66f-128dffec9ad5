'use client'

import React, { useState } from 'react'
import { autoGenManager, AGENT_TEMPLATES } from '@/lib/autogen'
import { context7Manager, enhancePromptWithContext7, LIBRARY_SETS } from '@/lib/context7'
import { Bot, Play, Square, Loader2, Code, Book } from 'lucide-react'

export function AutoGenDemo() {
  const [isRunning, setIsRunning] = useState(false)
  const [conversation, setConversation] = useState<any>(null)
  const [messages, setMessages] = useState<any[]>([])
  const [context7Enabled, setContext7Enabled] = useState(true)
  const [selectedLibraries, setSelectedLibraries] = useState<string[]>(LIBRARY_SETS.aiAgents)

  const startDemo = async () => {
    setIsRunning(true)
    setMessages([])

    try {
      // Create AutoGen agents
      const contentAnalyzer = autoGenManager.createAgent(AGENT_TEMPLATES.contentAnalyzer)
      const contentWriter = autoGenManager.createAgent(AGENT_TEMPLATES.contentWriter)
      const qualityReviewer = autoGenManager.createAgent(AGENT_TEMPLATES.qualityReviewer)

      // Start conversation
      const conv = autoGenManager.startConversation([contentAnalyzer, contentWriter, qualityReviewer])
      setConversation(conv)

      // Initial prompt
      let prompt = "Create a comprehensive guide for building AI-powered workflows with AutoGen. Include code examples and best practices."

      // Enhance with Context7 if enabled
      if (context7Enabled) {
        prompt = await enhancePromptWithContext7(prompt, selectedLibraries, 'workflows')
      }

      // Send initial message
      await autoGenManager.sendMessage(conv.id, 'user', prompt)

      // Simulate conversation flow
      const conversationSteps = [
        { agent: contentAnalyzer.id, delay: 2000 },
        { agent: contentWriter.id, delay: 3000 },
        { agent: qualityReviewer.id, delay: 2500 },
        { agent: contentWriter.id, delay: 2000 },
      ]

      for (const step of conversationSteps) {
        await new Promise(resolve => setTimeout(resolve, step.delay))
        
        if (!isRunning) break // Check if demo was stopped
        
        const updatedConv = autoGenManager.getConversation(conv.id)
        if (updatedConv) {
          setMessages([...updatedConv.messages])
        }
      }

    } catch (error) {
      console.error('Demo error:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const stopDemo = () => {
    setIsRunning(false)
    if (conversation) {
      autoGenManager.endConversation(conversation.id)
    }
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          AutoGen + Context7 Demo
        </h2>
        <p className="text-gray-600 mb-4">
          Experience multi-agent conversations enhanced with up-to-date documentation
        </p>
        
        {/* Controls */}
        <div className="flex items-center space-x-4">
          <button
            onClick={isRunning ? stopDemo : startDemo}
            disabled={isRunning && !conversation}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
              isRunning 
                ? 'bg-red-600 hover:bg-red-700 text-white' 
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {isRunning ? (
              <>
                <Square className="w-4 h-4" />
                <span>Stop Demo</span>
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                <span>Start Demo</span>
              </>
            )}
          </button>
          
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="context7-toggle"
              checked={context7Enabled}
              onChange={(e) => setContext7Enabled(e.target.checked)}
              className="rounded"
            />
            <label htmlFor="context7-toggle" className="text-sm text-gray-700">
              Enable Context7 Enhancement
            </label>
          </div>
        </div>
        
        {/* Library Selection */}
        {context7Enabled && (
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Context7 Libraries:
            </label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(LIBRARY_SETS).map(([key, libraries]) => (
                <button
                  key={key}
                  onClick={() => setSelectedLibraries(libraries)}
                  className={`px-3 py-1 text-xs rounded-full transition-colors ${
                    selectedLibraries === libraries
                      ? 'bg-blue-100 text-blue-800 border border-blue-300'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {key}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6">
        {messages.length === 0 && !isRunning ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Bot className="w-8 h-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Ready to Start Demo
            </h3>
            <p className="text-gray-600 max-w-md">
              Click "Start Demo" to see AutoGen agents collaborate on creating 
              AI workflow documentation, enhanced with Context7's up-to-date libraries.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message, index) => {
              const agent = conversation?.agents.find((a: any) => a.id === message.agentId)
              const isUser = message.agentId === 'user'
              
              return (
                <div
                  key={message.id}
                  className={`flex items-start space-x-3 ${
                    isUser ? 'justify-end' : 'justify-start'
                  }`}
                >
                  {!isUser && (
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <Bot className="w-4 h-4 text-blue-600" />
                    </div>
                  )}
                  
                  <div
                    className={`max-w-2xl rounded-lg p-4 ${
                      isUser
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 border border-gray-200'
                    }`}
                  >
                    {!isUser && (
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-semibold text-sm">
                          {agent?.name || 'Agent'}
                        </span>
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded">
                          {agent?.role || 'assistant'}
                        </span>
                      </div>
                    )}
                    
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    
                    <div className={`text-xs mt-2 ${
                      isUser ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      {message.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                  
                  {isUser && (
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-sm font-medium text-gray-600">U</span>
                    </div>
                  )}
                </div>
              )
            })}
            
            {isRunning && (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                <span className="ml-2 text-sm text-gray-600">
                  Agents are collaborating...
                </span>
              </div>
            )}
          </div>
        )}
      </div>
      
      {/* Status Bar */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Code className="w-4 h-4 text-gray-500" />
              <span className="text-gray-600">
                AutoGen: {conversation ? 'Active' : 'Inactive'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Book className="w-4 h-4 text-gray-500" />
              <span className="text-gray-600">
                Context7: {context7Enabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
          <div className="text-gray-500">
            Messages: {messages.length}
          </div>
        </div>
      </div>
    </div>
  )
}
