{"name": "@types/d3-brush", "version": "3.0.6", "description": "TypeScript definitions for d3-brush", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-brush", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-brush"}, "scripts": {}, "dependencies": {"@types/d3-selection": "*"}, "typesPublisherContentHash": "9236bf058564aa97984c8fb6e4ef96b84e6221485def99fbb4aebaccf68e0367", "typeScriptVersion": "4.5"}