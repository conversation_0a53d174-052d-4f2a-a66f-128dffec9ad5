/**
 * 向量化服务 - 模拟文本向量化处理
 * 在实际应用中，这里应该调用真实的向量化API，如OpenAI Embeddings、Cohere等
 */

export interface VectorData {
  text: string
  vector: number[]
  metadata?: Record<string, any>
}

export interface VectorSearchResult {
  text: string
  similarity: number
  metadata?: Record<string, any>
}

class VectorService {
  private vectors: VectorData[] = []

  /**
   * 模拟文本向量化
   * 在实际应用中，这里应该调用真实的向量化API
   */
  async vectorizeText(text: string, metadata?: Record<string, any>): Promise<number[]> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
    
    // 生成模拟向量（768维，模拟OpenAI ada-002的输出）
    const vector = Array.from({ length: 768 }, () => Math.random() * 2 - 1)
    
    // 存储向量数据
    this.vectors.push({
      text,
      vector,
      metadata
    })
    
    return vector
  }

  /**
   * 计算余弦相似度
   */
  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0)
    const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0))
    const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0))
    return dotProduct / (magnitudeA * magnitudeB)
  }

  /**
   * 语义搜索
   * 根据查询文本找到最相似的向量
   */
  async semanticSearch(query: string, topK: number = 5): Promise<VectorSearchResult[]> {
    if (this.vectors.length === 0) {
      return []
    }

    // 向量化查询文本
    const queryVector = await this.vectorizeText(query)
    
    // 计算相似度并排序
    const results = this.vectors
      .map(vectorData => ({
        text: vectorData.text,
        similarity: this.cosineSimilarity(queryVector, vectorData.vector),
        metadata: vectorData.metadata
      }))
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK)
    
    return results
  }

  /**
   * 获取所有向量数据
   */
  getAllVectors(): VectorData[] {
    return [...this.vectors]
  }

  /**
   * 清空所有向量数据
   */
  clearVectors(): void {
    this.vectors = []
  }

  /**
   * 批量向量化文本
   */
  async batchVectorize(texts: string[], metadata?: Record<string, any>[]): Promise<number[][]> {
    const vectors: number[][] = []
    
    for (let i = 0; i < texts.length; i++) {
      const text = texts[i]
      const meta = metadata?.[i]
      const vector = await this.vectorizeText(text, meta)
      vectors.push(vector)
    }
    
    return vectors
  }

  /**
   * 文档分块处理
   * 将长文档分割成较小的块进行向量化
   */
  chunkDocument(text: string, chunkSize: number = 1000, overlap: number = 200): string[] {
    const chunks: string[] = []
    let start = 0
    
    while (start < text.length) {
      const end = Math.min(start + chunkSize, text.length)
      const chunk = text.slice(start, end)
      chunks.push(chunk)
      
      if (end === text.length) break
      start = end - overlap
    }
    
    return chunks
  }

  /**
   * 处理文档并向量化
   */
  async processDocument(
    content: string, 
    filename: string, 
    chunkSize: number = 1000
  ): Promise<{
    chunks: string[]
    vectors: number[][]
    metadata: Record<string, any>[]
  }> {
    // 分块
    const chunks = this.chunkDocument(content, chunkSize)
    
    // 生成元数据
    const metadata = chunks.map((chunk, index) => ({
      filename,
      chunkIndex: index,
      totalChunks: chunks.length,
      chunkSize: chunk.length
    }))
    
    // 批量向量化
    const vectors = await this.batchVectorize(chunks, metadata)
    
    return {
      chunks,
      vectors,
      metadata
    }
  }
}

// 导出单例实例
export const vectorService = new VectorService()

// 导出类型
export type { VectorService }
