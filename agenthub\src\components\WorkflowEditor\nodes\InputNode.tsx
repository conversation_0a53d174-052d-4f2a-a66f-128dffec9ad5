'use client'

import React from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Play } from 'lucide-react'

interface InputNodeData {
  label: string
}

export function InputNode({ data }: NodeProps<InputNodeData>) {
  return (
    <div className="bg-white border-2 border-green-300 rounded-lg shadow-md min-w-[120px]">
      <div className="p-3">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
            <Play className="w-3 h-3 text-green-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 text-sm">{data.label}</h3>
            <span className="text-xs text-green-600 bg-green-50 px-2 py-0.5 rounded">
              Input
            </span>
          </div>
        </div>
      </div>
      
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </div>
  )
}
