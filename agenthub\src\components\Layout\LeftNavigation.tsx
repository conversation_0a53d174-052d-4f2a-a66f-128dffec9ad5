'use client'

import React from 'react'
import { useStore } from '@/store/useStore'
import {
  MessageSquare,
  Workflow,
  Store,
  Bot,
  Settings,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useTranslation } from '@/hooks/useTranslation'

const navigationItems = [
  { id: 'chat', labelKey: 'nav.chat', icon: MessageSquare },
  { id: 'workflows', labelKey: 'nav.workflows', icon: Workflow },
  { id: 'marketplace', labelKey: 'nav.marketplace', icon: Store },
  { id: 'agents', labelKey: 'nav.agents', icon: Bot },
  { id: 'settings', labelKey: 'nav.settings', icon: Settings },
] as const

export function LeftNavigation() {
  const { currentView, setCurrentView, sidebarCollapsed, setSidebarCollapsed } = useStore()
  const t = useTranslation()

  return (
    <div className={cn(
      "fixed left-0 top-14 h-[calc(100vh-3.5rem)] bg-white border-r border-gray-200 transition-all duration-300 z-10",
      sidebarCollapsed ? "w-16" : "w-64"
    )}>
      {/* Collapse Toggle */}
      <div className="flex justify-end p-2 border-b border-gray-100">
        <button
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
        >
          {sidebarCollapsed ? (
            <ChevronRight className="w-4 h-4" />
          ) : (
            <ChevronLeft className="w-4 h-4" />
          )}
        </button>
      </div>
      
      {/* Navigation Items */}
      <nav className="p-2 space-y-1">
        {navigationItems.map((item) => {
          const Icon = item.icon
          const isActive = currentView === item.id
          
          return (
            <button
              key={item.id}
              onClick={() => setCurrentView(item.id)}
              className={cn(
                "w-full flex items-center text-left transition-colors rounded-lg",
                sidebarCollapsed ? "p-3 justify-center" : "p-3 space-x-3",
                isActive
                  ? "bg-blue-50 text-blue-700 border border-blue-200"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              )}
              title={sidebarCollapsed ? t(item.labelKey) : undefined}
            >
              <Icon className={cn(
                "flex-shrink-0",
                sidebarCollapsed ? "w-5 h-5" : "w-5 h-5"
              )} />
              {!sidebarCollapsed && (
                <span className="font-medium">{t(item.labelKey)}</span>
              )}
            </button>
          )
        })}
      </nav>
      
      {/* Quick Actions */}
      {!sidebarCollapsed && (
        <div className="absolute bottom-4 left-2 right-2">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white">
            <h3 className="font-semibold text-sm mb-1">Create New Workflow</h3>
            <p className="text-xs text-blue-100 mb-3">
              Build powerful AI-driven workflows
            </p>
            <button className="w-full bg-white/20 hover:bg-white/30 text-white text-xs font-medium py-2 px-3 rounded-md transition-colors">
              Get Started
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
