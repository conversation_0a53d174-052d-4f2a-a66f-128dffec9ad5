'use client'

import React, { useState } from 'react'
import { Search, Star, Download, ExternalLink, X, Eye, User, Calendar, Tag } from 'lucide-react'
import * as Dialog from '@radix-ui/react-dialog'
import { useTranslation } from '@/hooks/useTranslation'
import { useStore } from '@/store/useStore'
import { Workflow } from '@/store/useStore'

const marketplaceItems = [
  {
    id: '1',
    name: 'Email Marketing Agent',
    description: 'Automated email campaign management with AI-powered content generation',
    category: 'Marketing',
    rating: 4.8,
    downloads: 1250,
    price: 'Free',
    image: '📧',
    author: 'MarketingPro',
    createdAt: '2024-01-15',
    tags: ['Email', 'Marketing', 'Automation', 'AI'],
    detailedDescription: 'This comprehensive email marketing workflow automates your entire email campaign process. It includes AI-powered content generation, audience segmentation, A/B testing, and performance analytics. The workflow can automatically create personalized email content based on user behavior, schedule optimal send times, and track engagement metrics.',
    features: [
      'AI-powered email content generation',
      'Automatic audience segmentation',
      'A/B testing capabilities',
      'Real-time analytics dashboard',
      'Integration with major email platforms',
      'Personalization based on user behavior'
    ],
    requirements: [
      'Email service provider API key',
      'Customer database access',
      'Basic marketing knowledge'
    ]
  },
  {
    id: '2',
    name: 'Data Analysis Workflow',
    description: 'Complete data processing pipeline with visualization and reporting',
    category: 'Analytics',
    rating: 4.9,
    downloads: 890,
    price: '$29',
    image: '📊',
    author: 'DataScience Team',
    createdAt: '2024-02-20',
    tags: ['Data', 'Analytics', 'Visualization', 'Reports'],
    detailedDescription: 'A powerful data analysis workflow that transforms raw data into actionable insights. This workflow includes data cleaning, statistical analysis, machine learning models, and automated report generation. Perfect for businesses looking to make data-driven decisions.',
    features: [
      'Automated data cleaning and preprocessing',
      'Statistical analysis and modeling',
      'Interactive data visualizations',
      'Automated report generation',
      'Machine learning integration',
      'Export to multiple formats'
    ],
    requirements: [
      'Data source connection',
      'Basic understanding of data analysis',
      'Storage for processed data'
    ]
  },
  {
    id: '3',
    name: 'Customer Support Bot',
    description: 'Intelligent customer service automation with natural language processing',
    category: 'Support',
    rating: 4.7,
    downloads: 2100,
    price: 'Free',
    image: '🤖',
    author: 'SupportAI',
    createdAt: '2024-03-10',
    tags: ['Support', 'Chatbot', 'NLP', 'Customer Service'],
    detailedDescription: 'An advanced customer support bot that handles customer inquiries 24/7. Uses natural language processing to understand customer questions and provides accurate responses. Can escalate complex issues to human agents and learn from interactions.',
    features: [
      '24/7 automated customer support',
      'Natural language understanding',
      'Multi-language support',
      'Escalation to human agents',
      'Learning from interactions',
      'Integration with help desk systems'
    ],
    requirements: [
      'Customer support knowledge base',
      'Chat platform integration',
      'Basic customer service setup'
    ]
  },
]

export function MarketplaceView() {
  const [selectedItem, setSelectedItem] = useState<typeof marketplaceItems[0] | null>(null)
  const t = useTranslation()
  const { addWorkflow } = useStore()

  const handleSaveWorkflow = (item: typeof marketplaceItems[0]) => {
    // Convert marketplace item to workflow format
    const newWorkflow: Workflow = {
      id: `workflow-${Date.now()}`,
      name: item.title,
      description: item.description,
      nodes: [
        {
          id: '1',
          type: 'input',
          position: { x: 100, y: 100 },
          data: { label: 'Start' }
        },
        {
          id: '2',
          type: 'ai-agent',
          position: { x: 300, y: 100 },
          data: {
            label: item.title,
            description: item.description,
            config: {
              model: 'gpt-4',
              temperature: 0.7,
              maxTokens: 1000,
              systemMessage: `You are a ${item.title.toLowerCase()} specialist.`
            }
          }
        },
        {
          id: '3',
          type: 'output',
          position: { x: 500, y: 100 },
          data: { label: 'Output' }
        }
      ],
      edges: [
        {
          id: 'e1-2',
          source: '1',
          target: '2'
        },
        {
          id: 'e2-3',
          source: '2',
          target: '3'
        }
      ],
      createdAt: new Date(),
      updatedAt: new Date()
    }

    addWorkflow(newWorkflow)
    setSelectedItem(null)

    // Show success message (you could add a toast notification here)
    alert(`工作流 "${item.title}" 已成功保存到我的工作流！`)
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('marketplace.title')}</h1>
        <p className="text-gray-600 mt-1">
          {t('marketplace.description')}
        </p>
        
        {/* Search */}
        <div className="mt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder={t('marketplace.searchWorkflows')}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {marketplaceItems.map((item) => (
            <div
              key={item.id}
              className="bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => setSelectedItem(item)}
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="text-3xl">{item.image}</div>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {item.category}
                  </span>
                </div>
                
                <h3 className="font-semibold text-gray-900 mb-2">{item.name}</h3>
                <p className="text-sm text-gray-600 mb-4">{item.description}</p>
                
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span>{item.rating}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Download className="w-4 h-4" />
                    <span>{item.downloads}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="font-semibold text-gray-900">{item.price}</span>
                  <div className="flex items-center space-x-2">
                    <button
                      className="flex items-center space-x-1 text-gray-600 hover:text-gray-700 text-sm font-medium"
                      onClick={(e) => {
                        e.stopPropagation()
                        setSelectedItem(item)
                      }}
                    >
                      <Eye className="w-3 h-3" />
                      <span>{t('marketplace.viewDetails')}</span>
                    </button>
                    <button
                      className="flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm font-medium"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleSaveWorkflow(item)
                      }}
                    >
                      <span>{t('marketplace.saveToMyWorkflows')}</span>
                      <ExternalLink className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Workflow Details Modal */}
      <Dialog.Root open={!!selectedItem} onOpenChange={() => setSelectedItem(null)}>
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
          <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto z-50">
            {selectedItem && (
              <div className="p-6">
                {/* Header */}
                <div className="flex items-start justify-between mb-6">
                  <div className="flex items-start space-x-4">
                    <div className="text-4xl">{selectedItem.image}</div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedItem.name}</h2>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <User className="w-4 h-4" />
                          <span>{t('marketplace.author')}: {selectedItem.author}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4" />
                          <span>{selectedItem.createdAt}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span>{selectedItem.rating}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Download className="w-4 h-4" />
                          <span>{selectedItem.downloads} {t('marketplace.downloads')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Dialog.Close asChild>
                    <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                      <X className="w-5 h-5" />
                    </button>
                  </Dialog.Close>
                </div>

                {/* Tags */}
                <div className="mb-6">
                  <div className="flex items-center space-x-2 mb-2">
                    <Tag className="w-4 h-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-700">{t('marketplace.tags')}:</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {selectedItem.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Description */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">{t('marketplace.description_label')}</h3>
                  <p className="text-gray-700 leading-relaxed">{selectedItem.detailedDescription}</p>
                </div>

                {/* Features */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Features</h3>
                  <ul className="space-y-2">
                    {selectedItem.features.map((feature, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Requirements */}
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Requirements</h3>
                  <ul className="space-y-2">
                    {selectedItem.requirements.map((requirement, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-700">{requirement}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                  <div className="flex items-center space-x-4">
                    <span className="text-2xl font-bold text-gray-900">{selectedItem.price}</span>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {selectedItem.category}
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Dialog.Close asChild>
                      <button className="px-4 py-2 text-gray-600 hover:text-gray-700 border border-gray-300 rounded-lg transition-colors">
                        {t('marketplace.close')}
                      </button>
                    </Dialog.Close>
                    <button
                      className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                      onClick={() => {
                        if (selectedItem) {
                          handleSaveWorkflow(selectedItem)
                        }
                      }}
                    >
                      {t('marketplace.saveToMyWorkflows')}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </div>
  )
}
