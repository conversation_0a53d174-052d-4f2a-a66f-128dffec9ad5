'use client'

import React from 'react'
import { Search, Star, Download, ExternalLink } from 'lucide-react'

const marketplaceItems = [
  {
    id: '1',
    name: 'Email Marketing Agent',
    description: 'Automated email campaign management with AI-powered content generation',
    category: 'Marketing',
    rating: 4.8,
    downloads: 1250,
    price: 'Free',
    image: '📧',
  },
  {
    id: '2',
    name: 'Data Analysis Workflow',
    description: 'Complete data processing pipeline with visualization and reporting',
    category: 'Analytics',
    rating: 4.9,
    downloads: 890,
    price: '$29',
    image: '📊',
  },
  {
    id: '3',
    name: 'Customer Support Bot',
    description: 'Intelligent customer service automation with natural language processing',
    category: 'Support',
    rating: 4.7,
    downloads: 2100,
    price: 'Free',
    image: '🤖',
  },
]

export function MarketplaceView() {
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <h1 className="text-2xl font-bold text-gray-900">Marketplace</h1>
        <p className="text-gray-600 mt-1">
          Discover and install pre-built workflows and AI agents
        </p>
        
        {/* Search */}
        <div className="mt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search marketplace..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {marketplaceItems.map((item) => (
            <div
              key={item.id}
              className="bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="text-3xl">{item.image}</div>
                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                    {item.category}
                  </span>
                </div>
                
                <h3 className="font-semibold text-gray-900 mb-2">{item.name}</h3>
                <p className="text-sm text-gray-600 mb-4">{item.description}</p>
                
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span>{item.rating}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Download className="w-4 h-4" />
                    <span>{item.downloads}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="font-semibold text-gray-900">{item.price}</span>
                  <button className="flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm font-medium">
                    <span>Install</span>
                    <ExternalLink className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
