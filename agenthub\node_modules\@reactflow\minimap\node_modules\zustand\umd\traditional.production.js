!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("use-sync-external-store/shim/with-selector"),require("zustand/vanilla")):"function"==typeof define&&define.amd?define(["exports","react","use-sync-external-store/shim/with-selector","zustand/vanilla"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zustandTraditional={},e.<PERSON><PERSON>,e.useSyncExternalStoreShimWithSelector,e.zustandVanilla)}(this,(function(e,t,n,r){"use strict";var i=t.useDebugValue,a=n.useSyncExternalStoreWithSelector,u=function(e){return e};function o(e,t,n){void 0===t&&(t=u);var r=a(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return i(r),r}var s=function(e,t){var n=r.createStore(e),i=function(e,r){return void 0===r&&(r=t),o(n,e,r)};return Object.assign(i,n),i};e.createWithEqualityFn=function(e,t){return e?s(e,t):s},e.useStoreWithEqualityFn=o}));
