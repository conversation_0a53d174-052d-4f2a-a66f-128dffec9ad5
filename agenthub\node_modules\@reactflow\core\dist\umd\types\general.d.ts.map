{"version": 3, "file": "general.d.ts", "sourceRoot": "", "sources": ["../../../../packages/core/src/types/general.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,UAAU,IAAI,eAAe,EAC7B,UAAU,IAAI,eAAe,EAC7B,aAAa,EACb,mBAAmB,EACpB,MAAM,OAAO,CAAC;AACf,OAAO,KAAK,EAAE,WAAW,EAAE,SAAS,IAAI,WAAW,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC;AAE/F,OAAO,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAC;AAC7E,OAAO,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AACxD,OAAO,KAAK,EACV,IAAI,EACJ,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,aAAa,EACb,YAAY,EACZ,eAAe,EACf,oBAAoB,EACpB,UAAU,EACX,MAAM,SAAS,CAAC;AACjB,OAAO,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAC9D,OAAO,KAAK,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAC9D,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,GAAG,CAAC;AAC5C,OAAO,KAAK,EAAE,iBAAiB,EAAE,MAAM,YAAY,CAAC;AAEpD,MAAM,MAAM,SAAS,GAAG;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,CAAA;CAAE,CAAC;AACpE,MAAM,MAAM,gBAAgB,GAAG;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,mBAAmB,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAA;CAAE,CAAC;AACpG,MAAM,MAAM,SAAS,GAAG;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,aAAa,CAAC,SAAS,CAAC,CAAA;CAAE,CAAC;AACpE,MAAM,MAAM,gBAAgB,GAAG;IAAE,CAAC,GAAG,EAAE,MAAM,GAAG,mBAAmB,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,CAAA;CAAE,CAAC;AAEpG,MAAM,MAAM,OAAO,GAAG,CAAC,cAAc,CAAC,EAAE,cAAc,KAAK,OAAO,CAAC;AAEnE,MAAM,MAAM,OAAO,GAAG,CAAC,QAAQ,EAAE,UAAU,KAAK,UAAU,CAAC;AAE3D,MAAM,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,IAAI,CAAC;AAC5D,MAAM,MAAM,aAAa,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,IAAI,CAAC;AAE5D,MAAM,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;AACpD,MAAM,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;AAEpD,MAAM,MAAM,MAAM,GAAG,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,EAAE,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;AAClF,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC;AACjC,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC;AAE/B,MAAM,MAAM,SAAS,GAAG,CAAC,OAAO,CAAC,EAAE,6BAA6B,KAAK,IAAI,CAAC;AAC1E,MAAM,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,6BAA6B,KAAK,IAAI,CAAC;AAC1F,MAAM,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC;AACnC,MAAM,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC;AACzC,MAAM,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,EAAE,6BAA6B,KAAK,IAAI,CAAC;AAChG,MAAM,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,gBAAgB,KAAK,IAAI,CAAC;AACnF,MAAM,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE,gBAAgB,KAAK,IAAI,CAAC;AAE3E,MAAM,MAAM,MAAM,CAAC,QAAQ,GAAG,GAAG,EAAE,QAAQ,GAAG,GAAG,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,IAAI,CAAC;AAExH,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;IAC5B,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;CAC7B;AAED,MAAM,MAAM,iBAAiB,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,UAAU,KAAK,OAAO,CAAC;AAErE,MAAM,MAAM,gBAAgB,GAAG,OAAO,GAAG,SAAS,CAAC;AAEnD,oBAAY,cAAc;IACxB,MAAM,WAAW;IACjB,KAAK,UAAU;CAChB;AAED,MAAM,MAAM,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,KAAK,IAAI,CAAC;AAEzD,MAAM,MAAM,cAAc,GAAG;IAC3B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,kBAAkB,CAAC,EAAE,OAAO,CAAC;IAC7B,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;QAAE,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;KAAE,CAAC,EAAE,CAAC;CAChD,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtB,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACxB,UAAU,EAAE,UAAU,GAAG,IAAI,CAAC;CAC/B,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,eAAe,GAAG,eAAe,EAAE,MAAM,EAAE,oBAAoB,KAAK,IAAI,CAAC;AAC9G,MAAM,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,KAAK,IAAI,CAAC;AAEpE,MAAM,MAAM,QAAQ,GAAG;IACrB,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,IAAI,EAAE,MAAM,CAAC;CACd,CAAC;AAEF,MAAM,MAAM,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAE7C,MAAM,MAAM,QAAQ,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAExC,oBAAY,eAAe;IACzB,IAAI,SAAS;IACb,QAAQ,aAAa;IACrB,UAAU,eAAe;CAC1B;AAED,MAAM,MAAM,6BAA6B,GAAG;IAC1C,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG,6BAA6B,GAAG;IAC7D,IAAI,CAAC,EAAE,MAAM,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG,6BAA6B,GAAG;IAC7D,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB,CAAC;AAEF,MAAM,MAAM,2BAA2B,GAAG;IACxC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC;IACf,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,QAAQ,KAAK,IAAI,CAAC;AAE5D,MAAM,MAAM,uBAAuB,GAAG;IACpC,MAAM,EAAE,SAAS,CAAC;IAClB,OAAO,EAAE,SAAS,CAAC;IACnB,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE,OAAO,CAAC;IACjB,WAAW,EAAE,WAAW,CAAC;IACzB,WAAW,EAAE,WAAW,CAAC;IACzB,OAAO,EAAE,OAAO,CAAC;IACjB,SAAS,EAAE,SAAS,CAAC;IACrB,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,OAAO,CAAC;IACjB,oBAAoB,EAAE,CAAC,QAAQ,EAAE,UAAU,KAAK,UAAU,CAAC;IAC3D,oBAAoB,EAAE,CAAC,QAAQ,EAAE,UAAU,KAAK,UAAU,CAAC;IAC3D,mBAAmB,EAAE,OAAO,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC3B,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,SAAS,CAAC;IACrB,aAAa,EAAE,aAAa,CAAC;IAC7B,KAAK,EAAE,IAAI,EAAE,CAAC;IACd,aAAa,EAAE,aAAa,GAAG,IAAI,CAAC;IACpC,aAAa,EAAE,aAAa,GAAG,IAAI,CAAC;IACpC,eAAe,EAAE,OAAO,CAAC;IACzB,eAAe,EAAE,OAAO,CAAC;IACzB,OAAO,EAAE,cAAc,GAAG,IAAI,CAAC;IAC/B,YAAY,EAAE,OAAO,CAAC;IACtB,cAAc,EAAE,MAAM,CAAC;IAEvB,MAAM,EAAE,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;IAC9C,WAAW,EAAE,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;IACnE,aAAa,EAAE,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC;IAC7E,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,eAAe,EAAE,gBAAgB,CAAC;IAClC,UAAU,EAAE,gBAAgB,CAAC;IAC7B,UAAU,EAAE,UAAU,CAAC;IACvB,iBAAiB,EAAE,MAAM,CAAC;IAE1B,oBAAoB,EAAE,OAAO,CAAC;IAC9B,mBAAmB,EAAE,OAAO,CAAC;IAC7B,iBAAiB,EAAE,aAAa,GAAG,IAAI,CAAC;IAGxC,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,kBAAkB,EAAE,MAAM,GAAG,IAAI,CAAC;IAClC,oBAAoB,EAAE,UAAU,GAAG,IAAI,CAAC;IACxC,kBAAkB,EAAE,UAAU,CAAC;IAC/B,gBAAgB,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAC1C,cAAc,EAAE,cAAc,CAAC;IAE/B,UAAU,EAAE,OAAO,CAAC;IACpB,QAAQ,EAAE,QAAQ,CAAC;IAEnB,cAAc,EAAE,OAAO,CAAC;IACxB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,cAAc,EAAE,OAAO,CAAC;IACxB,cAAc,EAAE,OAAO,CAAC;IACxB,cAAc,EAAE,OAAO,CAAC;IACxB,kBAAkB,EAAE,OAAO,CAAC;IAC5B,oBAAoB,EAAE,OAAO,CAAC;IAE9B,oBAAoB,EAAE,OAAO,CAAC;IAE9B,qBAAqB,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAC/C,mBAAmB,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAE7C,0BAA0B,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAEpD,eAAe,CAAC,EAAE,eAAe,CAAC;IAClC,UAAU,CAAC,EAAE,eAAe,CAAC;IAC7B,cAAc,CAAC,EAAE,eAAe,CAAC;IAEjC,oBAAoB,CAAC,EAAE,oBAAoB,CAAC;IAC5C,eAAe,CAAC,EAAE,oBAAoB,CAAC;IACvC,mBAAmB,CAAC,EAAE,oBAAoB,CAAC;IAE3C,SAAS,CAAC,EAAE,SAAS,CAAC;IACtB,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC,YAAY,CAAC,EAAE,YAAY,CAAC;IAE5B,mBAAmB,CAAC,EAAE,cAAc,CAAC;IACrC,iBAAiB,CAAC,EAAE,YAAY,CAAC;IAEjC,cAAc,EAAE,OAAO,CAAC;IACxB,kBAAkB,CAAC,EAAE,kBAAkB,CAAC;IAExC,aAAa,EAAE,OAAO,CAAC;IACvB,iBAAiB,EAAE,OAAO,CAAC;IAC3B,oBAAoB,EAAE,cAAc,GAAG,SAAS,CAAC;IAEjD,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,aAAa,CAAC,EAAE,aAAa,CAAC;IAC9B,OAAO,CAAC,EAAE,OAAO,CAAC;IAGlB,qBAAqB,CAAC,EAAE,gBAAgB,CAAC;IACzC,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IACpC,mBAAmB,CAAC,EAAE,gBAAgB,CAAC;IAEvC,iBAAiB,EAAE,qBAAqB,EAAE,CAAC;IAE3C,eAAe,EAAE,MAAM,CAAC;IACxB,gBAAgB,EAAE,OAAO,CAAC;IAC1B,iBAAiB,EAAE,OAAO,CAAC;IAC3B,gBAAgB,EAAE,MAAM,CAAC;IAEzB,iBAAiB,CAAC,EAAE,iBAAiB,CAAC;CACvC,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;IAClC,QAAQ,EAAE,MAAM,IAAI,EAAE,CAAC;IACvB,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;IAClC,uBAAuB,EAAE,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,IAAI,CAAC;IAClE,oBAAoB,EAAE,CAAC,OAAO,EAAE,mBAAmB,EAAE,KAAK,IAAI,CAAC;IAC/D,mBAAmB,EAAE,CAAC,aAAa,EAAE,YAAY,EAAE,GAAG,IAAI,EAAE,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,CAAC;IACnH,qBAAqB,EAAE,MAAM,IAAI,CAAC;IAClC,qBAAqB,EAAE,CAAC,MAAM,CAAC,EAAE,2BAA2B,KAAK,IAAI,CAAC;IACtE,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,IAAI,CAAC;IAC9C,gBAAgB,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,IAAI,CAAC;IAC9C,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IACtC,UAAU,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;IACtC,kBAAkB,EAAE,CAAC,eAAe,EAAE,gBAAgB,KAAK,IAAI,CAAC;IAChE,aAAa,EAAE,CAAC,UAAU,EAAE,gBAAgB,KAAK,IAAI,CAAC;IACtD,gBAAgB,EAAE,MAAM,IAAI,CAAC;IAC7B,KAAK,EAAE,MAAM,IAAI,CAAC;IAClB,kBAAkB,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,IAAI,CAAC;IACpD,KAAK,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,OAAO,CAAC;CACvC,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG,cAAc,GAAG,gBAAgB,CAAC;AAE/D,MAAM,MAAM,mBAAmB,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,KAAK,IAAI,CAAC;AAEtE,MAAM,MAAM,uBAAuB,GAAG;IACpC,KAAK,EAAE,IAAI,EAAE,CAAC;IACd,KAAK,EAAE,IAAI,EAAE,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,qBAAqB,GAAG,CAAC,MAAM,EAAE,uBAAuB,KAAK,IAAI,CAAC;AAE9E,MAAM,MAAM,aAAa,GAAG,UAAU,GAAG,YAAY,GAAG,WAAW,GAAG,aAAa,GAAG,eAAe,GAAG,cAAc,CAAC;AAEvH,MAAM,MAAM,UAAU,GAAG;IACvB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,eAAe,EAAE,OAAO,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG,WAAW,CAAC,cAAc,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;AAE9E,oBAAY,aAAa;IACvB,OAAO,YAAY;IACnB,IAAI,SAAS;CACd;AAED,MAAM,MAAM,aAAa,GAAG,IAAI,GAAG;IACjC,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;CAChB,CAAC;AAEF,MAAM,MAAM,OAAO,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,KAAK,IAAI,CAAC;AAE5D,MAAM,MAAM,iBAAiB,GAAG;IAC9B,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,oBAAoB,GAAG;IACjC,eAAe,CAAC,EAAE,OAAO,CAAC;CAC3B,CAAC"}