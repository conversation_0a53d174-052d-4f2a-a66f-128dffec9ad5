'use client'

import React from 'react'
import { User, Bell, Shield, Database, Palette, Globe } from 'lucide-react'
import * as Tabs from '@radix-ui/react-tabs'

export function SettingsView() {
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-1">
          Manage your account, preferences, and system configuration
        </p>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        <Tabs.Root defaultValue="profile" className="h-full">
          <div className="flex h-full">
            {/* Sidebar */}
            <div className="w-64 bg-white border-r border-gray-200 p-4">
              <Tabs.List className="space-y-1">
                <Tabs.Trigger
                  value="profile"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <User className="w-4 h-4" />
                  <span>Profile</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="notifications"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Bell className="w-4 h-4" />
                  <span>Notifications</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="security"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Shield className="w-4 h-4" />
                  <span>Security</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="integrations"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Database className="w-4 h-4" />
                  <span>Integrations</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="appearance"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Palette className="w-4 h-4" />
                  <span>Appearance</span>
                </Tabs.Trigger>
              </Tabs.List>
            </div>

            {/* Content */}
            <div className="flex-1 p-6">
              <Tabs.Content value="profile" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Profile Information</h2>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <input
                        type="text"
                        defaultValue="John Doe"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="notifications" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">Workflow Notifications</h3>
                        <p className="text-sm text-gray-600">Get notified when workflows complete</p>
                      </div>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">Agent Updates</h3>
                        <p className="text-sm text-gray-600">Receive updates about agent status</p>
                      </div>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="security" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Security Settings</h2>
                  <div className="space-y-4">
                    <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <h3 className="font-medium text-gray-900">Change Password</h3>
                      <p className="text-sm text-gray-600">Update your account password</p>
                    </button>
                    <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <h3 className="font-medium text-gray-900">Two-Factor Authentication</h3>
                      <p className="text-sm text-gray-600">Add an extra layer of security</p>
                    </button>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="integrations" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Connected Services</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <h3 className="font-medium text-gray-900">OpenAI API</h3>
                        <p className="text-sm text-gray-600">Connected</p>
                      </div>
                      <button className="text-sm text-red-600 hover:text-red-700">Disconnect</button>
                    </div>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="appearance" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Appearance</h2>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Theme
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option>Light</option>
                        <option>Dark</option>
                        <option>System</option>
                      </select>
                    </div>
                  </div>
                </div>
              </Tabs.Content>
            </div>
          </div>
        </Tabs.Root>
      </div>
    </div>
  )
}
