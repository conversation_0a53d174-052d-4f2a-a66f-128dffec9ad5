'use client'

import React, { useState } from 'react'
import { User, Bell, Shield, Database, Palette, Globe, Plus, Edit, Trash2, X } from 'lucide-react'
import * as Tabs from '@radix-ui/react-tabs'
import * as Dialog from '@radix-ui/react-dialog'
import { useStore } from '@/store/useStore'
import { useTranslation } from '@/hooks/useTranslation'
import { Language } from '@/lib/i18n'
import { AIModel } from '@/store/useStore'

export function SettingsView() {
  const { language, setLanguage, aiModels, addAIModel, updateAIModel, deleteAIModel } = useStore()
  const t = useTranslation()
  const [isModelDialogOpen, setIsModelDialogOpen] = useState(false)
  const [editingModel, setEditingModel] = useState<AIModel | null>(null)
  const [modelForm, setModelForm] = useState({
    name: '',
    provider: '',
    apiKey: '',
    endpoint: '',
    maxTokens: 4000,
    temperature: 0.7
  })

  const handleAddModel = () => {
    setEditingModel(null)
    setModelForm({
      name: '',
      provider: '',
      apiKey: '',
      endpoint: '',
      maxTokens: 4000,
      temperature: 0.7
    })
    setIsModelDialogOpen(true)
  }

  const handleEditModel = (model: AIModel) => {
    setEditingModel(model)
    setModelForm({
      name: model.name,
      provider: model.provider,
      apiKey: model.apiKey,
      endpoint: model.endpoint || '',
      maxTokens: model.maxTokens,
      temperature: model.temperature
    })
    setIsModelDialogOpen(true)
  }

  const handleSaveModel = () => {
    if (editingModel) {
      updateAIModel(editingModel.id, {
        ...modelForm,
        endpoint: modelForm.endpoint || undefined
      })
    } else {
      const newModel: AIModel = {
        id: `model-${Date.now()}`,
        ...modelForm,
        endpoint: modelForm.endpoint || undefined,
        createdAt: new Date()
      }
      addAIModel(newModel)
    }
    setIsModelDialogOpen(false)
  }

  const handleDeleteModel = (id: string) => {
    if (confirm('确定要删除这个AI模型吗？')) {
      deleteAIModel(id)
    }
  }
  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <h1 className="text-2xl font-bold text-gray-900">{t('settings.title')}</h1>
        <p className="text-gray-600 mt-1">
          {t('settings.description')}
        </p>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        <Tabs.Root defaultValue="general" className="h-full">
          <div className="flex h-full">
            {/* Sidebar */}
            <div className="w-64 bg-white border-r border-gray-200 p-4">
              <Tabs.List className="space-y-1">
                <Tabs.Trigger
                  value="profile"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <User className="w-4 h-4" />
                  <span>Profile</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="notifications"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Bell className="w-4 h-4" />
                  <span>Notifications</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="security"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Shield className="w-4 h-4" />
                  <span>Security</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="integrations"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Database className="w-4 h-4" />
                  <span>Integrations</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="general"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Globe className="w-4 h-4" />
                  <span>{t('settings.general')}</span>
                </Tabs.Trigger>
                <Tabs.Trigger
                  value="appearance"
                  className="w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors"
                >
                  <Palette className="w-4 h-4" />
                  <span>{t('settings.appearance')}</span>
                </Tabs.Trigger>
              </Tabs.List>
            </div>

            {/* Content */}
            <div className="flex-1 p-6">
              <Tabs.Content value="general" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">{t('settings.general')}</h2>
                  <div className="space-y-6">
                    {/* Language Setting */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {t('settings.language')}
                      </label>
                      <p className="text-sm text-gray-600 mb-3">
                        {t('settings.languageDescription')}
                      </p>
                      <select
                        value={language}
                        onChange={(e) => setLanguage(e.target.value as Language)}
                        className="w-full max-w-xs px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                      >
                        <option value="en" className="text-black">{t('settings.english')}</option>
                        <option value="zh" className="text-black">{t('settings.chinese')}</option>
                      </select>
                    </div>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="profile" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Profile Information</h2>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Full Name
                      </label>
                      <input
                        type="text"
                        defaultValue="John Doe"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="notifications" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">Workflow Notifications</h3>
                        <p className="text-sm text-gray-600">Get notified when workflows complete</p>
                      </div>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium text-gray-900">Agent Updates</h3>
                        <p className="text-sm text-gray-600">Receive updates about agent status</p>
                      </div>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="security" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Security Settings</h2>
                  <div className="space-y-4">
                    <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <h3 className="font-medium text-gray-900">Change Password</h3>
                      <p className="text-sm text-gray-600">Update your account password</p>
                    </button>
                    <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <h3 className="font-medium text-gray-900">Two-Factor Authentication</h3>
                      <p className="text-sm text-gray-600">Add an extra layer of security</p>
                    </button>
                  </div>
                </div>
              </Tabs.Content>

              <Tabs.Content value="integrations" className="space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold text-gray-900">Connected Services</h2>
                    <button
                      onClick={handleAddModel}
                      className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                      <span>添加模型</span>
                    </button>
                  </div>

                  {aiModels.length === 0 ? (
                    <div className="text-center py-8">
                      <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">暂无AI模型</h3>
                      <p className="text-gray-600 mb-4">添加您的第一个AI模型以开始创建智能代理。</p>
                      <button
                        onClick={handleAddModel}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                      >
                        添加您的第一个模型
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {aiModels.map((model) => (
                        <div key={model.id} className="bg-white border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <h3 className="font-medium text-gray-900">{model.name}</h3>
                              <p className="text-sm text-gray-600">{model.provider}</p>
                              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                <span>最大令牌: {model.maxTokens}</span>
                                <span>温度: {model.temperature}</span>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleEditModel(model)}
                                className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                                title="编辑模型"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteModel(model.id)}
                                className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                                title="删除模型"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </Tabs.Content>

              <Tabs.Content value="appearance" className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 mb-4">Appearance</h2>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Theme
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black">
                        <option className="text-black">Light</option>
                        <option className="text-black">Dark</option>
                        <option className="text-black">System</option>
                      </select>
                    </div>
                  </div>
                </div>
              </Tabs.Content>
            </div>
          </div>
        </Tabs.Root>

        {/* AI Model Dialog */}
        <Dialog.Root open={isModelDialogOpen} onOpenChange={setIsModelDialogOpen}>
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
            <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl z-50 w-full max-w-md p-6">
              <div className="flex items-center justify-between mb-4">
                <Dialog.Title className="text-lg font-semibold text-gray-900">
                  {editingModel ? '编辑AI模型' : '添加AI模型'}
                </Dialog.Title>
                <Dialog.Close className="text-gray-400 hover:text-gray-600">
                  <X className="w-5 h-5" />
                </Dialog.Close>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    模型名称
                  </label>
                  <input
                    type="text"
                    value={modelForm.name}
                    onChange={(e) => setModelForm({ ...modelForm, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="例如：GPT-4"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    提供商
                  </label>
                  <select
                    value={modelForm.provider}
                    onChange={(e) => setModelForm({ ...modelForm, provider: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                  >
                    <option value="" className="text-black">选择提供商</option>
                    <option value="OpenAI" className="text-black">OpenAI</option>
                    <option value="Anthropic" className="text-black">Anthropic</option>
                    <option value="Google" className="text-black">Google</option>
                    <option value="Custom" className="text-black">自定义</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    API密钥
                  </label>
                  <input
                    type="password"
                    value={modelForm.apiKey}
                    onChange={(e) => setModelForm({ ...modelForm, apiKey: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="输入您的API密钥"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    端点地址（可选）
                  </label>
                  <input
                    type="url"
                    value={modelForm.endpoint}
                    onChange={(e) => setModelForm({ ...modelForm, endpoint: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="https://api.example.com/v1"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      最大令牌数
                    </label>
                    <input
                      type="number"
                      value={modelForm.maxTokens}
                      onChange={(e) => setModelForm({ ...modelForm, maxTokens: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      min="1"
                      max="32000"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      温度
                    </label>
                    <input
                      type="number"
                      value={modelForm.temperature}
                      onChange={(e) => setModelForm({ ...modelForm, temperature: parseFloat(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      min="0"
                      max="2"
                      step="0.1"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <Dialog.Close className="px-4 py-2 text-gray-600 hover:text-gray-700 border border-gray-300 rounded-lg transition-colors">
                  取消
                </Dialog.Close>
                <button
                  onClick={handleSaveModel}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  {editingModel ? '更新' : '添加'}模型
                </button>
              </div>
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>
      </div>
    </div>
  )
}
