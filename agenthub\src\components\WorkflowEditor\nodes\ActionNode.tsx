'use client'

import React from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Zap } from 'lucide-react'

interface ActionNodeData {
  label: string
}

export function ActionNode({ data }: NodeProps<ActionNodeData>) {
  return (
    <div className="bg-white border-2 border-purple-300 rounded-lg shadow-md min-w-[140px]">
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      
      <div className="p-3">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
            <Zap className="w-3 h-3 text-purple-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 text-sm">{data.label}</h3>
            <span className="text-xs text-purple-600 bg-purple-50 px-2 py-0.5 rounded">
              Action
            </span>
          </div>
        </div>
      </div>
      
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </div>
  )
}
