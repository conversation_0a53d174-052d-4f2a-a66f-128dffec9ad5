import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { Language } from '@/lib/i18n'

export interface WorkflowNode {
  id: string
  type: 'ai-agent' | 'input' | 'output' | 'condition' | 'action'
  position: { x: number; y: number }
  data: {
    label: string
    description?: string
    config?: Record<string, any>
  }
}

export interface WorkflowEdge {
  id: string
  source: string
  target: string
  type?: string
}

export interface Workflow {
  id: string
  name: string
  description: string
  nodes: WorkflowNode[]
  edges: WorkflowEdge[]
  createdAt: Date
  updatedAt: Date
}

export interface Agent {
  id: string
  name: string
  description: string
  type: 'autogen' | 'custom'
  config: Record<string, any>
  createdAt: Date
}

interface AppState {
  // Navigation
  currentView: 'chat' | 'workflows' | 'marketplace' | 'agents' | 'settings'
  setCurrentView: (view: AppState['currentView']) => void
  
  // Workflows
  workflows: Workflow[]
  currentWorkflow: Workflow | null
  setCurrentWorkflow: (workflow: Workflow | null) => void
  addWorkflow: (workflow: Workflow) => void
  updateWorkflow: (id: string, updates: Partial<Workflow>) => void
  deleteWorkflow: (id: string) => void
  
  // Agents
  agents: Agent[]
  addAgent: (agent: Agent) => void
  updateAgent: (id: string, updates: Partial<Agent>) => void
  deleteAgent: (id: string) => void
  
  // UI State
  sidebarCollapsed: boolean
  setSidebarCollapsed: (collapsed: boolean) => void

  // Language
  language: Language
  setLanguage: (language: Language) => void
}

export const useStore = create<AppState>()(
  persist(
    (set, get) => ({
  // Navigation
  currentView: 'workflows',
  setCurrentView: (view) => set({ currentView: view }),
  
  // Workflows
  workflows: [],
  currentWorkflow: null,
  setCurrentWorkflow: (workflow) => set({ currentWorkflow: workflow }),
  addWorkflow: (workflow) => set((state) => ({ 
    workflows: [...state.workflows, workflow] 
  })),
  updateWorkflow: (id, updates) => set((state) => ({
    workflows: state.workflows.map(w => 
      w.id === id ? { ...w, ...updates, updatedAt: new Date() } : w
    ),
    currentWorkflow: state.currentWorkflow?.id === id 
      ? { ...state.currentWorkflow, ...updates, updatedAt: new Date() }
      : state.currentWorkflow
  })),
  deleteWorkflow: (id) => set((state) => ({
    workflows: state.workflows.filter(w => w.id !== id),
    currentWorkflow: state.currentWorkflow?.id === id ? null : state.currentWorkflow
  })),
  
  // Agents
  agents: [],
  addAgent: (agent) => set((state) => ({ 
    agents: [...state.agents, agent] 
  })),
  updateAgent: (id, updates) => set((state) => ({
    agents: state.agents.map(a => 
      a.id === id ? { ...a, ...updates } : a
    )
  })),
  deleteAgent: (id) => set((state) => ({
    agents: state.agents.filter(a => a.id !== id)
  })),
  
  // UI State
  sidebarCollapsed: false,
  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),

  // Language
  language: 'en' as Language,
  setLanguage: (language) => set({ language }),
    }),
    {
      name: 'agenthub-storage',
      partialize: (state) => ({
        language: state.language,
        sidebarCollapsed: state.sidebarCollapsed,
        workflows: state.workflows,
        agents: state.agents,
      }),
    }
  )
)
