'use client'

import React, { use<PERSON><PERSON>back, useState } from 'react'
import <PERSON>act<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
} from 'reactflow'
import 'reactflow/dist/style.css'

import { AIAgentNode } from './nodes/AIAgentNode'
import { InputNode } from './nodes/InputNode'
import { OutputNode } from './nodes/OutputNode'
import { ConditionNode } from './nodes/ConditionNode'
import { ActionNode } from './nodes/ActionNode'

const nodeTypes = {
  'ai-agent': AIAgentNode,
  'input': InputNode,
  'output': OutputNode,
  'condition': ConditionNode,
  'action': ActionNode,
}

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'input',
    position: { x: 100, y: 100 },
    data: { label: 'Start' },
  },
  {
    id: '2',
    type: 'ai-agent',
    position: { x: 300, y: 100 },
    data: {
      label: 'Content Analyzer',
      description: 'AI agent that analyzes content using AutoGen with Context7',
      agentType: 'autogen',
      config: {
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 1000,
        context7Libraries: ['/microsoft/autogen', '/openai/openai-node'],
        systemMessage: 'You are a content analysis expert with access to up-to-date documentation.'
      }
    },
  },
  {
    id: '3',
    type: 'condition',
    position: { x: 500, y: 100 },
    data: { label: 'Quality Check' },
  },
  {
    id: '4',
    type: 'action',
    position: { x: 700, y: 50 },
    data: { label: 'Approve Content' },
  },
  {
    id: '5',
    type: 'ai-agent',
    position: { x: 700, y: 150 },
    data: {
      label: 'Content Improver',
      description: 'AI agent that improves content quality with Context7 docs',
      agentType: 'autogen',
      config: {
        model: 'gpt-4',
        temperature: 0.5,
        maxTokens: 1500,
        context7Libraries: ['/vercel/next.js', '/tailwindlabs/tailwindcss'],
        systemMessage: 'You are a content improvement specialist with access to latest web development practices.'
      }
    },
  },
  {
    id: '6',
    type: 'output',
    position: { x: 900, y: 100 },
    data: { label: 'Final Output' },
  },
]

const initialEdges: Edge[] = [
  { id: 'e1-2', source: '1', target: '2' },
  { id: 'e2-3', source: '2', target: '3' },
  { id: 'e3-4', source: '3', target: '4', label: 'High Quality' },
  { id: 'e3-5', source: '3', target: '5', label: 'Needs Improvement' },
  { id: 'e4-6', source: '4', target: '6' },
  { id: 'e5-6', source: '5', target: '6' },
]

export function WorkflowEditor() {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  return (
    <div className="h-full w-full">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        className="bg-gray-50"
      >
        <Controls />
        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
      </ReactFlow>
    </div>
  )
}
