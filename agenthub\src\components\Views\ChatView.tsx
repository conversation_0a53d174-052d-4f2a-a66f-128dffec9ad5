'use client'

import React, { useState, useRef } from 'react'
import { Send, Bot, User, Paperclip, Globe, X, FileText, Loader2, Trash2 } from 'lucide-react'
import { useTranslation } from '@/hooks/useTranslation'
import { useStore } from '@/store/useStore'
import { vectorService } from '@/services/vectorService'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  agentId?: string
  attachments?: Attachment[]
  webSearchUsed?: boolean
}

interface Attachment {
  id: string
  name: string
  type: 'text' | 'document'
  content: string
  size: number
  vectorized?: boolean
  vectorData?: number[]
}

interface ChatSettings {
  selectedAgentId: string | null
  webSearchEnabled: boolean
}

export function ChatView() {
  const t = useTranslation()
  const { agents } = useStore()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: 'Hello! I\'m your AI assistant. I can help you create workflows, manage agents, and answer questions about your automation platform. How can I assist you today?',
      timestamp: new Date(),
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [chatSettings, setChatSettings] = useState<ChatSettings>({
    selectedAgentId: null,
    webSearchEnabled: false
  })
  const [attachments, setAttachments] = useState<Attachment[]>([])
  const [isVectorizing, setIsVectorizing] = useState(false)

  // 向量化处理
  const vectorizeText = async (text: string, filename: string): Promise<number[]> => {
    try {
      return await vectorService.vectorizeText(text, { filename, timestamp: new Date() })
    } catch (error) {
      console.error('向量化失败:', error)
      throw error
    }
  }

  // 处理文件上传
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    setIsVectorizing(true)

    for (const file of Array.from(files)) {
      // 只处理文本类文件
      if (file.type.startsWith('text/') || file.name.endsWith('.txt') || file.name.endsWith('.md') || file.name.endsWith('.json')) {
        try {
          const content = await file.text()

          // 如果文件较大，进行分块处理
          if (content.length > 2000) {
            const result = await vectorService.processDocument(content, file.name, 1000)

            // 为每个块创建附件
            result.chunks.forEach((chunk, index) => {
              const attachment: Attachment = {
                id: `attachment-${Date.now()}-${Math.random()}-${index}`,
                name: `${file.name} (块 ${index + 1}/${result.chunks.length})`,
                type: 'document',
                content: chunk,
                size: chunk.length,
                vectorized: true,
                vectorData: result.vectors[index]
              }
              setAttachments(prev => [...prev, attachment])
            })
          } else {
            // 小文件直接处理
            const vectorData = await vectorizeText(content, file.name)

            const attachment: Attachment = {
              id: `attachment-${Date.now()}-${Math.random()}`,
              name: file.name,
              type: file.type.startsWith('text/') ? 'text' : 'document',
              content,
              size: file.size,
              vectorized: true,
              vectorData
            }

            setAttachments(prev => [...prev, attachment])
          }
        } catch (error) {
          console.error('Error processing file:', error)
          alert(`处理文件 ${file.name} 时出错: ${error instanceof Error ? error.message : '未知错误'}`)
        }
      } else {
        alert(`文件 ${file.name} 不是支持的文本格式。支持的格式：.txt, .md, .json, text/*`)
      }
    }

    setIsVectorizing(false)
    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 移除附件
  const removeAttachment = (attachmentId: string) => {
    setAttachments(prev => prev.filter(att => att.id !== attachmentId))
  }

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const selectedAgent = chatSettings.selectedAgentId
      ? agents.find(a => a.id === chatSettings.selectedAgentId)
      : null

    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
      agentId: chatSettings.selectedAgentId || undefined,
      attachments: attachments.length > 0 ? [...attachments] : undefined,
      webSearchUsed: chatSettings.webSearchEnabled
    }

    setMessages(prev => [...prev, newMessage])
    setInputValue('')
    setAttachments([]) // 清空附件

    // 模拟AI响应
    setTimeout(() => {
      let responseContent = 'I understand you want to work with workflows. Let me help you with that.'

      // 根据选择的Agent调整响应
      if (selectedAgent) {
        responseContent = `[${selectedAgent.name}] ${responseContent}`
      }

      // 如果有附件，提及附件处理
      if (newMessage.attachments && newMessage.attachments.length > 0) {
        responseContent += ` I've processed your ${newMessage.attachments.length} attachment(s) and vectorized the content for better understanding.`
      }

      // 如果启用了网络搜索，提及搜索
      if (chatSettings.webSearchEnabled) {
        responseContent += ' I\'ve also searched the web for the latest information to provide you with up-to-date answers.'
      }

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: responseContent,
        timestamp: new Date(),
        agentId: chatSettings.selectedAgentId || undefined,
        webSearchUsed: chatSettings.webSearchEnabled
      }
      setMessages(prev => [...prev, aiResponse])
    }, 1500)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const clearChat = () => {
    if (window.confirm('确定要清空所有聊天记录吗？此操作无法撤销。')) {
      setMessages([{
        id: '1',
        type: 'assistant',
        content: 'Hello! I\'m your AI assistant. I can help you create workflows, manage agents, and answer questions about your automation platform. How can I assist you today?',
        timestamp: new Date(),
      }])
      setAttachments([])
    }
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('chat.title')}</h1>
            <p className="text-gray-600 mt-1">
              Chat with your AI assistant to get help with workflows and automation
            </p>
          </div>
          <button
            onClick={clearChat}
            className="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
            title="清空聊天记录"
          >
            <Trash2 className="w-4 h-4" />
            <span className="text-sm">清空</span>
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex items-start space-x-3 ${
              message.type === 'user' ? 'justify-end' : 'justify-start'
            }`}
          >
            {message.type === 'assistant' && (
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                <Bot className="w-4 h-4 text-blue-600" />
              </div>
            )}
            
            <div
              className={`max-w-2xl rounded-lg p-4 ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white border border-gray-200'
              }`}
            >
              {/* Agent Info */}
              {message.agentId && (
                <div className={`text-xs mb-2 flex items-center space-x-1 ${
                  message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  <Bot className="w-3 h-3" />
                  <span>
                    {agents.find(a => a.id === message.agentId)?.name || 'Unknown Agent'}
                  </span>
                </div>
              )}

              {/* Message Content */}
              <p className={`text-sm ${message.type === 'user' ? 'text-white' : 'text-black'}`}>
                {message.content}
              </p>

              {/* Attachments */}
              {message.attachments && message.attachments.length > 0 && (
                <div className={`mt-2 space-y-1 ${
                  message.type === 'user' ? 'text-blue-100' : 'text-gray-600'
                }`}>
                  {message.attachments.map(attachment => (
                    <div key={attachment.id} className="flex items-center space-x-1 text-xs">
                      <FileText className="w-3 h-3" />
                      <span>{attachment.name}</span>
                      {attachment.vectorized && (
                        <span className={`text-xs ${
                          message.type === 'user' ? 'text-blue-200' : 'text-green-600'
                        }`}>
                          ✓ 已向量化
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}

              {/* Web Search Indicator */}
              {message.webSearchUsed && (
                <div className={`mt-2 flex items-center space-x-1 text-xs ${
                  message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  <Globe className="w-3 h-3" />
                  <span>已使用网络搜索</span>
                </div>
              )}

              {/* Timestamp */}
              <p
                className={`text-xs mt-2 ${
                  message.type === 'user' ? 'text-blue-100' : 'text-gray-500'
                }`}
              >
                {new Date(message.timestamp).toLocaleTimeString()}
              </p>
            </div>
            
            {message.type === 'user' && (
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0">
                <User className="w-4 h-4 text-gray-600" />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Chat Controls */}
      <div className="bg-gray-50 border-t border-gray-200 p-3">
        <div className="flex flex-wrap items-center gap-4">
          {/* Agent Selection */}
          <div className="flex items-center space-x-2">
            <Bot className="w-4 h-4 text-gray-600" />
            <select
              value={chatSettings.selectedAgentId || ''}
              onChange={(e) => setChatSettings(prev => ({
                ...prev,
                selectedAgentId: e.target.value || null
              }))}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
            >
              <option value="">选择 Agent（可选）</option>
              {agents.map(agent => (
                <option key={agent.id} value={agent.id}>
                  {agent.name} ({agent.type})
                </option>
              ))}
            </select>
          </div>

          {/* File Upload */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isVectorizing}
              className="flex items-center space-x-1 px-3 py-1 border border-gray-300 rounded-lg text-sm hover:bg-gray-100 transition-colors disabled:opacity-50 text-gray-700"
            >
              {isVectorizing ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Paperclip className="w-4 h-4" />
              )}
              <span>{isVectorizing ? '向量化中...' : '上传文档'}</span>
            </button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".txt,.md,.json,text/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>

          {/* Web Search Toggle */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setChatSettings(prev => ({
                ...prev,
                webSearchEnabled: !prev.webSearchEnabled
              }))}
              className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm transition-colors ${
                chatSettings.webSearchEnabled
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'border border-gray-300 text-gray-700 hover:bg-gray-100'
              }`}
            >
              <Globe className="w-4 h-4" />
              <span>联网搜索</span>
            </button>
          </div>
        </div>

        {/* Attachments Display */}
        {attachments.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2">
            {attachments.map(attachment => (
              <div
                key={attachment.id}
                className="flex items-center space-x-2 bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm"
              >
                <FileText className="w-4 h-4 text-blue-600" />
                <span className="text-gray-700">{attachment.name}</span>
                <span className="text-xs text-gray-500">
                  ({Math.round(attachment.size / 1024)}KB)
                </span>
                {attachment.vectorized && (
                  <span className="text-xs text-green-600">✓ 已向量化</span>
                )}
                <button
                  onClick={() => removeAttachment(attachment.id)}
                  className="text-gray-400 hover:text-red-600 transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Input */}
      <div className="bg-white p-4">
        <div className="flex items-end space-x-4">
          <div className="flex-1">
            <textarea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={t('chat.typeMessage')}
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
              rows={3}
            />
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim()}
            className="p-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-lg transition-colors"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>

        {/* Status Bar */}
        <div className="mt-3 flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            {chatSettings.selectedAgentId && (
              <div className="flex items-center space-x-1">
                <Bot className="w-3 h-3" />
                <span>
                  使用 Agent: {agents.find(a => a.id === chatSettings.selectedAgentId)?.name}
                </span>
              </div>
            )}
            {attachments.length > 0 && (
              <div className="flex items-center space-x-1">
                <FileText className="w-3 h-3" />
                <span>{attachments.length} 个附件已准备</span>
              </div>
            )}
            {chatSettings.webSearchEnabled && (
              <div className="flex items-center space-x-1">
                <Globe className="w-3 h-3" />
                <span>联网搜索已启用</span>
              </div>
            )}
          </div>
          <div className="text-gray-400">
            按 Enter 发送，Shift+Enter 换行
          </div>
        </div>
      </div>
    </div>
  )
}
