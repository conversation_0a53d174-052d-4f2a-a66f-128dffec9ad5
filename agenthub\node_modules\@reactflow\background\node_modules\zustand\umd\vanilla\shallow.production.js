!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).zustandVanillaShallow={})}(this,(function(e){"use strict";function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function r(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,r){if(e){if("string"==typeof e)return t(e,r);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}(e))||r){n&&(e=n);var o=0;return function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}e.shallow=function(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(var n,o=r(e);!(n=o()).done;){var i=n.value,a=i[0],f=i[1];if(!Object.is(f,t.get(a)))return!1}return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(var l,u=r(e);!(l=u()).done;){var s=l.value;if(!t.has(s))return!1}return!0}var c=Object.keys(e);if(c.length!==Object.keys(t).length)return!1;for(var d=0,y=c;d<y.length;d++){var b=y[d];if(!Object.prototype.hasOwnProperty.call(t,b)||!Object.is(e[b],t[b]))return!1}return!0}}));
