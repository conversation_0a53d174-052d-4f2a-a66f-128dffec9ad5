module.exports = {

"[project]/node_modules/@reactflow/core/dist/esm/index.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseEdge": (()=>BaseEdge),
    "BezierEdge": (()=>BezierEdge),
    "ConnectionLineType": (()=>ConnectionLineType),
    "ConnectionMode": (()=>ConnectionMode),
    "EdgeLabelRenderer": (()=>EdgeLabelRenderer),
    "EdgeText": (()=>EdgeText$1),
    "Handle": (()=>Handle$1),
    "MarkerType": (()=>MarkerType),
    "PanOnScrollMode": (()=>PanOnScrollMode),
    "Panel": (()=>Panel),
    "Position": (()=>Position),
    "ReactFlow": (()=>ReactFlow),
    "ReactFlowProvider": (()=>ReactFlowProvider),
    "SelectionMode": (()=>SelectionMode),
    "SimpleBezierEdge": (()=>SimpleBezierEdge),
    "SmoothStepEdge": (()=>SmoothStepEdge),
    "StepEdge": (()=>StepEdge),
    "StraightEdge": (()=>StraightEdge),
    "addEdge": (()=>addEdge),
    "applyEdgeChanges": (()=>applyEdgeChanges),
    "applyNodeChanges": (()=>applyNodeChanges),
    "boxToRect": (()=>boxToRect),
    "clamp": (()=>clamp),
    "getBezierPath": (()=>getBezierPath),
    "getBoundsOfRects": (()=>getBoundsOfRects),
    "getConnectedEdges": (()=>getConnectedEdges),
    "getIncomers": (()=>getIncomers),
    "getMarkerEnd": (()=>getMarkerEnd),
    "getNodePositionWithOrigin": (()=>getNodePositionWithOrigin),
    "getNodesBounds": (()=>getNodesBounds),
    "getOutgoers": (()=>getOutgoers),
    "getRectOfNodes": (()=>getRectOfNodes),
    "getSimpleBezierPath": (()=>getSimpleBezierPath),
    "getSmoothStepPath": (()=>getSmoothStepPath),
    "getStraightPath": (()=>getStraightPath),
    "getTransformForBounds": (()=>getTransformForBounds),
    "getViewportForBounds": (()=>getViewportForBounds),
    "handleParentExpand": (()=>handleParentExpand),
    "internalsSymbol": (()=>internalsSymbol),
    "isEdge": (()=>isEdge),
    "isNode": (()=>isNode),
    "reconnectEdge": (()=>reconnectEdge),
    "rectToBox": (()=>rectToBox),
    "updateEdge": (()=>updateEdge),
    "useEdges": (()=>useEdges),
    "useEdgesState": (()=>useEdgesState),
    "useGetPointerPosition": (()=>useGetPointerPosition),
    "useKeyPress": (()=>useKeyPress),
    "useNodeId": (()=>useNodeId),
    "useNodes": (()=>useNodes),
    "useNodesInitialized": (()=>useNodesInitialized),
    "useNodesState": (()=>useNodesState),
    "useOnSelectionChange": (()=>useOnSelectionChange),
    "useOnViewportChange": (()=>useOnViewportChange),
    "useReactFlow": (()=>useReactFlow),
    "useStore": (()=>useStore),
    "useStoreApi": (()=>useStoreApi),
    "useUpdateNodeInternals": (()=>useUpdateNodeInternals),
    "useViewport": (()=>useViewport)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classcat/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$traditional$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reactflow/core/node_modules/zustand/esm/traditional.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reactflow/core/node_modules/zustand/esm/shallow.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-zoom/src/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__identity__as__zoomIdentity$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-zoom/src/transform.js [app-ssr] (ecmascript) <export identity as zoomIdentity>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$zoom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__zoom$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-zoom/src/zoom.js [app-ssr] (ecmascript) <export default as zoom>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/select.js [app-ssr] (ecmascript) <export default as select>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__pointer$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-selection/src/pointer.js [app-ssr] (ecmascript) <export default as pointer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$drag$2f$src$2f$drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__drag$3e$__ = __turbopack_context__.i("[project]/node_modules/d3-drag/src/drag.js [app-ssr] (ecmascript) <export default as drag>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
const StoreContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
const Provider$1 = StoreContext.Provider;
const errorMessages = {
    error001: ()=>'[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001',
    error002: ()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",
    error003: (nodeType)=>`Node type "${nodeType}" not found. Using fallback type "default".`,
    error004: ()=>'The React Flow parent container needs a width and a height to render the graph.',
    error005: ()=>'Only child nodes can use a parent extent.',
    error006: ()=>"Can't create edge. An edge needs a source and a target.",
    error007: (id)=>`The old edge with id=${id} does not exist.`,
    error009: (type)=>`Marker type "${type}" doesn't exist.`,
    error008: (sourceHandle, edge)=>`Couldn't create edge for ${!sourceHandle ? 'source' : 'target'} handle id: "${!sourceHandle ? edge.sourceHandle : edge.targetHandle}", edge id: ${edge.id}.`,
    error010: ()=>'Handle: No node id found. Make sure to only use a Handle inside a custom Node.',
    error011: (edgeType)=>`Edge type "${edgeType}" not found. Using fallback type "default".`,
    error012: (id)=>`Node with id "${id}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`
};
const zustandErrorMessage = errorMessages['error001']();
function useStore(selector, equalityFn) {
    const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    if (store === null) {
        throw new Error(zustandErrorMessage);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$traditional$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useStoreWithEqualityFn"])(store, selector, equalityFn);
}
const useStoreApi = ()=>{
    const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    if (store === null) {
        throw new Error(zustandErrorMessage);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
            getState: store.getState,
            setState: store.setState,
            subscribe: store.subscribe,
            destroy: store.destroy
        }), [
        store
    ]);
};
const selector$g = (s)=>s.userSelectionActive ? 'none' : 'all';
function Panel({ position, children, className, style, ...rest }) {
    const pointerEvents = useStore(selector$g);
    const positionClasses = `${position}`.split('-');
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
            'react-flow__panel',
            className,
            ...positionClasses
        ]),
        style: {
            ...style,
            pointerEvents
        },
        ...rest
    }, children);
}
function Attribution({ proOptions, position = 'bottom-right' }) {
    if (proOptions?.hideAttribution) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Panel, {
        position: position,
        className: "react-flow__attribution",
        "data-message": "Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("a", {
        href: "https://reactflow.dev",
        target: "_blank",
        rel: "noopener noreferrer",
        "aria-label": "React Flow attribution"
    }, "React Flow"));
}
const EdgeText = ({ x, y, label, labelStyle = {}, labelShowBg = true, labelBgStyle = {}, labelBgPadding = [
    2,
    4
], labelBgBorderRadius = 2, children, className, ...rest })=>{
    const edgeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [edgeTextBbox, setEdgeTextBbox] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        x: 0,
        y: 0,
        width: 0,
        height: 0
    });
    const edgeTextClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
        'react-flow__edge-textwrapper',
        className
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (edgeRef.current) {
            const textBbox = edgeRef.current.getBBox();
            setEdgeTextBbox({
                x: textBbox.x,
                y: textBbox.y,
                width: textBbox.width,
                height: textBbox.height
            });
        }
    }, [
        label
    ]);
    if (typeof label === 'undefined' || !label) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("g", {
        transform: `translate(${x - edgeTextBbox.width / 2} ${y - edgeTextBbox.height / 2})`,
        className: edgeTextClasses,
        visibility: edgeTextBbox.width ? 'visible' : 'hidden',
        ...rest
    }, labelShowBg && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("rect", {
        width: edgeTextBbox.width + 2 * labelBgPadding[0],
        x: -labelBgPadding[0],
        y: -labelBgPadding[1],
        height: edgeTextBbox.height + 2 * labelBgPadding[1],
        className: "react-flow__edge-textbg",
        style: labelBgStyle,
        rx: labelBgBorderRadius,
        ry: labelBgBorderRadius
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("text", {
        className: "react-flow__edge-text",
        y: edgeTextBbox.height / 2,
        dy: "0.3em",
        ref: edgeRef,
        style: labelStyle
    }, label), children);
};
var EdgeText$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(EdgeText);
const getDimensions = (node)=>({
        width: node.offsetWidth,
        height: node.offsetHeight
    });
const clamp = (val, min = 0, max = 1)=>Math.min(Math.max(val, min), max);
const clampPosition = (position = {
    x: 0,
    y: 0
}, extent)=>({
        x: clamp(position.x, extent[0][0], extent[1][0]),
        y: clamp(position.y, extent[0][1], extent[1][1])
    });
// returns a number between 0 and 1 that represents the velocity of the movement
// when the mouse is close to the edge of the canvas
const calcAutoPanVelocity = (value, min, max)=>{
    if (value < min) {
        return clamp(Math.abs(value - min), 1, 50) / 50;
    } else if (value > max) {
        return -clamp(Math.abs(value - max), 1, 50) / 50;
    }
    return 0;
};
const calcAutoPan = (pos, bounds)=>{
    const xMovement = calcAutoPanVelocity(pos.x, 35, bounds.width - 35) * 20;
    const yMovement = calcAutoPanVelocity(pos.y, 35, bounds.height - 35) * 20;
    return [
        xMovement,
        yMovement
    ];
};
const getHostForElement = (element)=>element.getRootNode?.() || window?.document;
const getBoundsOfBoxes = (box1, box2)=>({
        x: Math.min(box1.x, box2.x),
        y: Math.min(box1.y, box2.y),
        x2: Math.max(box1.x2, box2.x2),
        y2: Math.max(box1.y2, box2.y2)
    });
const rectToBox = ({ x, y, width, height })=>({
        x,
        y,
        x2: x + width,
        y2: y + height
    });
const boxToRect = ({ x, y, x2, y2 })=>({
        x,
        y,
        width: x2 - x,
        height: y2 - y
    });
const nodeToRect = (node)=>({
        ...node.positionAbsolute || {
            x: 0,
            y: 0
        },
        width: node.width || 0,
        height: node.height || 0
    });
const getBoundsOfRects = (rect1, rect2)=>boxToRect(getBoundsOfBoxes(rectToBox(rect1), rectToBox(rect2)));
const getOverlappingArea = (rectA, rectB)=>{
    const xOverlap = Math.max(0, Math.min(rectA.x + rectA.width, rectB.x + rectB.width) - Math.max(rectA.x, rectB.x));
    const yOverlap = Math.max(0, Math.min(rectA.y + rectA.height, rectB.y + rectB.height) - Math.max(rectA.y, rectB.y));
    return Math.ceil(xOverlap * yOverlap);
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const isRectObject = (obj)=>isNumeric(obj.width) && isNumeric(obj.height) && isNumeric(obj.x) && isNumeric(obj.y);
/* eslint-disable-next-line @typescript-eslint/no-explicit-any */ const isNumeric = (n)=>!isNaN(n) && isFinite(n);
const internalsSymbol = Symbol.for('internals');
// used for a11y key board controls for nodes and edges
const elementSelectionKeys = [
    'Enter',
    ' ',
    'Escape'
];
const devWarn = (id, message)=>{
    if ("TURBOPACK compile-time truthy", 1) {
        console.warn(`[React Flow]: ${message} Help: https://reactflow.dev/error#${id}`);
    }
};
const isReactKeyboardEvent = (event)=>'nativeEvent' in event;
function isInputDOMNode(event) {
    const kbEvent = isReactKeyboardEvent(event) ? event.nativeEvent : event;
    // using composed path for handling shadow dom
    const target = kbEvent.composedPath?.()?.[0] || event.target;
    const isInput = [
        'INPUT',
        'SELECT',
        'TEXTAREA'
    ].includes(target?.nodeName) || target?.hasAttribute('contenteditable');
    // when an input field is focused we don't want to trigger deletion or movement of nodes
    return isInput || !!target?.closest('.nokey');
}
const isMouseEvent = (event)=>'clientX' in event;
const getEventPosition = (event, bounds)=>{
    const isMouseTriggered = isMouseEvent(event);
    const evtX = isMouseTriggered ? event.clientX : event.touches?.[0].clientX;
    const evtY = isMouseTriggered ? event.clientY : event.touches?.[0].clientY;
    return {
        x: evtX - (bounds?.left ?? 0),
        y: evtY - (bounds?.top ?? 0)
    };
};
const isMacOs = ()=>typeof navigator !== 'undefined' && navigator?.userAgent?.indexOf('Mac') >= 0;
const BaseEdge = ({ id, path, labelX, labelY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth = 20 })=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        id: id,
        style: style,
        d: path,
        fill: "none",
        className: "react-flow__edge-path",
        markerEnd: markerEnd,
        markerStart: markerStart
    }), interactionWidth && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: path,
        fill: "none",
        strokeOpacity: 0,
        strokeWidth: interactionWidth,
        className: "react-flow__edge-interaction"
    }), label && isNumeric(labelX) && isNumeric(labelY) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EdgeText$1, {
        x: labelX,
        y: labelY,
        label: label,
        labelStyle: labelStyle,
        labelShowBg: labelShowBg,
        labelBgStyle: labelBgStyle,
        labelBgPadding: labelBgPadding,
        labelBgBorderRadius: labelBgBorderRadius
    }) : null);
};
BaseEdge.displayName = 'BaseEdge';
const getMarkerEnd = (markerType, markerEndId)=>{
    if (typeof markerEndId !== 'undefined' && markerEndId) {
        return `url(#${markerEndId})`;
    }
    return typeof markerType !== 'undefined' ? `url(#react-flow__${markerType})` : 'none';
};
function getMouseHandler$1(id, getState, handler) {
    return handler === undefined ? handler : (event)=>{
        const edge = getState().edges.find((e)=>e.id === id);
        if (edge) {
            handler(event, {
                ...edge
            });
        }
    };
}
// this is used for straight edges and simple smoothstep edges (LTR, RTL, BTT, TTB)
function getEdgeCenter({ sourceX, sourceY, targetX, targetY }) {
    const xOffset = Math.abs(targetX - sourceX) / 2;
    const centerX = targetX < sourceX ? targetX + xOffset : targetX - xOffset;
    const yOffset = Math.abs(targetY - sourceY) / 2;
    const centerY = targetY < sourceY ? targetY + yOffset : targetY - yOffset;
    return [
        centerX,
        centerY,
        xOffset,
        yOffset
    ];
}
function getBezierEdgeCenter({ sourceX, sourceY, targetX, targetY, sourceControlX, sourceControlY, targetControlX, targetControlY }) {
    // cubic bezier t=0.5 mid point, not the actual mid point, but easy to calculate
    // https://stackoverflow.com/questions/67516101/how-to-find-distance-mid-point-of-bezier-curve
    const centerX = sourceX * 0.125 + sourceControlX * 0.375 + targetControlX * 0.375 + targetX * 0.125;
    const centerY = sourceY * 0.125 + sourceControlY * 0.375 + targetControlY * 0.375 + targetY * 0.125;
    const offsetX = Math.abs(centerX - sourceX);
    const offsetY = Math.abs(centerY - sourceY);
    return [
        centerX,
        centerY,
        offsetX,
        offsetY
    ];
}
var ConnectionMode;
(function(ConnectionMode) {
    ConnectionMode["Strict"] = "strict";
    ConnectionMode["Loose"] = "loose";
})(ConnectionMode || (ConnectionMode = {}));
var PanOnScrollMode;
(function(PanOnScrollMode) {
    PanOnScrollMode["Free"] = "free";
    PanOnScrollMode["Vertical"] = "vertical";
    PanOnScrollMode["Horizontal"] = "horizontal";
})(PanOnScrollMode || (PanOnScrollMode = {}));
var SelectionMode;
(function(SelectionMode) {
    SelectionMode["Partial"] = "partial";
    SelectionMode["Full"] = "full";
})(SelectionMode || (SelectionMode = {}));
var ConnectionLineType;
(function(ConnectionLineType) {
    ConnectionLineType["Bezier"] = "default";
    ConnectionLineType["Straight"] = "straight";
    ConnectionLineType["Step"] = "step";
    ConnectionLineType["SmoothStep"] = "smoothstep";
    ConnectionLineType["SimpleBezier"] = "simplebezier";
})(ConnectionLineType || (ConnectionLineType = {}));
var MarkerType;
(function(MarkerType) {
    MarkerType["Arrow"] = "arrow";
    MarkerType["ArrowClosed"] = "arrowclosed";
})(MarkerType || (MarkerType = {}));
var Position;
(function(Position) {
    Position["Left"] = "left";
    Position["Top"] = "top";
    Position["Right"] = "right";
    Position["Bottom"] = "bottom";
})(Position || (Position = {}));
function getControl({ pos, x1, y1, x2, y2 }) {
    if (pos === Position.Left || pos === Position.Right) {
        return [
            0.5 * (x1 + x2),
            y1
        ];
    }
    return [
        x1,
        0.5 * (y1 + y2)
    ];
}
function getSimpleBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top }) {
    const [sourceControlX, sourceControlY] = getControl({
        pos: sourcePosition,
        x1: sourceX,
        y1: sourceY,
        x2: targetX,
        y2: targetY
    });
    const [targetControlX, targetControlY] = getControl({
        pos: targetPosition,
        x1: targetX,
        y1: targetY,
        x2: sourceX,
        y2: sourceY
    });
    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({
        sourceX,
        sourceY,
        targetX,
        targetY,
        sourceControlX,
        sourceControlY,
        targetControlX,
        targetControlY
    });
    return [
        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,
        labelX,
        labelY,
        offsetX,
        offsetY
    ];
}
const SimpleBezierEdge = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(({ sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth })=>{
    const [path, labelX, labelY] = getSimpleBezierPath({
        sourceX,
        sourceY,
        sourcePosition,
        targetX,
        targetY,
        targetPosition
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(BaseEdge, {
        path: path,
        labelX: labelX,
        labelY: labelY,
        label: label,
        labelStyle: labelStyle,
        labelShowBg: labelShowBg,
        labelBgStyle: labelBgStyle,
        labelBgPadding: labelBgPadding,
        labelBgBorderRadius: labelBgBorderRadius,
        style: style,
        markerEnd: markerEnd,
        markerStart: markerStart,
        interactionWidth: interactionWidth
    });
});
SimpleBezierEdge.displayName = 'SimpleBezierEdge';
const handleDirections = {
    [Position.Left]: {
        x: -1,
        y: 0
    },
    [Position.Right]: {
        x: 1,
        y: 0
    },
    [Position.Top]: {
        x: 0,
        y: -1
    },
    [Position.Bottom]: {
        x: 0,
        y: 1
    }
};
const getDirection = ({ source, sourcePosition = Position.Bottom, target })=>{
    if (sourcePosition === Position.Left || sourcePosition === Position.Right) {
        return source.x < target.x ? {
            x: 1,
            y: 0
        } : {
            x: -1,
            y: 0
        };
    }
    return source.y < target.y ? {
        x: 0,
        y: 1
    } : {
        x: 0,
        y: -1
    };
};
const distance = (a, b)=>Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));
// ith this function we try to mimic a orthogonal edge routing behaviour
// It's not as good as a real orthogonal edge routing but it's faster and good enough as a default for step and smooth step edges
function getPoints({ source, sourcePosition = Position.Bottom, target, targetPosition = Position.Top, center, offset }) {
    const sourceDir = handleDirections[sourcePosition];
    const targetDir = handleDirections[targetPosition];
    const sourceGapped = {
        x: source.x + sourceDir.x * offset,
        y: source.y + sourceDir.y * offset
    };
    const targetGapped = {
        x: target.x + targetDir.x * offset,
        y: target.y + targetDir.y * offset
    };
    const dir = getDirection({
        source: sourceGapped,
        sourcePosition,
        target: targetGapped
    });
    const dirAccessor = dir.x !== 0 ? 'x' : 'y';
    const currDir = dir[dirAccessor];
    let points = [];
    let centerX, centerY;
    const sourceGapOffset = {
        x: 0,
        y: 0
    };
    const targetGapOffset = {
        x: 0,
        y: 0
    };
    const [defaultCenterX, defaultCenterY, defaultOffsetX, defaultOffsetY] = getEdgeCenter({
        sourceX: source.x,
        sourceY: source.y,
        targetX: target.x,
        targetY: target.y
    });
    // opposite handle positions, default case
    if (sourceDir[dirAccessor] * targetDir[dirAccessor] === -1) {
        centerX = center.x ?? defaultCenterX;
        centerY = center.y ?? defaultCenterY;
        //    --->
        //    |
        // >---
        const verticalSplit = [
            {
                x: centerX,
                y: sourceGapped.y
            },
            {
                x: centerX,
                y: targetGapped.y
            }
        ];
        //    |
        //  ---
        //  |
        const horizontalSplit = [
            {
                x: sourceGapped.x,
                y: centerY
            },
            {
                x: targetGapped.x,
                y: centerY
            }
        ];
        if (sourceDir[dirAccessor] === currDir) {
            points = dirAccessor === 'x' ? verticalSplit : horizontalSplit;
        } else {
            points = dirAccessor === 'x' ? horizontalSplit : verticalSplit;
        }
    } else {
        // sourceTarget means we take x from source and y from target, targetSource is the opposite
        const sourceTarget = [
            {
                x: sourceGapped.x,
                y: targetGapped.y
            }
        ];
        const targetSource = [
            {
                x: targetGapped.x,
                y: sourceGapped.y
            }
        ];
        // this handles edges with same handle positions
        if (dirAccessor === 'x') {
            points = sourceDir.x === currDir ? targetSource : sourceTarget;
        } else {
            points = sourceDir.y === currDir ? sourceTarget : targetSource;
        }
        if (sourcePosition === targetPosition) {
            const diff = Math.abs(source[dirAccessor] - target[dirAccessor]);
            // if an edge goes from right to right for example (sourcePosition === targetPosition) and the distance between source.x and target.x is less than the offset, the added point and the gapped source/target will overlap. This leads to a weird edge path. To avoid this we add a gapOffset to the source/target
            if (diff <= offset) {
                const gapOffset = Math.min(offset - 1, offset - diff);
                if (sourceDir[dirAccessor] === currDir) {
                    sourceGapOffset[dirAccessor] = (sourceGapped[dirAccessor] > source[dirAccessor] ? -1 : 1) * gapOffset;
                } else {
                    targetGapOffset[dirAccessor] = (targetGapped[dirAccessor] > target[dirAccessor] ? -1 : 1) * gapOffset;
                }
            }
        }
        // these are conditions for handling mixed handle positions like Right -> Bottom for example
        if (sourcePosition !== targetPosition) {
            const dirAccessorOpposite = dirAccessor === 'x' ? 'y' : 'x';
            const isSameDir = sourceDir[dirAccessor] === targetDir[dirAccessorOpposite];
            const sourceGtTargetOppo = sourceGapped[dirAccessorOpposite] > targetGapped[dirAccessorOpposite];
            const sourceLtTargetOppo = sourceGapped[dirAccessorOpposite] < targetGapped[dirAccessorOpposite];
            const flipSourceTarget = sourceDir[dirAccessor] === 1 && (!isSameDir && sourceGtTargetOppo || isSameDir && sourceLtTargetOppo) || sourceDir[dirAccessor] !== 1 && (!isSameDir && sourceLtTargetOppo || isSameDir && sourceGtTargetOppo);
            if (flipSourceTarget) {
                points = dirAccessor === 'x' ? sourceTarget : targetSource;
            }
        }
        const sourceGapPoint = {
            x: sourceGapped.x + sourceGapOffset.x,
            y: sourceGapped.y + sourceGapOffset.y
        };
        const targetGapPoint = {
            x: targetGapped.x + targetGapOffset.x,
            y: targetGapped.y + targetGapOffset.y
        };
        const maxXDistance = Math.max(Math.abs(sourceGapPoint.x - points[0].x), Math.abs(targetGapPoint.x - points[0].x));
        const maxYDistance = Math.max(Math.abs(sourceGapPoint.y - points[0].y), Math.abs(targetGapPoint.y - points[0].y));
        // we want to place the label on the longest segment of the edge
        if (maxXDistance >= maxYDistance) {
            centerX = (sourceGapPoint.x + targetGapPoint.x) / 2;
            centerY = points[0].y;
        } else {
            centerX = points[0].x;
            centerY = (sourceGapPoint.y + targetGapPoint.y) / 2;
        }
    }
    const pathPoints = [
        source,
        {
            x: sourceGapped.x + sourceGapOffset.x,
            y: sourceGapped.y + sourceGapOffset.y
        },
        ...points,
        {
            x: targetGapped.x + targetGapOffset.x,
            y: targetGapped.y + targetGapOffset.y
        },
        target
    ];
    return [
        pathPoints,
        centerX,
        centerY,
        defaultOffsetX,
        defaultOffsetY
    ];
}
function getBend(a, b, c, size) {
    const bendSize = Math.min(distance(a, b) / 2, distance(b, c) / 2, size);
    const { x, y } = b;
    // no bend
    if (a.x === x && x === c.x || a.y === y && y === c.y) {
        return `L${x} ${y}`;
    }
    // first segment is horizontal
    if (a.y === y) {
        const xDir = a.x < c.x ? -1 : 1;
        const yDir = a.y < c.y ? 1 : -1;
        return `L ${x + bendSize * xDir},${y}Q ${x},${y} ${x},${y + bendSize * yDir}`;
    }
    const xDir = a.x < c.x ? 1 : -1;
    const yDir = a.y < c.y ? -1 : 1;
    return `L ${x},${y + bendSize * yDir}Q ${x},${y} ${x + bendSize * xDir},${y}`;
}
function getSmoothStepPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, borderRadius = 5, centerX, centerY, offset = 20 }) {
    const [points, labelX, labelY, offsetX, offsetY] = getPoints({
        source: {
            x: sourceX,
            y: sourceY
        },
        sourcePosition,
        target: {
            x: targetX,
            y: targetY
        },
        targetPosition,
        center: {
            x: centerX,
            y: centerY
        },
        offset
    });
    const path = points.reduce((res, p, i)=>{
        let segment = '';
        if (i > 0 && i < points.length - 1) {
            segment = getBend(points[i - 1], p, points[i + 1], borderRadius);
        } else {
            segment = `${i === 0 ? 'M' : 'L'}${p.x} ${p.y}`;
        }
        res += segment;
        return res;
    }, '');
    return [
        path,
        labelX,
        labelY,
        offsetX,
        offsetY
    ];
}
const SmoothStepEdge = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(({ sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, sourcePosition = Position.Bottom, targetPosition = Position.Top, markerEnd, markerStart, pathOptions, interactionWidth })=>{
    const [path, labelX, labelY] = getSmoothStepPath({
        sourceX,
        sourceY,
        sourcePosition,
        targetX,
        targetY,
        targetPosition,
        borderRadius: pathOptions?.borderRadius,
        offset: pathOptions?.offset
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(BaseEdge, {
        path: path,
        labelX: labelX,
        labelY: labelY,
        label: label,
        labelStyle: labelStyle,
        labelShowBg: labelShowBg,
        labelBgStyle: labelBgStyle,
        labelBgPadding: labelBgPadding,
        labelBgBorderRadius: labelBgBorderRadius,
        style: style,
        markerEnd: markerEnd,
        markerStart: markerStart,
        interactionWidth: interactionWidth
    });
});
SmoothStepEdge.displayName = 'SmoothStepEdge';
const StepEdge = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])((props)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(SmoothStepEdge, {
        ...props,
        pathOptions: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>({
                borderRadius: 0,
                offset: props.pathOptions?.offset
            }), [
            props.pathOptions?.offset
        ])
    }));
StepEdge.displayName = 'StepEdge';
function getStraightPath({ sourceX, sourceY, targetX, targetY }) {
    const [labelX, labelY, offsetX, offsetY] = getEdgeCenter({
        sourceX,
        sourceY,
        targetX,
        targetY
    });
    return [
        `M ${sourceX},${sourceY}L ${targetX},${targetY}`,
        labelX,
        labelY,
        offsetX,
        offsetY
    ];
}
const StraightEdge = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(({ sourceX, sourceY, targetX, targetY, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, interactionWidth })=>{
    const [path, labelX, labelY] = getStraightPath({
        sourceX,
        sourceY,
        targetX,
        targetY
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(BaseEdge, {
        path: path,
        labelX: labelX,
        labelY: labelY,
        label: label,
        labelStyle: labelStyle,
        labelShowBg: labelShowBg,
        labelBgStyle: labelBgStyle,
        labelBgPadding: labelBgPadding,
        labelBgBorderRadius: labelBgBorderRadius,
        style: style,
        markerEnd: markerEnd,
        markerStart: markerStart,
        interactionWidth: interactionWidth
    });
});
StraightEdge.displayName = 'StraightEdge';
function calculateControlOffset(distance, curvature) {
    if (distance >= 0) {
        return 0.5 * distance;
    }
    return curvature * 25 * Math.sqrt(-distance);
}
function getControlWithCurvature({ pos, x1, y1, x2, y2, c }) {
    switch(pos){
        case Position.Left:
            return [
                x1 - calculateControlOffset(x1 - x2, c),
                y1
            ];
        case Position.Right:
            return [
                x1 + calculateControlOffset(x2 - x1, c),
                y1
            ];
        case Position.Top:
            return [
                x1,
                y1 - calculateControlOffset(y1 - y2, c)
            ];
        case Position.Bottom:
            return [
                x1,
                y1 + calculateControlOffset(y2 - y1, c)
            ];
    }
}
function getBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, curvature = 0.25 }) {
    const [sourceControlX, sourceControlY] = getControlWithCurvature({
        pos: sourcePosition,
        x1: sourceX,
        y1: sourceY,
        x2: targetX,
        y2: targetY,
        c: curvature
    });
    const [targetControlX, targetControlY] = getControlWithCurvature({
        pos: targetPosition,
        x1: targetX,
        y1: targetY,
        x2: sourceX,
        y2: sourceY,
        c: curvature
    });
    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({
        sourceX,
        sourceY,
        targetX,
        targetY,
        sourceControlX,
        sourceControlY,
        targetControlX,
        targetControlY
    });
    return [
        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,
        labelX,
        labelY,
        offsetX,
        offsetY
    ];
}
const BezierEdge = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(({ sourceX, sourceY, targetX, targetY, sourcePosition = Position.Bottom, targetPosition = Position.Top, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, markerEnd, markerStart, pathOptions, interactionWidth })=>{
    const [path, labelX, labelY] = getBezierPath({
        sourceX,
        sourceY,
        sourcePosition,
        targetX,
        targetY,
        targetPosition,
        curvature: pathOptions?.curvature
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(BaseEdge, {
        path: path,
        labelX: labelX,
        labelY: labelY,
        label: label,
        labelStyle: labelStyle,
        labelShowBg: labelShowBg,
        labelBgStyle: labelBgStyle,
        labelBgPadding: labelBgPadding,
        labelBgBorderRadius: labelBgBorderRadius,
        style: style,
        markerEnd: markerEnd,
        markerStart: markerStart,
        interactionWidth: interactionWidth
    });
});
BezierEdge.displayName = 'BezierEdge';
const NodeIdContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
const Provider = NodeIdContext.Provider;
NodeIdContext.Consumer;
const useNodeId = ()=>{
    const nodeId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(NodeIdContext);
    return nodeId;
};
const isEdge = (element)=>'id' in element && 'source' in element && 'target' in element;
const isNode = (element)=>'id' in element && !('source' in element) && !('target' in element);
const getOutgoers = (node, nodes, edges)=>{
    if (!isNode(node)) {
        return [];
    }
    const outgoerIds = edges.filter((e)=>e.source === node.id).map((e)=>e.target);
    return nodes.filter((n)=>outgoerIds.includes(n.id));
};
const getIncomers = (node, nodes, edges)=>{
    if (!isNode(node)) {
        return [];
    }
    const incomersIds = edges.filter((e)=>e.target === node.id).map((e)=>e.source);
    return nodes.filter((n)=>incomersIds.includes(n.id));
};
const getEdgeId = ({ source, sourceHandle, target, targetHandle })=>`reactflow__edge-${source}${sourceHandle || ''}-${target}${targetHandle || ''}`;
const getMarkerId = (marker, rfId)=>{
    if (typeof marker === 'undefined') {
        return '';
    }
    if (typeof marker === 'string') {
        return marker;
    }
    const idPrefix = rfId ? `${rfId}__` : '';
    return `${idPrefix}${Object.keys(marker).sort().map((key)=>`${key}=${marker[key]}`).join('&')}`;
};
const connectionExists = (edge, edges)=>{
    return edges.some((el)=>el.source === edge.source && el.target === edge.target && (el.sourceHandle === edge.sourceHandle || !el.sourceHandle && !edge.sourceHandle) && (el.targetHandle === edge.targetHandle || !el.targetHandle && !edge.targetHandle));
};
const addEdge = (edgeParams, edges)=>{
    if (!edgeParams.source || !edgeParams.target) {
        devWarn('006', errorMessages['error006']());
        return edges;
    }
    let edge;
    if (isEdge(edgeParams)) {
        edge = {
            ...edgeParams
        };
    } else {
        edge = {
            ...edgeParams,
            id: getEdgeId(edgeParams)
        };
    }
    if (connectionExists(edge, edges)) {
        return edges;
    }
    return edges.concat(edge);
};
const reconnectEdge = (oldEdge, newConnection, edges, options = {
    shouldReplaceId: true
})=>{
    const { id: oldEdgeId, ...rest } = oldEdge;
    if (!newConnection.source || !newConnection.target) {
        devWarn('006', errorMessages['error006']());
        return edges;
    }
    const foundEdge = edges.find((e)=>e.id === oldEdgeId);
    if (!foundEdge) {
        devWarn('007', errorMessages['error007'](oldEdgeId));
        return edges;
    }
    // Remove old edge and create the new edge with parameters of old edge.
    const edge = {
        ...rest,
        id: options.shouldReplaceId ? getEdgeId(newConnection) : oldEdgeId,
        source: newConnection.source,
        target: newConnection.target,
        sourceHandle: newConnection.sourceHandle,
        targetHandle: newConnection.targetHandle
    };
    return edges.filter((e)=>e.id !== oldEdgeId).concat(edge);
};
/**
 *
 * @deprecated Use `reconnectEdge` instead.
 */ const updateEdge = (oldEdge, newConnection, edges, options = {
    shouldReplaceId: true
})=>{
    console.warn('[DEPRECATED] `updateEdge` is deprecated. Instead use `reconnectEdge` https://reactflow.dev/api-reference/utils/reconnect-edge');
    return reconnectEdge(oldEdge, newConnection, edges, options);
};
const pointToRendererPoint = ({ x, y }, [tx, ty, tScale], snapToGrid, [snapX, snapY])=>{
    const position = {
        x: (x - tx) / tScale,
        y: (y - ty) / tScale
    };
    if (snapToGrid) {
        return {
            x: snapX * Math.round(position.x / snapX),
            y: snapY * Math.round(position.y / snapY)
        };
    }
    return position;
};
const rendererPointToPoint = ({ x, y }, [tx, ty, tScale])=>{
    return {
        x: x * tScale + tx,
        y: y * tScale + ty
    };
};
const getNodePositionWithOrigin = (node, nodeOrigin = [
    0,
    0
])=>{
    if (!node) {
        return {
            x: 0,
            y: 0,
            positionAbsolute: {
                x: 0,
                y: 0
            }
        };
    }
    const offsetX = (node.width ?? 0) * nodeOrigin[0];
    const offsetY = (node.height ?? 0) * nodeOrigin[1];
    const position = {
        x: node.position.x - offsetX,
        y: node.position.y - offsetY
    };
    return {
        ...position,
        positionAbsolute: node.positionAbsolute ? {
            x: node.positionAbsolute.x - offsetX,
            y: node.positionAbsolute.y - offsetY
        } : position
    };
};
const getNodesBounds = (nodes, nodeOrigin = [
    0,
    0
])=>{
    if (nodes.length === 0) {
        return {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
    }
    const box = nodes.reduce((currBox, node)=>{
        const { x, y } = getNodePositionWithOrigin(node, nodeOrigin).positionAbsolute;
        return getBoundsOfBoxes(currBox, rectToBox({
            x,
            y,
            width: node.width || 0,
            height: node.height || 0
        }));
    }, {
        x: Infinity,
        y: Infinity,
        x2: -Infinity,
        y2: -Infinity
    });
    return boxToRect(box);
};
// @deprecated Use `getNodesBounds`.
const getRectOfNodes = (nodes, nodeOrigin = [
    0,
    0
])=>{
    console.warn('[DEPRECATED] `getRectOfNodes` is deprecated. Instead use `getNodesBounds` https://reactflow.dev/api-reference/utils/get-nodes-bounds.');
    return getNodesBounds(nodes, nodeOrigin);
};
const getNodesInside = (nodeInternals, rect, [tx, ty, tScale] = [
    0,
    0,
    1
], partially = false, // set excludeNonSelectableNodes if you want to pay attention to the nodes "selectable" attribute
excludeNonSelectableNodes = false, nodeOrigin = [
    0,
    0
])=>{
    const paneRect = {
        x: (rect.x - tx) / tScale,
        y: (rect.y - ty) / tScale,
        width: rect.width / tScale,
        height: rect.height / tScale
    };
    const visibleNodes = [];
    nodeInternals.forEach((node)=>{
        const { width, height, selectable = true, hidden = false } = node;
        if (excludeNonSelectableNodes && !selectable || hidden) {
            return false;
        }
        const { positionAbsolute } = getNodePositionWithOrigin(node, nodeOrigin);
        const nodeRect = {
            x: positionAbsolute.x,
            y: positionAbsolute.y,
            width: width || 0,
            height: height || 0
        };
        const overlappingArea = getOverlappingArea(paneRect, nodeRect);
        const notInitialized = typeof width === 'undefined' || typeof height === 'undefined' || width === null || height === null;
        const partiallyVisible = partially && overlappingArea > 0;
        const area = (width || 0) * (height || 0);
        const isVisible = notInitialized || partiallyVisible || overlappingArea >= area;
        if (isVisible || node.dragging) {
            visibleNodes.push(node);
        }
    });
    return visibleNodes;
};
const getConnectedEdges = (nodes, edges)=>{
    const nodeIds = nodes.map((node)=>node.id);
    return edges.filter((edge)=>nodeIds.includes(edge.source) || nodeIds.includes(edge.target));
};
// @deprecated Use `getViewportForBounds`.
const getTransformForBounds = (bounds, width, height, minZoom, maxZoom, padding = 0.1)=>{
    const { x, y, zoom } = getViewportForBounds(bounds, width, height, minZoom, maxZoom, padding);
    console.warn('[DEPRECATED] `getTransformForBounds` is deprecated. Instead use `getViewportForBounds`. Beware that the return value is type Viewport (`{ x: number, y: number, zoom: number }`) instead of Transform (`[number, number, number]`). https://reactflow.dev/api-reference/utils/get-viewport-for-bounds');
    return [
        x,
        y,
        zoom
    ];
};
const getViewportForBounds = (bounds, width, height, minZoom, maxZoom, padding = 0.1)=>{
    const xZoom = width / (bounds.width * (1 + padding));
    const yZoom = height / (bounds.height * (1 + padding));
    const zoom = Math.min(xZoom, yZoom);
    const clampedZoom = clamp(zoom, minZoom, maxZoom);
    const boundsCenterX = bounds.x + bounds.width / 2;
    const boundsCenterY = bounds.y + bounds.height / 2;
    const x = width / 2 - boundsCenterX * clampedZoom;
    const y = height / 2 - boundsCenterY * clampedZoom;
    return {
        x,
        y,
        zoom: clampedZoom
    };
};
const getD3Transition = (selection, duration = 0)=>{
    return selection.transition().duration(duration);
};
// this functions collects all handles and adds an absolute position
// so that we can later find the closest handle to the mouse position
function getHandles(node, handleBounds, type, currentHandle) {
    return (handleBounds[type] || []).reduce((res, h)=>{
        if (`${node.id}-${h.id}-${type}` !== currentHandle) {
            res.push({
                id: h.id || null,
                type,
                nodeId: node.id,
                x: (node.positionAbsolute?.x ?? 0) + h.x + h.width / 2,
                y: (node.positionAbsolute?.y ?? 0) + h.y + h.height / 2
            });
        }
        return res;
    }, []);
}
function getClosestHandle(event, doc, pos, connectionRadius, handles, validator) {
    // we always want to prioritize the handle below the mouse cursor over the closest distance handle,
    // because it could be that the center of another handle is closer to the mouse pointer than the handle below the cursor
    const { x, y } = getEventPosition(event);
    const domNodes = doc.elementsFromPoint(x, y);
    const handleBelow = domNodes.find((el)=>el.classList.contains('react-flow__handle'));
    if (handleBelow) {
        const handleNodeId = handleBelow.getAttribute('data-nodeid');
        if (handleNodeId) {
            const handleType = getHandleType(undefined, handleBelow);
            const handleId = handleBelow.getAttribute('data-handleid');
            const validHandleResult = validator({
                nodeId: handleNodeId,
                id: handleId,
                type: handleType
            });
            if (validHandleResult) {
                const handle = handles.find((h)=>h.nodeId === handleNodeId && h.type === handleType && h.id === handleId);
                return {
                    handle: {
                        id: handleId,
                        type: handleType,
                        nodeId: handleNodeId,
                        x: handle?.x || pos.x,
                        y: handle?.y || pos.y
                    },
                    validHandleResult
                };
            }
        }
    }
    // if we couldn't find a handle below the mouse cursor we look for the closest distance based on the connectionRadius
    let closestHandles = [];
    let minDistance = Infinity;
    handles.forEach((handle)=>{
        const distance = Math.sqrt((handle.x - pos.x) ** 2 + (handle.y - pos.y) ** 2);
        if (distance <= connectionRadius) {
            const validHandleResult = validator(handle);
            if (distance <= minDistance) {
                if (distance < minDistance) {
                    closestHandles = [
                        {
                            handle,
                            validHandleResult
                        }
                    ];
                } else if (distance === minDistance) {
                    // when multiple handles are on the same distance we collect all of them
                    closestHandles.push({
                        handle,
                        validHandleResult
                    });
                }
                minDistance = distance;
            }
        }
    });
    if (!closestHandles.length) {
        return {
            handle: null,
            validHandleResult: defaultResult()
        };
    }
    if (closestHandles.length === 1) {
        return closestHandles[0];
    }
    const hasValidHandle = closestHandles.some(({ validHandleResult })=>validHandleResult.isValid);
    const hasTargetHandle = closestHandles.some(({ handle })=>handle.type === 'target');
    // if multiple handles are layouted on top of each other we prefer the one with type = target and the one that is valid
    return closestHandles.find(({ handle, validHandleResult })=>hasTargetHandle ? handle.type === 'target' : hasValidHandle ? validHandleResult.isValid : true) || closestHandles[0];
}
const nullConnection = {
    source: null,
    target: null,
    sourceHandle: null,
    targetHandle: null
};
const defaultResult = ()=>({
        handleDomNode: null,
        isValid: false,
        connection: nullConnection,
        endHandle: null
    });
// checks if  and returns connection in fom of an object { source: 123, target: 312 }
function isValidHandle(handle, connectionMode, fromNodeId, fromHandleId, fromType, isValidConnection, doc) {
    const isTarget = fromType === 'target';
    const handleToCheck = doc.querySelector(`.react-flow__handle[data-id="${handle?.nodeId}-${handle?.id}-${handle?.type}"]`);
    const result = {
        ...defaultResult(),
        handleDomNode: handleToCheck
    };
    if (handleToCheck) {
        const handleType = getHandleType(undefined, handleToCheck);
        const handleNodeId = handleToCheck.getAttribute('data-nodeid');
        const handleId = handleToCheck.getAttribute('data-handleid');
        const connectable = handleToCheck.classList.contains('connectable');
        const connectableEnd = handleToCheck.classList.contains('connectableend');
        const connection = {
            source: isTarget ? handleNodeId : fromNodeId,
            sourceHandle: isTarget ? handleId : fromHandleId,
            target: isTarget ? fromNodeId : handleNodeId,
            targetHandle: isTarget ? fromHandleId : handleId
        };
        result.connection = connection;
        const isConnectable = connectable && connectableEnd;
        // in strict mode we don't allow target to target or source to source connections
        const isValid = isConnectable && (connectionMode === ConnectionMode.Strict ? isTarget && handleType === 'source' || !isTarget && handleType === 'target' : handleNodeId !== fromNodeId || handleId !== fromHandleId);
        if (isValid) {
            result.endHandle = {
                nodeId: handleNodeId,
                handleId,
                type: handleType
            };
            result.isValid = isValidConnection(connection);
        }
    }
    return result;
}
function getHandleLookup({ nodes, nodeId, handleId, handleType }) {
    return nodes.reduce((res, node)=>{
        if (node[internalsSymbol]) {
            const { handleBounds } = node[internalsSymbol];
            let sourceHandles = [];
            let targetHandles = [];
            if (handleBounds) {
                sourceHandles = getHandles(node, handleBounds, 'source', `${nodeId}-${handleId}-${handleType}`);
                targetHandles = getHandles(node, handleBounds, 'target', `${nodeId}-${handleId}-${handleType}`);
            }
            res.push(...sourceHandles, ...targetHandles);
        }
        return res;
    }, []);
}
function getHandleType(edgeUpdaterType, handleDomNode) {
    if (edgeUpdaterType) {
        return edgeUpdaterType;
    } else if (handleDomNode?.classList.contains('target')) {
        return 'target';
    } else if (handleDomNode?.classList.contains('source')) {
        return 'source';
    }
    return null;
}
function resetRecentHandle(handleDomNode) {
    handleDomNode?.classList.remove('valid', 'connecting', 'react-flow__handle-valid', 'react-flow__handle-connecting');
}
function getConnectionStatus(isInsideConnectionRadius, isHandleValid) {
    let connectionStatus = null;
    if (isHandleValid) {
        connectionStatus = 'valid';
    } else if (isInsideConnectionRadius && !isHandleValid) {
        connectionStatus = 'invalid';
    }
    return connectionStatus;
}
function handlePointerDown({ event, handleId, nodeId, onConnect, isTarget, getState, setState, isValidConnection, edgeUpdaterType, onReconnectEnd }) {
    // when react-flow is used inside a shadow root we can't use document
    const doc = getHostForElement(event.target);
    const { connectionMode, domNode, autoPanOnConnect, connectionRadius, onConnectStart, panBy, getNodes, cancelConnection } = getState();
    let autoPanId = 0;
    let closestHandle;
    const { x, y } = getEventPosition(event);
    const clickedHandle = doc?.elementFromPoint(x, y);
    const handleType = getHandleType(edgeUpdaterType, clickedHandle);
    const containerBounds = domNode?.getBoundingClientRect();
    if (!containerBounds || !handleType) {
        return;
    }
    let prevActiveHandle;
    let connectionPosition = getEventPosition(event, containerBounds);
    let autoPanStarted = false;
    let connection = null;
    let isValid = false;
    let handleDomNode = null;
    const handleLookup = getHandleLookup({
        nodes: getNodes(),
        nodeId,
        handleId,
        handleType
    });
    // when the user is moving the mouse close to the edge of the canvas while connecting we move the canvas
    const autoPan = ()=>{
        if (!autoPanOnConnect) {
            return;
        }
        const [xMovement, yMovement] = calcAutoPan(connectionPosition, containerBounds);
        panBy({
            x: xMovement,
            y: yMovement
        });
        autoPanId = requestAnimationFrame(autoPan);
    };
    setState({
        connectionPosition,
        connectionStatus: null,
        // connectionNodeId etc will be removed in the next major in favor of connectionStartHandle
        connectionNodeId: nodeId,
        connectionHandleId: handleId,
        connectionHandleType: handleType,
        connectionStartHandle: {
            nodeId,
            handleId,
            type: handleType
        },
        connectionEndHandle: null
    });
    onConnectStart?.(event, {
        nodeId,
        handleId,
        handleType
    });
    function onPointerMove(event) {
        const { transform } = getState();
        connectionPosition = getEventPosition(event, containerBounds);
        const { handle, validHandleResult } = getClosestHandle(event, doc, pointToRendererPoint(connectionPosition, transform, false, [
            1,
            1
        ]), connectionRadius, handleLookup, (handle)=>isValidHandle(handle, connectionMode, nodeId, handleId, isTarget ? 'target' : 'source', isValidConnection, doc));
        closestHandle = handle;
        if (!autoPanStarted) {
            autoPan();
            autoPanStarted = true;
        }
        handleDomNode = validHandleResult.handleDomNode;
        connection = validHandleResult.connection;
        isValid = validHandleResult.isValid;
        setState({
            connectionPosition: closestHandle && isValid ? rendererPointToPoint({
                x: closestHandle.x,
                y: closestHandle.y
            }, transform) : connectionPosition,
            connectionStatus: getConnectionStatus(!!closestHandle, isValid),
            connectionEndHandle: validHandleResult.endHandle
        });
        if (!closestHandle && !isValid && !handleDomNode) {
            return resetRecentHandle(prevActiveHandle);
        }
        if (connection.source !== connection.target && handleDomNode) {
            resetRecentHandle(prevActiveHandle);
            prevActiveHandle = handleDomNode;
            // @todo: remove the old class names "react-flow__handle-" in the next major version
            handleDomNode.classList.add('connecting', 'react-flow__handle-connecting');
            handleDomNode.classList.toggle('valid', isValid);
            handleDomNode.classList.toggle('react-flow__handle-valid', isValid);
        }
    }
    function onPointerUp(event) {
        if ((closestHandle || handleDomNode) && connection && isValid) {
            onConnect?.(connection);
        }
        // it's important to get a fresh reference from the store here
        // in order to get the latest state of onConnectEnd
        getState().onConnectEnd?.(event);
        if (edgeUpdaterType) {
            onReconnectEnd?.(event);
        }
        resetRecentHandle(prevActiveHandle);
        cancelConnection();
        cancelAnimationFrame(autoPanId);
        autoPanStarted = false;
        isValid = false;
        connection = null;
        handleDomNode = null;
        doc.removeEventListener('mousemove', onPointerMove);
        doc.removeEventListener('mouseup', onPointerUp);
        doc.removeEventListener('touchmove', onPointerMove);
        doc.removeEventListener('touchend', onPointerUp);
    }
    doc.addEventListener('mousemove', onPointerMove);
    doc.addEventListener('mouseup', onPointerUp);
    doc.addEventListener('touchmove', onPointerMove);
    doc.addEventListener('touchend', onPointerUp);
}
const alwaysValid = ()=>true;
const selector$f = (s)=>({
        connectionStartHandle: s.connectionStartHandle,
        connectOnClick: s.connectOnClick,
        noPanClassName: s.noPanClassName
    });
const connectingSelector = (nodeId, handleId, type)=>(state)=>{
        const { connectionStartHandle: startHandle, connectionEndHandle: endHandle, connectionClickStartHandle: clickHandle } = state;
        return {
            connecting: startHandle?.nodeId === nodeId && startHandle?.handleId === handleId && startHandle?.type === type || endHandle?.nodeId === nodeId && endHandle?.handleId === handleId && endHandle?.type === type,
            clickConnecting: clickHandle?.nodeId === nodeId && clickHandle?.handleId === handleId && clickHandle?.type === type
        };
    };
const Handle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ type = 'source', position = Position.Top, isValidConnection, isConnectable = true, isConnectableStart = true, isConnectableEnd = true, id, onConnect, children, className, onMouseDown, onTouchStart, ...rest }, ref)=>{
    const handleId = id || null;
    const isTarget = type === 'target';
    const store = useStoreApi();
    const nodeId = useNodeId();
    const { connectOnClick, noPanClassName } = useStore(selector$f, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const { connecting, clickConnecting } = useStore(connectingSelector(nodeId, handleId, type), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    if (!nodeId) {
        store.getState().onError?.('010', errorMessages['error010']());
    }
    const onConnectExtended = (params)=>{
        const { defaultEdgeOptions, onConnect: onConnectAction, hasDefaultEdges } = store.getState();
        const edgeParams = {
            ...defaultEdgeOptions,
            ...params
        };
        if (hasDefaultEdges) {
            const { edges, setEdges } = store.getState();
            setEdges(addEdge(edgeParams, edges));
        }
        onConnectAction?.(edgeParams);
        onConnect?.(edgeParams);
    };
    const onPointerDown = (event)=>{
        if (!nodeId) {
            return;
        }
        const isMouseTriggered = isMouseEvent(event);
        if (isConnectableStart && (isMouseTriggered && event.button === 0 || !isMouseTriggered)) {
            handlePointerDown({
                event,
                handleId,
                nodeId,
                onConnect: onConnectExtended,
                isTarget,
                getState: store.getState,
                setState: store.setState,
                isValidConnection: isValidConnection || store.getState().isValidConnection || alwaysValid
            });
        }
        if (isMouseTriggered) {
            onMouseDown?.(event);
        } else {
            onTouchStart?.(event);
        }
    };
    const onClick = (event)=>{
        const { onClickConnectStart, onClickConnectEnd, connectionClickStartHandle, connectionMode, isValidConnection: isValidConnectionStore } = store.getState();
        if (!nodeId || !connectionClickStartHandle && !isConnectableStart) {
            return;
        }
        if (!connectionClickStartHandle) {
            onClickConnectStart?.(event, {
                nodeId,
                handleId,
                handleType: type
            });
            store.setState({
                connectionClickStartHandle: {
                    nodeId,
                    type,
                    handleId
                }
            });
            return;
        }
        const doc = getHostForElement(event.target);
        const isValidConnectionHandler = isValidConnection || isValidConnectionStore || alwaysValid;
        const { connection, isValid } = isValidHandle({
            nodeId,
            id: handleId,
            type
        }, connectionMode, connectionClickStartHandle.nodeId, connectionClickStartHandle.handleId || null, connectionClickStartHandle.type, isValidConnectionHandler, doc);
        if (isValid) {
            onConnectExtended(connection);
        }
        onClickConnectEnd?.(event);
        store.setState({
            connectionClickStartHandle: null
        });
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        "data-handleid": handleId,
        "data-nodeid": nodeId,
        "data-handlepos": position,
        "data-id": `${nodeId}-${handleId}-${type}`,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
            'react-flow__handle',
            `react-flow__handle-${position}`,
            'nodrag',
            noPanClassName,
            className,
            {
                source: !isTarget,
                target: isTarget,
                connectable: isConnectable,
                connectablestart: isConnectableStart,
                connectableend: isConnectableEnd,
                connecting: clickConnecting,
                // this class is used to style the handle when the user is connecting
                connectionindicator: isConnectable && (isConnectableStart && !connecting || isConnectableEnd && connecting)
            }
        ]),
        onMouseDown: onPointerDown,
        onTouchStart: onPointerDown,
        onClick: connectOnClick ? onClick : undefined,
        ref: ref,
        ...rest
    }, children);
});
Handle.displayName = 'Handle';
var Handle$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(Handle);
const DefaultNode = ({ data, isConnectable, targetPosition = Position.Top, sourcePosition = Position.Bottom })=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Handle$1, {
        type: "target",
        position: targetPosition,
        isConnectable: isConnectable
    }), data?.label, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Handle$1, {
        type: "source",
        position: sourcePosition,
        isConnectable: isConnectable
    }));
};
DefaultNode.displayName = 'DefaultNode';
var DefaultNode$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(DefaultNode);
const InputNode = ({ data, isConnectable, sourcePosition = Position.Bottom })=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, data?.label, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Handle$1, {
        type: "source",
        position: sourcePosition,
        isConnectable: isConnectable
    }));
InputNode.displayName = 'InputNode';
var InputNode$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(InputNode);
const OutputNode = ({ data, isConnectable, targetPosition = Position.Top })=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Handle$1, {
        type: "target",
        position: targetPosition,
        isConnectable: isConnectable
    }), data?.label);
OutputNode.displayName = 'OutputNode';
var OutputNode$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(OutputNode);
const GroupNode = ()=>null;
GroupNode.displayName = 'GroupNode';
const selector$e = (s)=>({
        selectedNodes: s.getNodes().filter((n)=>n.selected),
        selectedEdges: s.edges.filter((e)=>e.selected).map((e)=>({
                ...e
            }))
    });
const selectId = (obj)=>obj.id;
function areEqual(a, b) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"])(a.selectedNodes.map(selectId), b.selectedNodes.map(selectId)) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"])(a.selectedEdges.map(selectId), b.selectedEdges.map(selectId));
}
// This is just a helper component for calling the onSelectionChange listener.
// @TODO: Now that we have the onNodesChange and on EdgesChange listeners, do we still need this component?
const SelectionListener = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(({ onSelectionChange })=>{
    const store = useStoreApi();
    const { selectedNodes, selectedEdges } = useStore(selector$e, areEqual);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const params = {
            nodes: selectedNodes,
            edges: selectedEdges
        };
        onSelectionChange?.(params);
        store.getState().onSelectionChange.forEach((fn)=>fn(params));
    }, [
        selectedNodes,
        selectedEdges,
        onSelectionChange
    ]);
    return null;
});
SelectionListener.displayName = 'SelectionListener';
const changeSelector = (s)=>!!s.onSelectionChange;
function Wrapper$1({ onSelectionChange }) {
    const storeHasSelectionChange = useStore(changeSelector);
    if (onSelectionChange || storeHasSelectionChange) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(SelectionListener, {
            onSelectionChange: onSelectionChange
        });
    }
    return null;
}
const selector$d = (s)=>({
        setNodes: s.setNodes,
        setEdges: s.setEdges,
        setDefaultNodesAndEdges: s.setDefaultNodesAndEdges,
        setMinZoom: s.setMinZoom,
        setMaxZoom: s.setMaxZoom,
        setTranslateExtent: s.setTranslateExtent,
        setNodeExtent: s.setNodeExtent,
        reset: s.reset
    });
function useStoreUpdater(value, setStoreState) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (typeof value !== 'undefined') {
            setStoreState(value);
        }
    }, [
        value
    ]);
}
// updates with values in store that don't have a dedicated setter function
function useDirectStoreUpdater(key, value, setState) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (typeof value !== 'undefined') {
            setState({
                [key]: value
            });
        }
    }, [
        value
    ]);
}
const StoreUpdater = ({ nodes, edges, defaultNodes, defaultEdges, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, nodesDraggable, nodesConnectable, nodesFocusable, edgesFocusable, edgesUpdatable, elevateNodesOnSelect, minZoom, maxZoom, nodeExtent, onNodesChange, onEdgesChange, elementsSelectable, connectionMode, snapGrid, snapToGrid, translateExtent, connectOnClick, defaultEdgeOptions, fitView, fitViewOptions, onNodesDelete, onEdgesDelete, onNodeDrag, onNodeDragStart, onNodeDragStop, onSelectionDrag, onSelectionDragStart, onSelectionDragStop, noPanClassName, nodeOrigin, rfId, autoPanOnConnect, autoPanOnNodeDrag, onError, connectionRadius, isValidConnection, nodeDragThreshold })=>{
    const { setNodes, setEdges, setDefaultNodesAndEdges, setMinZoom, setMaxZoom, setTranslateExtent, setNodeExtent, reset } = useStore(selector$d, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const store = useStoreApi();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const edgesWithDefaults = defaultEdges?.map((e)=>({
                ...e,
                ...defaultEdgeOptions
            }));
        setDefaultNodesAndEdges(defaultNodes, edgesWithDefaults);
        return ()=>{
            reset();
        };
    }, []);
    useDirectStoreUpdater('defaultEdgeOptions', defaultEdgeOptions, store.setState);
    useDirectStoreUpdater('connectionMode', connectionMode, store.setState);
    useDirectStoreUpdater('onConnect', onConnect, store.setState);
    useDirectStoreUpdater('onConnectStart', onConnectStart, store.setState);
    useDirectStoreUpdater('onConnectEnd', onConnectEnd, store.setState);
    useDirectStoreUpdater('onClickConnectStart', onClickConnectStart, store.setState);
    useDirectStoreUpdater('onClickConnectEnd', onClickConnectEnd, store.setState);
    useDirectStoreUpdater('nodesDraggable', nodesDraggable, store.setState);
    useDirectStoreUpdater('nodesConnectable', nodesConnectable, store.setState);
    useDirectStoreUpdater('nodesFocusable', nodesFocusable, store.setState);
    useDirectStoreUpdater('edgesFocusable', edgesFocusable, store.setState);
    useDirectStoreUpdater('edgesUpdatable', edgesUpdatable, store.setState);
    useDirectStoreUpdater('elementsSelectable', elementsSelectable, store.setState);
    useDirectStoreUpdater('elevateNodesOnSelect', elevateNodesOnSelect, store.setState);
    useDirectStoreUpdater('snapToGrid', snapToGrid, store.setState);
    useDirectStoreUpdater('snapGrid', snapGrid, store.setState);
    useDirectStoreUpdater('onNodesChange', onNodesChange, store.setState);
    useDirectStoreUpdater('onEdgesChange', onEdgesChange, store.setState);
    useDirectStoreUpdater('connectOnClick', connectOnClick, store.setState);
    useDirectStoreUpdater('fitViewOnInit', fitView, store.setState);
    useDirectStoreUpdater('fitViewOnInitOptions', fitViewOptions, store.setState);
    useDirectStoreUpdater('onNodesDelete', onNodesDelete, store.setState);
    useDirectStoreUpdater('onEdgesDelete', onEdgesDelete, store.setState);
    useDirectStoreUpdater('onNodeDrag', onNodeDrag, store.setState);
    useDirectStoreUpdater('onNodeDragStart', onNodeDragStart, store.setState);
    useDirectStoreUpdater('onNodeDragStop', onNodeDragStop, store.setState);
    useDirectStoreUpdater('onSelectionDrag', onSelectionDrag, store.setState);
    useDirectStoreUpdater('onSelectionDragStart', onSelectionDragStart, store.setState);
    useDirectStoreUpdater('onSelectionDragStop', onSelectionDragStop, store.setState);
    useDirectStoreUpdater('noPanClassName', noPanClassName, store.setState);
    useDirectStoreUpdater('nodeOrigin', nodeOrigin, store.setState);
    useDirectStoreUpdater('rfId', rfId, store.setState);
    useDirectStoreUpdater('autoPanOnConnect', autoPanOnConnect, store.setState);
    useDirectStoreUpdater('autoPanOnNodeDrag', autoPanOnNodeDrag, store.setState);
    useDirectStoreUpdater('onError', onError, store.setState);
    useDirectStoreUpdater('connectionRadius', connectionRadius, store.setState);
    useDirectStoreUpdater('isValidConnection', isValidConnection, store.setState);
    useDirectStoreUpdater('nodeDragThreshold', nodeDragThreshold, store.setState);
    useStoreUpdater(nodes, setNodes);
    useStoreUpdater(edges, setEdges);
    useStoreUpdater(minZoom, setMinZoom);
    useStoreUpdater(maxZoom, setMaxZoom);
    useStoreUpdater(translateExtent, setTranslateExtent);
    useStoreUpdater(nodeExtent, setNodeExtent);
    return null;
};
const style = {
    display: 'none'
};
const ariaLiveStyle = {
    position: 'absolute',
    width: 1,
    height: 1,
    margin: -1,
    border: 0,
    padding: 0,
    overflow: 'hidden',
    clip: 'rect(0px, 0px, 0px, 0px)',
    clipPath: 'inset(100%)'
};
const ARIA_NODE_DESC_KEY = 'react-flow__node-desc';
const ARIA_EDGE_DESC_KEY = 'react-flow__edge-desc';
const ARIA_LIVE_MESSAGE = 'react-flow__aria-live';
const selector$c = (s)=>s.ariaLiveMessage;
function AriaLiveMessage({ rfId }) {
    const ariaLiveMessage = useStore(selector$c);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        id: `${ARIA_LIVE_MESSAGE}-${rfId}`,
        "aria-live": "assertive",
        "aria-atomic": "true",
        style: ariaLiveStyle
    }, ariaLiveMessage);
}
function A11yDescriptions({ rfId, disableKeyboardA11y }) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        id: `${ARIA_NODE_DESC_KEY}-${rfId}`,
        style: style
    }, "Press enter or space to select a node.", !disableKeyboardA11y && 'You can then use the arrow keys to move the node around.', " Press delete to remove it and escape to cancel.", ' '), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        id: `${ARIA_EDGE_DESC_KEY}-${rfId}`,
        style: style
    }, "Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."), !disableKeyboardA11y && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(AriaLiveMessage, {
        rfId: rfId
    }));
}
// the keycode can be a string 'a' or an array of strings ['a', 'a+d']
// a string means a single key 'a' or a combination when '+' is used 'a+d'
// an array means different possibilities. Explainer: ['a', 'd+s'] here the
// user can use the single key 'a' or the combination 'd' + 's'
var useKeyPress = (keyCode = null, options = {
    actInsideInputWithModifier: true
})=>{
    const [keyPressed, setKeyPressed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // we need to remember if a modifier key is pressed in order to track it
    const modifierPressed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    // we need to remember the pressed keys in order to support combinations
    const pressedKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(new Set([]));
    // keyCodes = array with single keys [['a']] or key combinations [['a', 's']]
    // keysToWatch = array with all keys flattened ['a', 'd', 'ShiftLeft']
    // used to check if we store event.code or event.key. When the code is in the list of keysToWatch
    // we use the code otherwise the key. Explainer: When you press the left "command" key, the code is "MetaLeft"
    // and the key is "Meta". We want users to be able to pass keys and codes so we assume that the key is meant when
    // we can't find it in the list of keysToWatch.
    const [keyCodes, keysToWatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (keyCode !== null) {
            const keyCodeArr = Array.isArray(keyCode) ? keyCode : [
                keyCode
            ];
            const keys = keyCodeArr.filter((kc)=>typeof kc === 'string').map((kc)=>kc.split('+'));
            const keysFlat = keys.reduce((res, item)=>res.concat(...item), []);
            return [
                keys,
                keysFlat
            ];
        }
        return [
            [],
            []
        ];
    }, [
        keyCode
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const doc = typeof document !== 'undefined' ? document : null;
        const target = options?.target || doc;
        if (keyCode !== null) {
            const downHandler = (event)=>{
                modifierPressed.current = event.ctrlKey || event.metaKey || event.shiftKey;
                const preventAction = (!modifierPressed.current || modifierPressed.current && !options.actInsideInputWithModifier) && isInputDOMNode(event);
                if (preventAction) {
                    return false;
                }
                const keyOrCode = useKeyOrCode(event.code, keysToWatch);
                pressedKeys.current.add(event[keyOrCode]);
                if (isMatchingKey(keyCodes, pressedKeys.current, false)) {
                    event.preventDefault();
                    setKeyPressed(true);
                }
            };
            const upHandler = (event)=>{
                const preventAction = (!modifierPressed.current || modifierPressed.current && !options.actInsideInputWithModifier) && isInputDOMNode(event);
                if (preventAction) {
                    return false;
                }
                const keyOrCode = useKeyOrCode(event.code, keysToWatch);
                if (isMatchingKey(keyCodes, pressedKeys.current, true)) {
                    setKeyPressed(false);
                    pressedKeys.current.clear();
                } else {
                    pressedKeys.current.delete(event[keyOrCode]);
                }
                // fix for Mac: when cmd key is pressed, keyup is not triggered for any other key, see: https://stackoverflow.com/questions/27380018/when-cmd-key-is-kept-pressed-keyup-is-not-triggered-for-any-other-key
                if (event.key === 'Meta') {
                    pressedKeys.current.clear();
                }
                modifierPressed.current = false;
            };
            const resetHandler = ()=>{
                pressedKeys.current.clear();
                setKeyPressed(false);
            };
            target?.addEventListener('keydown', downHandler);
            target?.addEventListener('keyup', upHandler);
            window.addEventListener('blur', resetHandler);
            return ()=>{
                target?.removeEventListener('keydown', downHandler);
                target?.removeEventListener('keyup', upHandler);
                window.removeEventListener('blur', resetHandler);
            };
        }
    }, [
        keyCode,
        setKeyPressed
    ]);
    return keyPressed;
};
// utils
function isMatchingKey(keyCodes, pressedKeys, isUp) {
    return keyCodes// we only want to compare same sizes of keyCode definitions
    // and pressed keys. When the user specified 'Meta' as a key somewhere
    // this would also be truthy without this filter when user presses 'Meta' + 'r'
    .filter((keys)=>isUp || keys.length === pressedKeys.size)// since we want to support multiple possibilities only one of the
    // combinations need to be part of the pressed keys
    .some((keys)=>keys.every((k)=>pressedKeys.has(k)));
}
function useKeyOrCode(eventCode, keysToWatch) {
    return keysToWatch.includes(eventCode) ? 'code' : 'key';
}
function calculateXYZPosition(node, nodeInternals, result, nodeOrigin) {
    const parentId = node.parentNode || node.parentId;
    if (!parentId) {
        return result;
    }
    const parentNode = nodeInternals.get(parentId);
    const parentNodePosition = getNodePositionWithOrigin(parentNode, nodeOrigin);
    return calculateXYZPosition(parentNode, nodeInternals, {
        x: (result.x ?? 0) + parentNodePosition.x,
        y: (result.y ?? 0) + parentNodePosition.y,
        z: (parentNode[internalsSymbol]?.z ?? 0) > (result.z ?? 0) ? parentNode[internalsSymbol]?.z ?? 0 : result.z ?? 0
    }, nodeOrigin);
}
function updateAbsoluteNodePositions(nodeInternals, nodeOrigin, parentNodes) {
    nodeInternals.forEach((node)=>{
        const parentId = node.parentNode || node.parentId;
        if (parentId && !nodeInternals.has(parentId)) {
            throw new Error(`Parent node ${parentId} not found`);
        }
        if (parentId || parentNodes?.[node.id]) {
            const { x, y, z } = calculateXYZPosition(node, nodeInternals, {
                ...node.position,
                z: node[internalsSymbol]?.z ?? 0
            }, nodeOrigin);
            node.positionAbsolute = {
                x,
                y
            };
            node[internalsSymbol].z = z;
            if (parentNodes?.[node.id]) {
                node[internalsSymbol].isParent = true;
            }
        }
    });
}
function createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect) {
    const nextNodeInternals = new Map();
    const parentNodes = {};
    const selectedNodeZ = elevateNodesOnSelect ? 1000 : 0;
    nodes.forEach((node)=>{
        const z = (isNumeric(node.zIndex) ? node.zIndex : 0) + (node.selected ? selectedNodeZ : 0);
        const currInternals = nodeInternals.get(node.id);
        const internals = {
            ...node,
            positionAbsolute: {
                x: node.position.x,
                y: node.position.y
            }
        };
        const parentId = node.parentNode || node.parentId;
        if (parentId) {
            parentNodes[parentId] = true;
        }
        const resetHandleBounds = currInternals?.type && currInternals?.type !== node.type;
        Object.defineProperty(internals, internalsSymbol, {
            enumerable: false,
            value: {
                handleBounds: resetHandleBounds ? undefined : currInternals?.[internalsSymbol]?.handleBounds,
                z
            }
        });
        nextNodeInternals.set(node.id, internals);
    });
    updateAbsoluteNodePositions(nextNodeInternals, nodeOrigin, parentNodes);
    return nextNodeInternals;
}
function fitView(get, options = {}) {
    const { getNodes, width, height, minZoom, maxZoom, d3Zoom, d3Selection, fitViewOnInitDone, fitViewOnInit, nodeOrigin } = get();
    const isInitialFitView = options.initial && !fitViewOnInitDone && fitViewOnInit;
    const d3initialized = d3Zoom && d3Selection;
    if (d3initialized && (isInitialFitView || !options.initial)) {
        const nodes = getNodes().filter((n)=>{
            const isVisible = options.includeHiddenNodes ? n.width && n.height : !n.hidden;
            if (options.nodes?.length) {
                return isVisible && options.nodes.some((optionNode)=>optionNode.id === n.id);
            }
            return isVisible;
        });
        const nodesInitialized = nodes.every((n)=>n.width && n.height);
        if (nodes.length > 0 && nodesInitialized) {
            const bounds = getNodesBounds(nodes, nodeOrigin);
            const { x, y, zoom } = getViewportForBounds(bounds, width, height, options.minZoom ?? minZoom, options.maxZoom ?? maxZoom, options.padding ?? 0.1);
            const nextTransform = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__identity__as__zoomIdentity$3e$__["zoomIdentity"].translate(x, y).scale(zoom);
            if (typeof options.duration === 'number' && options.duration > 0) {
                d3Zoom.transform(getD3Transition(d3Selection, options.duration), nextTransform);
            } else {
                d3Zoom.transform(d3Selection, nextTransform);
            }
            return true;
        }
    }
    return false;
}
function handleControlledNodeSelectionChange(nodeChanges, nodeInternals) {
    nodeChanges.forEach((change)=>{
        const node = nodeInternals.get(change.id);
        if (node) {
            nodeInternals.set(node.id, {
                ...node,
                [internalsSymbol]: node[internalsSymbol],
                selected: change.selected
            });
        }
    });
    return new Map(nodeInternals);
}
function handleControlledEdgeSelectionChange(edgeChanges, edges) {
    return edges.map((e)=>{
        const change = edgeChanges.find((change)=>change.id === e.id);
        if (change) {
            e.selected = change.selected;
        }
        return e;
    });
}
function updateNodesAndEdgesSelections({ changedNodes, changedEdges, get, set }) {
    const { nodeInternals, edges, onNodesChange, onEdgesChange, hasDefaultNodes, hasDefaultEdges } = get();
    if (changedNodes?.length) {
        if (hasDefaultNodes) {
            set({
                nodeInternals: handleControlledNodeSelectionChange(changedNodes, nodeInternals)
            });
        }
        onNodesChange?.(changedNodes);
    }
    if (changedEdges?.length) {
        if (hasDefaultEdges) {
            set({
                edges: handleControlledEdgeSelectionChange(changedEdges, edges)
            });
        }
        onEdgesChange?.(changedEdges);
    }
}
// eslint-disable-next-line @typescript-eslint/no-empty-function
const noop = ()=>{};
const initialViewportHelper = {
    zoomIn: noop,
    zoomOut: noop,
    zoomTo: noop,
    getZoom: ()=>1,
    setViewport: noop,
    getViewport: ()=>({
            x: 0,
            y: 0,
            zoom: 1
        }),
    fitView: ()=>false,
    setCenter: noop,
    fitBounds: noop,
    project: (position)=>position,
    screenToFlowPosition: (position)=>position,
    flowToScreenPosition: (position)=>position,
    viewportInitialized: false
};
const selector$b = (s)=>({
        d3Zoom: s.d3Zoom,
        d3Selection: s.d3Selection
    });
const useViewportHelper = ()=>{
    const store = useStoreApi();
    const { d3Zoom, d3Selection } = useStore(selector$b, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const viewportHelperFunctions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (d3Selection && d3Zoom) {
            return {
                zoomIn: (options)=>d3Zoom.scaleBy(getD3Transition(d3Selection, options?.duration), 1.2),
                zoomOut: (options)=>d3Zoom.scaleBy(getD3Transition(d3Selection, options?.duration), 1 / 1.2),
                zoomTo: (zoomLevel, options)=>d3Zoom.scaleTo(getD3Transition(d3Selection, options?.duration), zoomLevel),
                getZoom: ()=>store.getState().transform[2],
                setViewport: (transform, options)=>{
                    const [x, y, zoom] = store.getState().transform;
                    const nextTransform = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__identity__as__zoomIdentity$3e$__["zoomIdentity"].translate(transform.x ?? x, transform.y ?? y).scale(transform.zoom ?? zoom);
                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), nextTransform);
                },
                getViewport: ()=>{
                    const [x, y, zoom] = store.getState().transform;
                    return {
                        x,
                        y,
                        zoom
                    };
                },
                fitView: (options)=>fitView(store.getState, options),
                setCenter: (x, y, options)=>{
                    const { width, height, maxZoom } = store.getState();
                    const nextZoom = typeof options?.zoom !== 'undefined' ? options.zoom : maxZoom;
                    const centerX = width / 2 - x * nextZoom;
                    const centerY = height / 2 - y * nextZoom;
                    const transform = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__identity__as__zoomIdentity$3e$__["zoomIdentity"].translate(centerX, centerY).scale(nextZoom);
                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), transform);
                },
                fitBounds: (bounds, options)=>{
                    const { width, height, minZoom, maxZoom } = store.getState();
                    const { x, y, zoom } = getViewportForBounds(bounds, width, height, minZoom, maxZoom, options?.padding ?? 0.1);
                    const transform = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__identity__as__zoomIdentity$3e$__["zoomIdentity"].translate(x, y).scale(zoom);
                    d3Zoom.transform(getD3Transition(d3Selection, options?.duration), transform);
                },
                // @deprecated Use `screenToFlowPosition`.
                project: (position)=>{
                    const { transform, snapToGrid, snapGrid } = store.getState();
                    console.warn('[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position');
                    return pointToRendererPoint(position, transform, snapToGrid, snapGrid);
                },
                screenToFlowPosition: (position)=>{
                    const { transform, snapToGrid, snapGrid, domNode } = store.getState();
                    if (!domNode) {
                        return position;
                    }
                    const { x: domX, y: domY } = domNode.getBoundingClientRect();
                    const relativePosition = {
                        x: position.x - domX,
                        y: position.y - domY
                    };
                    return pointToRendererPoint(relativePosition, transform, snapToGrid, snapGrid);
                },
                flowToScreenPosition: (position)=>{
                    const { transform, domNode } = store.getState();
                    if (!domNode) {
                        return position;
                    }
                    const { x: domX, y: domY } = domNode.getBoundingClientRect();
                    const rendererPosition = rendererPointToPoint(position, transform);
                    return {
                        x: rendererPosition.x + domX,
                        y: rendererPosition.y + domY
                    };
                },
                viewportInitialized: true
            };
        }
        return initialViewportHelper;
    }, [
        d3Zoom,
        d3Selection
    ]);
    return viewportHelperFunctions;
};
/* eslint-disable-next-line @typescript-eslint/no-explicit-any */ function useReactFlow() {
    const viewportHelper = useViewportHelper();
    const store = useStoreApi();
    const getNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        return store.getState().getNodes().map((n)=>({
                ...n
            }));
    }, []);
    const getNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((id)=>{
        return store.getState().nodeInternals.get(id);
    }, []);
    const getEdges = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const { edges = [] } = store.getState();
        return edges.map((e)=>({
                ...e
            }));
    }, []);
    const getEdge = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((id)=>{
        const { edges = [] } = store.getState();
        return edges.find((e)=>e.id === id);
    }, []);
    const setNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((payload)=>{
        const { getNodes, setNodes, hasDefaultNodes, onNodesChange } = store.getState();
        const nodes = getNodes();
        const nextNodes = typeof payload === 'function' ? payload(nodes) : payload;
        if (hasDefaultNodes) {
            setNodes(nextNodes);
        } else if (onNodesChange) {
            const changes = nextNodes.length === 0 ? nodes.map((node)=>({
                    type: 'remove',
                    id: node.id
                })) : nextNodes.map((node)=>({
                    item: node,
                    type: 'reset'
                }));
            onNodesChange(changes);
        }
    }, []);
    const setEdges = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((payload)=>{
        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange } = store.getState();
        const nextEdges = typeof payload === 'function' ? payload(edges) : payload;
        if (hasDefaultEdges) {
            setEdges(nextEdges);
        } else if (onEdgesChange) {
            const changes = nextEdges.length === 0 ? edges.map((edge)=>({
                    type: 'remove',
                    id: edge.id
                })) : nextEdges.map((edge)=>({
                    item: edge,
                    type: 'reset'
                }));
            onEdgesChange(changes);
        }
    }, []);
    const addNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((payload)=>{
        const nodes = Array.isArray(payload) ? payload : [
            payload
        ];
        const { getNodes, setNodes, hasDefaultNodes, onNodesChange } = store.getState();
        if (hasDefaultNodes) {
            const currentNodes = getNodes();
            const nextNodes = [
                ...currentNodes,
                ...nodes
            ];
            setNodes(nextNodes);
        } else if (onNodesChange) {
            const changes = nodes.map((node)=>({
                    item: node,
                    type: 'add'
                }));
            onNodesChange(changes);
        }
    }, []);
    const addEdges = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((payload)=>{
        const nextEdges = Array.isArray(payload) ? payload : [
            payload
        ];
        const { edges = [], setEdges, hasDefaultEdges, onEdgesChange } = store.getState();
        if (hasDefaultEdges) {
            setEdges([
                ...edges,
                ...nextEdges
            ]);
        } else if (onEdgesChange) {
            const changes = nextEdges.map((edge)=>({
                    item: edge,
                    type: 'add'
                }));
            onEdgesChange(changes);
        }
    }, []);
    const toObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        const { getNodes, edges = [], transform } = store.getState();
        const [x, y, zoom] = transform;
        return {
            nodes: getNodes().map((n)=>({
                    ...n
                })),
            edges: edges.map((e)=>({
                    ...e
                })),
            viewport: {
                x,
                y,
                zoom
            }
        };
    }, []);
    const deleteElements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(({ nodes: nodesDeleted, edges: edgesDeleted })=>{
        const { nodeInternals, getNodes, edges, hasDefaultNodes, hasDefaultEdges, onNodesDelete, onEdgesDelete, onNodesChange, onEdgesChange } = store.getState();
        const nodeIds = (nodesDeleted || []).map((node)=>node.id);
        const edgeIds = (edgesDeleted || []).map((edge)=>edge.id);
        const nodesToRemove = getNodes().reduce((res, node)=>{
            const parentId = node.parentNode || node.parentId;
            const parentHit = !nodeIds.includes(node.id) && parentId && res.find((n)=>n.id === parentId);
            const deletable = typeof node.deletable === 'boolean' ? node.deletable : true;
            if (deletable && (nodeIds.includes(node.id) || parentHit)) {
                res.push(node);
            }
            return res;
        }, []);
        const deletableEdges = edges.filter((e)=>typeof e.deletable === 'boolean' ? e.deletable : true);
        const initialHitEdges = deletableEdges.filter((e)=>edgeIds.includes(e.id));
        if (nodesToRemove || initialHitEdges) {
            const connectedEdges = getConnectedEdges(nodesToRemove, deletableEdges);
            const edgesToRemove = [
                ...initialHitEdges,
                ...connectedEdges
            ];
            const edgeIdsToRemove = edgesToRemove.reduce((res, edge)=>{
                if (!res.includes(edge.id)) {
                    res.push(edge.id);
                }
                return res;
            }, []);
            if (hasDefaultEdges || hasDefaultNodes) {
                if (hasDefaultEdges) {
                    store.setState({
                        edges: edges.filter((e)=>!edgeIdsToRemove.includes(e.id))
                    });
                }
                if (hasDefaultNodes) {
                    nodesToRemove.forEach((node)=>{
                        nodeInternals.delete(node.id);
                    });
                    store.setState({
                        nodeInternals: new Map(nodeInternals)
                    });
                }
            }
            if (edgeIdsToRemove.length > 0) {
                onEdgesDelete?.(edgesToRemove);
                if (onEdgesChange) {
                    onEdgesChange(edgeIdsToRemove.map((id)=>({
                            id,
                            type: 'remove'
                        })));
                }
            }
            if (nodesToRemove.length > 0) {
                onNodesDelete?.(nodesToRemove);
                if (onNodesChange) {
                    const nodeChanges = nodesToRemove.map((n)=>({
                            id: n.id,
                            type: 'remove'
                        }));
                    onNodesChange(nodeChanges);
                }
            }
        }
    }, []);
    const getNodeRect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodeOrRect)=>{
        const isRect = isRectObject(nodeOrRect);
        const node = isRect ? null : store.getState().nodeInternals.get(nodeOrRect.id);
        if (!isRect && !node) {
            return [
                null,
                null,
                isRect
            ];
        }
        const nodeRect = isRect ? nodeOrRect : nodeToRect(node);
        return [
            nodeRect,
            node,
            isRect
        ];
    }, []);
    const getIntersectingNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodeOrRect, partially = true, nodes)=>{
        const [nodeRect, node, isRect] = getNodeRect(nodeOrRect);
        if (!nodeRect) {
            return [];
        }
        return (nodes || store.getState().getNodes()).filter((n)=>{
            if (!isRect && (n.id === node.id || !n.positionAbsolute)) {
                return false;
            }
            const currNodeRect = nodeToRect(n);
            const overlappingArea = getOverlappingArea(currNodeRect, nodeRect);
            const partiallyVisible = partially && overlappingArea > 0;
            return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;
        });
    }, []);
    const isNodeIntersecting = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((nodeOrRect, area, partially = true)=>{
        const [nodeRect] = getNodeRect(nodeOrRect);
        if (!nodeRect) {
            return false;
        }
        const overlappingArea = getOverlappingArea(nodeRect, area);
        const partiallyVisible = partially && overlappingArea > 0;
        return partiallyVisible || overlappingArea >= nodeRect.width * nodeRect.height;
    }, []);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        return {
            ...viewportHelper,
            getNodes,
            getNode,
            getEdges,
            getEdge,
            setNodes,
            setEdges,
            addNodes,
            addEdges,
            toObject,
            deleteElements,
            getIntersectingNodes,
            isNodeIntersecting
        };
    }, [
        viewportHelper,
        getNodes,
        getNode,
        getEdges,
        getEdge,
        setNodes,
        setEdges,
        addNodes,
        addEdges,
        toObject,
        deleteElements,
        getIntersectingNodes,
        isNodeIntersecting
    ]);
}
const deleteKeyOptions = {
    actInsideInputWithModifier: false
};
var useGlobalKeyHandler = ({ deleteKeyCode, multiSelectionKeyCode })=>{
    const store = useStoreApi();
    const { deleteElements } = useReactFlow();
    const deleteKeyPressed = useKeyPress(deleteKeyCode, deleteKeyOptions);
    const multiSelectionKeyPressed = useKeyPress(multiSelectionKeyCode);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (deleteKeyPressed) {
            const { edges, getNodes } = store.getState();
            const selectedNodes = getNodes().filter((node)=>node.selected);
            const selectedEdges = edges.filter((edge)=>edge.selected);
            deleteElements({
                nodes: selectedNodes,
                edges: selectedEdges
            });
            store.setState({
                nodesSelectionActive: false
            });
        }
    }, [
        deleteKeyPressed
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        store.setState({
            multiSelectionActive: multiSelectionKeyPressed
        });
    }, [
        multiSelectionKeyPressed
    ]);
};
function useResizeHandler(rendererNode) {
    const store = useStoreApi();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let resizeObserver;
        const updateDimensions = ()=>{
            if (!rendererNode.current) {
                return;
            }
            const size = getDimensions(rendererNode.current);
            if (size.height === 0 || size.width === 0) {
                store.getState().onError?.('004', errorMessages['error004']());
            }
            store.setState({
                width: size.width || 500,
                height: size.height || 500
            });
        };
        updateDimensions();
        window.addEventListener('resize', updateDimensions);
        if (rendererNode.current) {
            resizeObserver = new ResizeObserver(()=>updateDimensions());
            resizeObserver.observe(rendererNode.current);
        }
        return ()=>{
            window.removeEventListener('resize', updateDimensions);
            if (resizeObserver && rendererNode.current) {
                resizeObserver.unobserve(rendererNode.current);
            }
        };
    }, []);
}
const containerStyle = {
    position: 'absolute',
    width: '100%',
    height: '100%',
    top: 0,
    left: 0
};
/* eslint-disable @typescript-eslint/ban-ts-comment */ const viewChanged = (prevViewport, eventTransform)=>prevViewport.x !== eventTransform.x || prevViewport.y !== eventTransform.y || prevViewport.zoom !== eventTransform.k;
const eventToFlowTransform = (eventTransform)=>({
        x: eventTransform.x,
        y: eventTransform.y,
        zoom: eventTransform.k
    });
const isWrappedWithClass = (event, className)=>event.target.closest(`.${className}`);
const isRightClickPan = (panOnDrag, usedButton)=>usedButton === 2 && Array.isArray(panOnDrag) && panOnDrag.includes(2);
const wheelDelta = (event)=>{
    const factor = event.ctrlKey && isMacOs() ? 10 : 1;
    return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * factor;
};
const selector$a = (s)=>({
        d3Zoom: s.d3Zoom,
        d3Selection: s.d3Selection,
        d3ZoomHandler: s.d3ZoomHandler,
        userSelectionActive: s.userSelectionActive
    });
const ZoomPane = ({ onMove, onMoveStart, onMoveEnd, onPaneContextMenu, zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, elementsSelectable, panOnDrag = true, defaultViewport, translateExtent, minZoom, maxZoom, zoomActivationKeyCode, preventScrolling = true, children, noWheelClassName, noPanClassName })=>{
    const timerId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const store = useStoreApi();
    const isZoomingOrPanning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const zoomedWithRightMouseButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const zoomPane = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const prevTransform = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({
        x: 0,
        y: 0,
        zoom: 0
    });
    const { d3Zoom, d3Selection, d3ZoomHandler, userSelectionActive } = useStore(selector$a, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const zoomActivationKeyPressed = useKeyPress(zoomActivationKeyCode);
    const mouseButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(0);
    const isPanScrolling = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const panScrollTimeout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    useResizeHandler(zoomPane);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (zoomPane.current) {
            const bbox = zoomPane.current.getBoundingClientRect();
            const d3ZoomInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$zoom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__zoom$3e$__["zoom"])().scaleExtent([
                minZoom,
                maxZoom
            ]).translateExtent(translateExtent);
            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(zoomPane.current).call(d3ZoomInstance);
            const updatedTransform = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__identity__as__zoomIdentity$3e$__["zoomIdentity"].translate(defaultViewport.x, defaultViewport.y).scale(clamp(defaultViewport.zoom, minZoom, maxZoom));
            const extent = [
                [
                    0,
                    0
                ],
                [
                    bbox.width,
                    bbox.height
                ]
            ];
            const constrainedTransform = d3ZoomInstance.constrain()(updatedTransform, extent, translateExtent);
            d3ZoomInstance.transform(selection, constrainedTransform);
            d3ZoomInstance.wheelDelta(wheelDelta);
            store.setState({
                d3Zoom: d3ZoomInstance,
                d3Selection: selection,
                d3ZoomHandler: selection.on('wheel.zoom'),
                // we need to pass transform because zoom handler is not registered when we set the initial transform
                transform: [
                    constrainedTransform.x,
                    constrainedTransform.y,
                    constrainedTransform.k
                ],
                domNode: zoomPane.current.closest('.react-flow')
            });
        }
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (d3Selection && d3Zoom) {
            if (panOnScroll && !zoomActivationKeyPressed && !userSelectionActive) {
                d3Selection.on('wheel.zoom', (event)=>{
                    if (isWrappedWithClass(event, noWheelClassName)) {
                        return false;
                    }
                    event.preventDefault();
                    event.stopImmediatePropagation();
                    const currentZoom = d3Selection.property('__zoom').k || 1;
                    // macos and win set ctrlKey=true for pinch gesture on a trackpad
                    if (event.ctrlKey && zoomOnPinch) {
                        const point = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__pointer$3e$__["pointer"])(event);
                        const pinchDelta = wheelDelta(event);
                        const zoom = currentZoom * Math.pow(2, pinchDelta);
                        // @ts-ignore
                        d3Zoom.scaleTo(d3Selection, zoom, point, event);
                        return;
                    }
                    // increase scroll speed in firefox
                    // firefox: deltaMode === 1; chrome: deltaMode === 0
                    const deltaNormalize = event.deltaMode === 1 ? 20 : 1;
                    let deltaX = panOnScrollMode === PanOnScrollMode.Vertical ? 0 : event.deltaX * deltaNormalize;
                    let deltaY = panOnScrollMode === PanOnScrollMode.Horizontal ? 0 : event.deltaY * deltaNormalize;
                    // this enables vertical scrolling with shift + scroll on windows
                    if (!isMacOs() && event.shiftKey && panOnScrollMode !== PanOnScrollMode.Vertical) {
                        deltaX = event.deltaY * deltaNormalize;
                        deltaY = 0;
                    }
                    d3Zoom.translateBy(d3Selection, -(deltaX / currentZoom) * panOnScrollSpeed, -(deltaY / currentZoom) * panOnScrollSpeed, // @ts-ignore
                    {
                        internal: true
                    });
                    const nextViewport = eventToFlowTransform(d3Selection.property('__zoom'));
                    const { onViewportChangeStart, onViewportChange, onViewportChangeEnd } = store.getState();
                    clearTimeout(panScrollTimeout.current);
                    // for pan on scroll we need to handle the event calls on our own
                    // we can't use the start, zoom and end events from d3-zoom
                    // because start and move gets called on every scroll event and not once at the beginning
                    if (!isPanScrolling.current) {
                        isPanScrolling.current = true;
                        onMoveStart?.(event, nextViewport);
                        onViewportChangeStart?.(nextViewport);
                    }
                    if (isPanScrolling.current) {
                        onMove?.(event, nextViewport);
                        onViewportChange?.(nextViewport);
                        panScrollTimeout.current = setTimeout(()=>{
                            onMoveEnd?.(event, nextViewport);
                            onViewportChangeEnd?.(nextViewport);
                            isPanScrolling.current = false;
                        }, 150);
                    }
                }, {
                    passive: false
                });
            } else if (typeof d3ZoomHandler !== 'undefined') {
                d3Selection.on('wheel.zoom', function(event, d) {
                    // we still want to enable pinch zooming even if preventScrolling is set to false
                    const invalidEvent = !preventScrolling && event.type === 'wheel' && !event.ctrlKey;
                    if (invalidEvent || isWrappedWithClass(event, noWheelClassName)) {
                        return null;
                    }
                    event.preventDefault();
                    d3ZoomHandler.call(this, event, d);
                }, {
                    passive: false
                });
            }
        }
    }, [
        userSelectionActive,
        panOnScroll,
        panOnScrollMode,
        d3Selection,
        d3Zoom,
        d3ZoomHandler,
        zoomActivationKeyPressed,
        zoomOnPinch,
        preventScrolling,
        noWheelClassName,
        onMoveStart,
        onMove,
        onMoveEnd
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (d3Zoom) {
            d3Zoom.on('start', (event)=>{
                if (!event.sourceEvent || event.sourceEvent.internal) {
                    return null;
                }
                // we need to remember it here, because it's always 0 in the "zoom" event
                mouseButton.current = event.sourceEvent?.button;
                const { onViewportChangeStart } = store.getState();
                const flowTransform = eventToFlowTransform(event.transform);
                isZoomingOrPanning.current = true;
                prevTransform.current = flowTransform;
                if (event.sourceEvent?.type === 'mousedown') {
                    store.setState({
                        paneDragging: true
                    });
                }
                onViewportChangeStart?.(flowTransform);
                onMoveStart?.(event.sourceEvent, flowTransform);
            });
        }
    }, [
        d3Zoom,
        onMoveStart
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (d3Zoom) {
            if (userSelectionActive && !isZoomingOrPanning.current) {
                d3Zoom.on('zoom', null);
            } else if (!userSelectionActive) {
                d3Zoom.on('zoom', (event)=>{
                    const { onViewportChange } = store.getState();
                    store.setState({
                        transform: [
                            event.transform.x,
                            event.transform.y,
                            event.transform.k
                        ]
                    });
                    zoomedWithRightMouseButton.current = !!(onPaneContextMenu && isRightClickPan(panOnDrag, mouseButton.current ?? 0));
                    if ((onMove || onViewportChange) && !event.sourceEvent?.internal) {
                        const flowTransform = eventToFlowTransform(event.transform);
                        onViewportChange?.(flowTransform);
                        onMove?.(event.sourceEvent, flowTransform);
                    }
                });
            }
        }
    }, [
        userSelectionActive,
        d3Zoom,
        onMove,
        panOnDrag,
        onPaneContextMenu
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (d3Zoom) {
            d3Zoom.on('end', (event)=>{
                if (!event.sourceEvent || event.sourceEvent.internal) {
                    return null;
                }
                const { onViewportChangeEnd } = store.getState();
                isZoomingOrPanning.current = false;
                store.setState({
                    paneDragging: false
                });
                if (onPaneContextMenu && isRightClickPan(panOnDrag, mouseButton.current ?? 0) && !zoomedWithRightMouseButton.current) {
                    onPaneContextMenu(event.sourceEvent);
                }
                zoomedWithRightMouseButton.current = false;
                if ((onMoveEnd || onViewportChangeEnd) && viewChanged(prevTransform.current, event.transform)) {
                    const flowTransform = eventToFlowTransform(event.transform);
                    prevTransform.current = flowTransform;
                    clearTimeout(timerId.current);
                    timerId.current = setTimeout(()=>{
                        onViewportChangeEnd?.(flowTransform);
                        onMoveEnd?.(event.sourceEvent, flowTransform);
                    }, panOnScroll ? 150 : 0);
                }
            });
        }
    }, [
        d3Zoom,
        panOnScroll,
        panOnDrag,
        onMoveEnd,
        onPaneContextMenu
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (d3Zoom) {
            d3Zoom.filter((event)=>{
                const zoomScroll = zoomActivationKeyPressed || zoomOnScroll;
                const pinchZoom = zoomOnPinch && event.ctrlKey;
                if ((panOnDrag === true || Array.isArray(panOnDrag) && panOnDrag.includes(1)) && event.button === 1 && event.type === 'mousedown' && (isWrappedWithClass(event, 'react-flow__node') || isWrappedWithClass(event, 'react-flow__edge'))) {
                    return true;
                }
                // if all interactions are disabled, we prevent all zoom events
                if (!panOnDrag && !zoomScroll && !panOnScroll && !zoomOnDoubleClick && !zoomOnPinch) {
                    return false;
                }
                // during a selection we prevent all other interactions
                if (userSelectionActive) {
                    return false;
                }
                // if zoom on double click is disabled, we prevent the double click event
                if (!zoomOnDoubleClick && event.type === 'dblclick') {
                    return false;
                }
                // if the target element is inside an element with the nowheel class, we prevent zooming
                if (isWrappedWithClass(event, noWheelClassName) && event.type === 'wheel') {
                    return false;
                }
                // if the target element is inside an element with the nopan class, we prevent panning
                if (isWrappedWithClass(event, noPanClassName) && (event.type !== 'wheel' || panOnScroll && event.type === 'wheel' && !zoomActivationKeyPressed)) {
                    return false;
                }
                if (!zoomOnPinch && event.ctrlKey && event.type === 'wheel') {
                    return false;
                }
                // when there is no scroll handling enabled, we prevent all wheel events
                if (!zoomScroll && !panOnScroll && !pinchZoom && event.type === 'wheel') {
                    return false;
                }
                // if the pane is not movable, we prevent dragging it with mousestart or touchstart
                if (!panOnDrag && (event.type === 'mousedown' || event.type === 'touchstart')) {
                    return false;
                }
                // if the pane is only movable using allowed clicks
                if (Array.isArray(panOnDrag) && !panOnDrag.includes(event.button) && event.type === 'mousedown') {
                    return false;
                }
                // We only allow right clicks if pan on drag is set to right click
                const buttonAllowed = Array.isArray(panOnDrag) && panOnDrag.includes(event.button) || !event.button || event.button <= 1;
                // default filter for d3-zoom
                return (!event.ctrlKey || event.type === 'wheel') && buttonAllowed;
            });
        }
    }, [
        userSelectionActive,
        d3Zoom,
        zoomOnScroll,
        zoomOnPinch,
        panOnScroll,
        zoomOnDoubleClick,
        panOnDrag,
        elementsSelectable,
        zoomActivationKeyPressed
    ]);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "react-flow__renderer",
        ref: zoomPane,
        style: containerStyle
    }, children);
};
const selector$9 = (s)=>({
        userSelectionActive: s.userSelectionActive,
        userSelectionRect: s.userSelectionRect
    });
function UserSelection() {
    const { userSelectionActive, userSelectionRect } = useStore(selector$9, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const isActive = userSelectionActive && userSelectionRect;
    if (!isActive) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "react-flow__selection react-flow__container",
        style: {
            width: userSelectionRect.width,
            height: userSelectionRect.height,
            transform: `translate(${userSelectionRect.x}px, ${userSelectionRect.y}px)`
        }
    });
}
function handleParentExpand(res, updateItem) {
    const parentId = updateItem.parentNode || updateItem.parentId;
    const parent = res.find((e)=>e.id === parentId);
    if (parent) {
        const extendWidth = updateItem.position.x + updateItem.width - parent.width;
        const extendHeight = updateItem.position.y + updateItem.height - parent.height;
        if (extendWidth > 0 || extendHeight > 0 || updateItem.position.x < 0 || updateItem.position.y < 0) {
            parent.style = {
                ...parent.style
            } || {};
            parent.style.width = parent.style.width ?? parent.width;
            parent.style.height = parent.style.height ?? parent.height;
            if (extendWidth > 0) {
                parent.style.width += extendWidth;
            }
            if (extendHeight > 0) {
                parent.style.height += extendHeight;
            }
            if (updateItem.position.x < 0) {
                const xDiff = Math.abs(updateItem.position.x);
                parent.position.x = parent.position.x - xDiff;
                parent.style.width += xDiff;
                updateItem.position.x = 0;
            }
            if (updateItem.position.y < 0) {
                const yDiff = Math.abs(updateItem.position.y);
                parent.position.y = parent.position.y - yDiff;
                parent.style.height += yDiff;
                updateItem.position.y = 0;
            }
            parent.width = parent.style.width;
            parent.height = parent.style.height;
        }
    }
}
function applyChanges(changes, elements) {
    // we need this hack to handle the setNodes and setEdges function of the useReactFlow hook for controlled flows
    if (changes.some((c)=>c.type === 'reset')) {
        return changes.filter((c)=>c.type === 'reset').map((c)=>c.item);
    }
    const initElements = changes.filter((c)=>c.type === 'add').map((c)=>c.item);
    return elements.reduce((res, item)=>{
        const currentChanges = changes.filter((c)=>c.id === item.id);
        if (currentChanges.length === 0) {
            res.push(item);
            return res;
        }
        const updateItem = {
            ...item
        };
        for (const currentChange of currentChanges){
            if (currentChange) {
                switch(currentChange.type){
                    case 'select':
                        {
                            updateItem.selected = currentChange.selected;
                            break;
                        }
                    case 'position':
                        {
                            if (typeof currentChange.position !== 'undefined') {
                                updateItem.position = currentChange.position;
                            }
                            if (typeof currentChange.positionAbsolute !== 'undefined') {
                                updateItem.positionAbsolute = currentChange.positionAbsolute;
                            }
                            if (typeof currentChange.dragging !== 'undefined') {
                                updateItem.dragging = currentChange.dragging;
                            }
                            if (updateItem.expandParent) {
                                handleParentExpand(res, updateItem);
                            }
                            break;
                        }
                    case 'dimensions':
                        {
                            if (typeof currentChange.dimensions !== 'undefined') {
                                updateItem.width = currentChange.dimensions.width;
                                updateItem.height = currentChange.dimensions.height;
                            }
                            if (typeof currentChange.updateStyle !== 'undefined') {
                                updateItem.style = {
                                    ...updateItem.style || {},
                                    ...currentChange.dimensions
                                };
                            }
                            if (typeof currentChange.resizing === 'boolean') {
                                updateItem.resizing = currentChange.resizing;
                            }
                            if (updateItem.expandParent) {
                                handleParentExpand(res, updateItem);
                            }
                            break;
                        }
                    case 'remove':
                        {
                            return res;
                        }
                }
            }
        }
        res.push(updateItem);
        return res;
    }, initElements);
}
function applyNodeChanges(changes, nodes) {
    return applyChanges(changes, nodes);
}
function applyEdgeChanges(changes, edges) {
    return applyChanges(changes, edges);
}
const createSelectionChange = (id, selected)=>({
        id,
        type: 'select',
        selected
    });
function getSelectionChanges(items, selectedIds) {
    return items.reduce((res, item)=>{
        const willBeSelected = selectedIds.includes(item.id);
        if (!item.selected && willBeSelected) {
            item.selected = true;
            res.push(createSelectionChange(item.id, true));
        } else if (item.selected && !willBeSelected) {
            item.selected = false;
            res.push(createSelectionChange(item.id, false));
        }
        return res;
    }, []);
}
/**
 * The user selection rectangle gets displayed when a user drags the mouse while pressing shift
 */ const wrapHandler = (handler, containerRef)=>{
    return (event)=>{
        if (event.target !== containerRef.current) {
            return;
        }
        handler?.(event);
    };
};
const selector$8 = (s)=>({
        userSelectionActive: s.userSelectionActive,
        elementsSelectable: s.elementsSelectable,
        dragging: s.paneDragging
    });
const Pane = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(({ isSelecting, selectionMode = SelectionMode.Full, panOnDrag, onSelectionStart, onSelectionEnd, onPaneClick, onPaneContextMenu, onPaneScroll, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, children })=>{
    const container = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const store = useStoreApi();
    const prevSelectedNodesCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(0);
    const prevSelectedEdgesCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(0);
    const containerBounds = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const { userSelectionActive, elementsSelectable, dragging } = useStore(selector$8, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const resetUserSelection = ()=>{
        store.setState({
            userSelectionActive: false,
            userSelectionRect: null
        });
        prevSelectedNodesCount.current = 0;
        prevSelectedEdgesCount.current = 0;
    };
    const onClick = (event)=>{
        onPaneClick?.(event);
        store.getState().resetSelectedElements();
        store.setState({
            nodesSelectionActive: false
        });
    };
    const onContextMenu = (event)=>{
        if (Array.isArray(panOnDrag) && panOnDrag?.includes(2)) {
            event.preventDefault();
            return;
        }
        onPaneContextMenu?.(event);
    };
    const onWheel = onPaneScroll ? (event)=>onPaneScroll(event) : undefined;
    const onMouseDown = (event)=>{
        const { resetSelectedElements, domNode } = store.getState();
        containerBounds.current = domNode?.getBoundingClientRect();
        if (!elementsSelectable || !isSelecting || event.button !== 0 || event.target !== container.current || !containerBounds.current) {
            return;
        }
        const { x, y } = getEventPosition(event, containerBounds.current);
        resetSelectedElements();
        store.setState({
            userSelectionRect: {
                width: 0,
                height: 0,
                startX: x,
                startY: y,
                x,
                y
            }
        });
        onSelectionStart?.(event);
    };
    const onMouseMove = (event)=>{
        const { userSelectionRect, nodeInternals, edges, transform, onNodesChange, onEdgesChange, nodeOrigin, getNodes } = store.getState();
        if (!isSelecting || !containerBounds.current || !userSelectionRect) {
            return;
        }
        store.setState({
            userSelectionActive: true,
            nodesSelectionActive: false
        });
        const mousePos = getEventPosition(event, containerBounds.current);
        const startX = userSelectionRect.startX ?? 0;
        const startY = userSelectionRect.startY ?? 0;
        const nextUserSelectRect = {
            ...userSelectionRect,
            x: mousePos.x < startX ? mousePos.x : startX,
            y: mousePos.y < startY ? mousePos.y : startY,
            width: Math.abs(mousePos.x - startX),
            height: Math.abs(mousePos.y - startY)
        };
        const nodes = getNodes();
        const selectedNodes = getNodesInside(nodeInternals, nextUserSelectRect, transform, selectionMode === SelectionMode.Partial, true, nodeOrigin);
        const selectedEdgeIds = getConnectedEdges(selectedNodes, edges).map((e)=>e.id);
        const selectedNodeIds = selectedNodes.map((n)=>n.id);
        if (prevSelectedNodesCount.current !== selectedNodeIds.length) {
            prevSelectedNodesCount.current = selectedNodeIds.length;
            const changes = getSelectionChanges(nodes, selectedNodeIds);
            if (changes.length) {
                onNodesChange?.(changes);
            }
        }
        if (prevSelectedEdgesCount.current !== selectedEdgeIds.length) {
            prevSelectedEdgesCount.current = selectedEdgeIds.length;
            const changes = getSelectionChanges(edges, selectedEdgeIds);
            if (changes.length) {
                onEdgesChange?.(changes);
            }
        }
        store.setState({
            userSelectionRect: nextUserSelectRect
        });
    };
    const onMouseUp = (event)=>{
        if (event.button !== 0) {
            return;
        }
        const { userSelectionRect } = store.getState();
        // We only want to trigger click functions when in selection mode if
        // the user did not move the mouse.
        if (!userSelectionActive && userSelectionRect && event.target === container.current) {
            onClick?.(event);
        }
        store.setState({
            nodesSelectionActive: prevSelectedNodesCount.current > 0
        });
        resetUserSelection();
        onSelectionEnd?.(event);
    };
    const onMouseLeave = (event)=>{
        if (userSelectionActive) {
            store.setState({
                nodesSelectionActive: prevSelectedNodesCount.current > 0
            });
            onSelectionEnd?.(event);
        }
        resetUserSelection();
    };
    const hasActiveSelection = elementsSelectable && (isSelecting || userSelectionActive);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
            'react-flow__pane',
            {
                dragging,
                selection: isSelecting
            }
        ]),
        onClick: hasActiveSelection ? undefined : wrapHandler(onClick, container),
        onContextMenu: wrapHandler(onContextMenu, container),
        onWheel: wrapHandler(onWheel, container),
        onMouseEnter: hasActiveSelection ? undefined : onPaneMouseEnter,
        onMouseDown: hasActiveSelection ? onMouseDown : undefined,
        onMouseMove: hasActiveSelection ? onMouseMove : onPaneMouseMove,
        onMouseUp: hasActiveSelection ? onMouseUp : undefined,
        onMouseLeave: hasActiveSelection ? onMouseLeave : onPaneMouseLeave,
        ref: container,
        style: containerStyle
    }, children, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(UserSelection, null));
});
Pane.displayName = 'Pane';
function isParentSelected(node, nodeInternals) {
    const parentId = node.parentNode || node.parentId;
    if (!parentId) {
        return false;
    }
    const parentNode = nodeInternals.get(parentId);
    if (!parentNode) {
        return false;
    }
    if (parentNode.selected) {
        return true;
    }
    return isParentSelected(parentNode, nodeInternals);
}
function hasSelector(target, selector, nodeRef) {
    let current = target;
    do {
        if (current?.matches(selector)) return true;
        if (current === nodeRef.current) return false;
        current = current.parentElement;
    }while (current)
    return false;
}
// looks for all selected nodes and created a NodeDragItem for each of them
function getDragItems(nodeInternals, nodesDraggable, mousePos, nodeId) {
    return Array.from(nodeInternals.values()).filter((n)=>(n.selected || n.id === nodeId) && (!n.parentNode || n.parentId || !isParentSelected(n, nodeInternals)) && (n.draggable || nodesDraggable && typeof n.draggable === 'undefined')).map((n)=>({
            id: n.id,
            position: n.position || {
                x: 0,
                y: 0
            },
            positionAbsolute: n.positionAbsolute || {
                x: 0,
                y: 0
            },
            distance: {
                x: mousePos.x - (n.positionAbsolute?.x ?? 0),
                y: mousePos.y - (n.positionAbsolute?.y ?? 0)
            },
            delta: {
                x: 0,
                y: 0
            },
            extent: n.extent,
            parentNode: n.parentNode || n.parentId,
            parentId: n.parentNode || n.parentId,
            width: n.width,
            height: n.height,
            expandParent: n.expandParent
        }));
}
function clampNodeExtent(node, extent) {
    if (!extent || extent === 'parent') {
        return extent;
    }
    return [
        extent[0],
        [
            extent[1][0] - (node.width || 0),
            extent[1][1] - (node.height || 0)
        ]
    ];
}
function calcNextPosition(node, nextPosition, nodeInternals, nodeExtent, nodeOrigin = [
    0,
    0
], onError) {
    const clampedNodeExtent = clampNodeExtent(node, node.extent || nodeExtent);
    let currentExtent = clampedNodeExtent;
    const parentId = node.parentNode || node.parentId;
    if (node.extent === 'parent' && !node.expandParent) {
        if (parentId && node.width && node.height) {
            const parent = nodeInternals.get(parentId);
            const { x: parentX, y: parentY } = getNodePositionWithOrigin(parent, nodeOrigin).positionAbsolute;
            currentExtent = parent && isNumeric(parentX) && isNumeric(parentY) && isNumeric(parent.width) && isNumeric(parent.height) ? [
                [
                    parentX + node.width * nodeOrigin[0],
                    parentY + node.height * nodeOrigin[1]
                ],
                [
                    parentX + parent.width - node.width + node.width * nodeOrigin[0],
                    parentY + parent.height - node.height + node.height * nodeOrigin[1]
                ]
            ] : currentExtent;
        } else {
            onError?.('005', errorMessages['error005']());
            currentExtent = clampedNodeExtent;
        }
    } else if (node.extent && parentId && node.extent !== 'parent') {
        const parent = nodeInternals.get(parentId);
        const { x: parentX, y: parentY } = getNodePositionWithOrigin(parent, nodeOrigin).positionAbsolute;
        currentExtent = [
            [
                node.extent[0][0] + parentX,
                node.extent[0][1] + parentY
            ],
            [
                node.extent[1][0] + parentX,
                node.extent[1][1] + parentY
            ]
        ];
    }
    let parentPosition = {
        x: 0,
        y: 0
    };
    if (parentId) {
        const parentNode = nodeInternals.get(parentId);
        parentPosition = getNodePositionWithOrigin(parentNode, nodeOrigin).positionAbsolute;
    }
    const positionAbsolute = currentExtent && currentExtent !== 'parent' ? clampPosition(nextPosition, currentExtent) : nextPosition;
    return {
        position: {
            x: positionAbsolute.x - parentPosition.x,
            y: positionAbsolute.y - parentPosition.y
        },
        positionAbsolute
    };
}
// returns two params:
// 1. the dragged node (or the first of the list, if we are dragging a node selection)
// 2. array of selected nodes (for multi selections)
function getEventHandlerParams({ nodeId, dragItems, nodeInternals }) {
    const extentedDragItems = dragItems.map((n)=>{
        const node = nodeInternals.get(n.id);
        return {
            ...node,
            position: n.position,
            positionAbsolute: n.positionAbsolute
        };
    });
    return [
        nodeId ? extentedDragItems.find((n)=>n.id === nodeId) : extentedDragItems[0],
        extentedDragItems
    ];
}
const getHandleBounds = (selector, nodeElement, zoom, nodeOrigin)=>{
    const handles = nodeElement.querySelectorAll(selector);
    if (!handles || !handles.length) {
        return null;
    }
    const handlesArray = Array.from(handles);
    const nodeBounds = nodeElement.getBoundingClientRect();
    const nodeOffset = {
        x: nodeBounds.width * nodeOrigin[0],
        y: nodeBounds.height * nodeOrigin[1]
    };
    return handlesArray.map((handle)=>{
        const handleBounds = handle.getBoundingClientRect();
        return {
            id: handle.getAttribute('data-handleid'),
            position: handle.getAttribute('data-handlepos'),
            x: (handleBounds.left - nodeBounds.left - nodeOffset.x) / zoom,
            y: (handleBounds.top - nodeBounds.top - nodeOffset.y) / zoom,
            ...getDimensions(handle)
        };
    });
};
function getMouseHandler(id, getState, handler) {
    return handler === undefined ? handler : (event)=>{
        const node = getState().nodeInternals.get(id);
        if (node) {
            handler(event, {
                ...node
            });
        }
    };
}
// this handler is called by
// 1. the click handler when node is not draggable or selectNodesOnDrag = false
// or
// 2. the on drag start handler when node is draggable and selectNodesOnDrag = true
function handleNodeClick({ id, store, unselect = false, nodeRef }) {
    const { addSelectedNodes, unselectNodesAndEdges, multiSelectionActive, nodeInternals, onError } = store.getState();
    const node = nodeInternals.get(id);
    if (!node) {
        onError?.('012', errorMessages['error012'](id));
        return;
    }
    store.setState({
        nodesSelectionActive: false
    });
    if (!node.selected) {
        addSelectedNodes([
            id
        ]);
    } else if (unselect || node.selected && multiSelectionActive) {
        unselectNodesAndEdges({
            nodes: [
                node
            ],
            edges: []
        });
        requestAnimationFrame(()=>nodeRef?.current?.blur());
    }
}
function useGetPointerPosition() {
    const store = useStoreApi();
    // returns the pointer position projected to the RF coordinate system
    const getPointerPosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(({ sourceEvent })=>{
        const { transform, snapGrid, snapToGrid } = store.getState();
        const x = sourceEvent.touches ? sourceEvent.touches[0].clientX : sourceEvent.clientX;
        const y = sourceEvent.touches ? sourceEvent.touches[0].clientY : sourceEvent.clientY;
        const pointerPos = {
            x: (x - transform[0]) / transform[2],
            y: (y - transform[1]) / transform[2]
        };
        // we need the snapped position in order to be able to skip unnecessary drag events
        return {
            xSnapped: snapToGrid ? snapGrid[0] * Math.round(pointerPos.x / snapGrid[0]) : pointerPos.x,
            ySnapped: snapToGrid ? snapGrid[1] * Math.round(pointerPos.y / snapGrid[1]) : pointerPos.y,
            ...pointerPos
        };
    }, []);
    return getPointerPosition;
}
function wrapSelectionDragFunc(selectionFunc) {
    return (event, _, nodes)=>selectionFunc?.(event, nodes);
}
function useDrag({ nodeRef, disabled = false, noDragClassName, handleSelector, nodeId, isSelectable, selectNodesOnDrag }) {
    const store = useStoreApi();
    const [dragging, setDragging] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const dragItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]);
    const lastPos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({
        x: null,
        y: null
    });
    const autoPanId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(0);
    const containerBounds = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mousePosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({
        x: 0,
        y: 0
    });
    const dragEvent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const autoPanStarted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const dragStarted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const abortDrag = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    const getPointerPosition = useGetPointerPosition();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (nodeRef?.current) {
            const selection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$selection$2f$src$2f$select$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__select$3e$__["select"])(nodeRef.current);
            const updateNodes = ({ x, y })=>{
                const { nodeInternals, onNodeDrag, onSelectionDrag, updateNodePositions, nodeExtent, snapGrid, snapToGrid, nodeOrigin, onError } = store.getState();
                lastPos.current = {
                    x,
                    y
                };
                let hasChange = false;
                let nodesBox = {
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 0
                };
                if (dragItems.current.length > 1 && nodeExtent) {
                    const rect = getNodesBounds(dragItems.current, nodeOrigin);
                    nodesBox = rectToBox(rect);
                }
                dragItems.current = dragItems.current.map((n)=>{
                    const nextPosition = {
                        x: x - n.distance.x,
                        y: y - n.distance.y
                    };
                    if (snapToGrid) {
                        nextPosition.x = snapGrid[0] * Math.round(nextPosition.x / snapGrid[0]);
                        nextPosition.y = snapGrid[1] * Math.round(nextPosition.y / snapGrid[1]);
                    }
                    // if there is selection with multiple nodes and a node extent is set, we need to adjust the node extent for each node
                    // based on its position so that the node stays at it's position relative to the selection.
                    const adjustedNodeExtent = [
                        [
                            nodeExtent[0][0],
                            nodeExtent[0][1]
                        ],
                        [
                            nodeExtent[1][0],
                            nodeExtent[1][1]
                        ]
                    ];
                    if (dragItems.current.length > 1 && nodeExtent && !n.extent) {
                        adjustedNodeExtent[0][0] = n.positionAbsolute.x - nodesBox.x + nodeExtent[0][0];
                        adjustedNodeExtent[1][0] = n.positionAbsolute.x + (n.width ?? 0) - nodesBox.x2 + nodeExtent[1][0];
                        adjustedNodeExtent[0][1] = n.positionAbsolute.y - nodesBox.y + nodeExtent[0][1];
                        adjustedNodeExtent[1][1] = n.positionAbsolute.y + (n.height ?? 0) - nodesBox.y2 + nodeExtent[1][1];
                    }
                    const updatedPos = calcNextPosition(n, nextPosition, nodeInternals, adjustedNodeExtent, nodeOrigin, onError);
                    // we want to make sure that we only fire a change event when there is a change
                    hasChange = hasChange || n.position.x !== updatedPos.position.x || n.position.y !== updatedPos.position.y;
                    n.position = updatedPos.position;
                    n.positionAbsolute = updatedPos.positionAbsolute;
                    return n;
                });
                if (!hasChange) {
                    return;
                }
                updateNodePositions(dragItems.current, true, true);
                setDragging(true);
                const onDrag = nodeId ? onNodeDrag : wrapSelectionDragFunc(onSelectionDrag);
                if (onDrag && dragEvent.current) {
                    const [currentNode, nodes] = getEventHandlerParams({
                        nodeId,
                        dragItems: dragItems.current,
                        nodeInternals
                    });
                    onDrag(dragEvent.current, currentNode, nodes);
                }
            };
            const autoPan = ()=>{
                if (!containerBounds.current) {
                    return;
                }
                const [xMovement, yMovement] = calcAutoPan(mousePosition.current, containerBounds.current);
                if (xMovement !== 0 || yMovement !== 0) {
                    const { transform, panBy } = store.getState();
                    lastPos.current.x = (lastPos.current.x ?? 0) - xMovement / transform[2];
                    lastPos.current.y = (lastPos.current.y ?? 0) - yMovement / transform[2];
                    if (panBy({
                        x: xMovement,
                        y: yMovement
                    })) {
                        updateNodes(lastPos.current);
                    }
                }
                autoPanId.current = requestAnimationFrame(autoPan);
            };
            const startDrag = (event)=>{
                const { nodeInternals, multiSelectionActive, nodesDraggable, unselectNodesAndEdges, onNodeDragStart, onSelectionDragStart } = store.getState();
                dragStarted.current = true;
                const onStart = nodeId ? onNodeDragStart : wrapSelectionDragFunc(onSelectionDragStart);
                if ((!selectNodesOnDrag || !isSelectable) && !multiSelectionActive && nodeId) {
                    if (!nodeInternals.get(nodeId)?.selected) {
                        // we need to reset selected nodes when selectNodesOnDrag=false
                        unselectNodesAndEdges();
                    }
                }
                if (nodeId && isSelectable && selectNodesOnDrag) {
                    handleNodeClick({
                        id: nodeId,
                        store,
                        nodeRef: nodeRef
                    });
                }
                const pointerPos = getPointerPosition(event);
                lastPos.current = pointerPos;
                dragItems.current = getDragItems(nodeInternals, nodesDraggable, pointerPos, nodeId);
                if (onStart && dragItems.current) {
                    const [currentNode, nodes] = getEventHandlerParams({
                        nodeId,
                        dragItems: dragItems.current,
                        nodeInternals
                    });
                    onStart(event.sourceEvent, currentNode, nodes);
                }
            };
            if (disabled) {
                selection.on('.drag', null);
            } else {
                const dragHandler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$drag$2f$src$2f$drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__drag$3e$__["drag"])().on('start', (event)=>{
                    const { domNode, nodeDragThreshold } = store.getState();
                    if (nodeDragThreshold === 0) {
                        startDrag(event);
                    }
                    abortDrag.current = false;
                    const pointerPos = getPointerPosition(event);
                    lastPos.current = pointerPos;
                    containerBounds.current = domNode?.getBoundingClientRect() || null;
                    mousePosition.current = getEventPosition(event.sourceEvent, containerBounds.current);
                }).on('drag', (event)=>{
                    const pointerPos = getPointerPosition(event);
                    const { autoPanOnNodeDrag, nodeDragThreshold } = store.getState();
                    if (event.sourceEvent.type === 'touchmove' && event.sourceEvent.touches.length > 1) {
                        abortDrag.current = true;
                    }
                    if (abortDrag.current) {
                        return;
                    }
                    if (!autoPanStarted.current && dragStarted.current && autoPanOnNodeDrag) {
                        autoPanStarted.current = true;
                        autoPan();
                    }
                    if (!dragStarted.current) {
                        const x = pointerPos.xSnapped - (lastPos?.current?.x ?? 0);
                        const y = pointerPos.ySnapped - (lastPos?.current?.y ?? 0);
                        const distance = Math.sqrt(x * x + y * y);
                        if (distance > nodeDragThreshold) {
                            startDrag(event);
                        }
                    }
                    // skip events without movement
                    if ((lastPos.current.x !== pointerPos.xSnapped || lastPos.current.y !== pointerPos.ySnapped) && dragItems.current && dragStarted.current) {
                        dragEvent.current = event.sourceEvent;
                        mousePosition.current = getEventPosition(event.sourceEvent, containerBounds.current);
                        updateNodes(pointerPos);
                    }
                }).on('end', (event)=>{
                    if (!dragStarted.current || abortDrag.current) {
                        return;
                    }
                    setDragging(false);
                    autoPanStarted.current = false;
                    dragStarted.current = false;
                    cancelAnimationFrame(autoPanId.current);
                    if (dragItems.current) {
                        const { updateNodePositions, nodeInternals, onNodeDragStop, onSelectionDragStop } = store.getState();
                        const onStop = nodeId ? onNodeDragStop : wrapSelectionDragFunc(onSelectionDragStop);
                        updateNodePositions(dragItems.current, false, false);
                        if (onStop) {
                            const [currentNode, nodes] = getEventHandlerParams({
                                nodeId,
                                dragItems: dragItems.current,
                                nodeInternals
                            });
                            onStop(event.sourceEvent, currentNode, nodes);
                        }
                    }
                }).filter((event)=>{
                    const target = event.target;
                    const isDraggable = !event.button && (!noDragClassName || !hasSelector(target, `.${noDragClassName}`, nodeRef)) && (!handleSelector || hasSelector(target, handleSelector, nodeRef));
                    return isDraggable;
                });
                selection.call(dragHandler);
                return ()=>{
                    selection.on('.drag', null);
                };
            }
        }
    }, [
        nodeRef,
        disabled,
        noDragClassName,
        handleSelector,
        isSelectable,
        store,
        nodeId,
        selectNodesOnDrag,
        getPointerPosition
    ]);
    return dragging;
}
function useUpdateNodePositions() {
    const store = useStoreApi();
    const updatePositions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((params)=>{
        const { nodeInternals, nodeExtent, updateNodePositions, getNodes, snapToGrid, snapGrid, onError, nodesDraggable } = store.getState();
        const selectedNodes = getNodes().filter((n)=>n.selected && (n.draggable || nodesDraggable && typeof n.draggable === 'undefined'));
        // by default a node moves 5px on each key press, or 20px if shift is pressed
        // if snap grid is enabled, we use that for the velocity.
        const xVelo = snapToGrid ? snapGrid[0] : 5;
        const yVelo = snapToGrid ? snapGrid[1] : 5;
        const factor = params.isShiftPressed ? 4 : 1;
        const positionDiffX = params.x * xVelo * factor;
        const positionDiffY = params.y * yVelo * factor;
        const nodeUpdates = selectedNodes.map((n)=>{
            if (n.positionAbsolute) {
                const nextPosition = {
                    x: n.positionAbsolute.x + positionDiffX,
                    y: n.positionAbsolute.y + positionDiffY
                };
                if (snapToGrid) {
                    nextPosition.x = snapGrid[0] * Math.round(nextPosition.x / snapGrid[0]);
                    nextPosition.y = snapGrid[1] * Math.round(nextPosition.y / snapGrid[1]);
                }
                const { positionAbsolute, position } = calcNextPosition(n, nextPosition, nodeInternals, nodeExtent, undefined, onError);
                n.position = position;
                n.positionAbsolute = positionAbsolute;
            }
            return n;
        });
        updateNodePositions(nodeUpdates, true, false);
    }, []);
    return updatePositions;
}
const arrowKeyDiffs = {
    ArrowUp: {
        x: 0,
        y: -1
    },
    ArrowDown: {
        x: 0,
        y: 1
    },
    ArrowLeft: {
        x: -1,
        y: 0
    },
    ArrowRight: {
        x: 1,
        y: 0
    }
};
var wrapNode = (NodeComponent)=>{
    const NodeWrapper = ({ id, type, data, xPos, yPos, xPosOrigin, yPosOrigin, selected, onClick, onMouseEnter, onMouseMove, onMouseLeave, onContextMenu, onDoubleClick, style, className, isDraggable, isSelectable, isConnectable, isFocusable, selectNodesOnDrag, sourcePosition, targetPosition, hidden, resizeObserver, dragHandle, zIndex, isParent, noDragClassName, noPanClassName, initialized, disableKeyboardA11y, ariaLabel, rfId, hasHandleBounds })=>{
        const store = useStoreApi();
        const nodeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
        const prevNodeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
        const prevSourcePosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(sourcePosition);
        const prevTargetPosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(targetPosition);
        const prevType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(type);
        const hasPointerEvents = isSelectable || isDraggable || onClick || onMouseEnter || onMouseMove || onMouseLeave;
        const updatePositions = useUpdateNodePositions();
        const onMouseEnterHandler = getMouseHandler(id, store.getState, onMouseEnter);
        const onMouseMoveHandler = getMouseHandler(id, store.getState, onMouseMove);
        const onMouseLeaveHandler = getMouseHandler(id, store.getState, onMouseLeave);
        const onContextMenuHandler = getMouseHandler(id, store.getState, onContextMenu);
        const onDoubleClickHandler = getMouseHandler(id, store.getState, onDoubleClick);
        const onSelectNodeHandler = (event)=>{
            const { nodeDragThreshold } = store.getState();
            if (isSelectable && (!selectNodesOnDrag || !isDraggable || nodeDragThreshold > 0)) {
                // this handler gets called within the drag start event when selectNodesOnDrag=true
                handleNodeClick({
                    id,
                    store,
                    nodeRef
                });
            }
            if (onClick) {
                const node = store.getState().nodeInternals.get(id);
                if (node) {
                    onClick(event, {
                        ...node
                    });
                }
            }
        };
        const onKeyDown = (event)=>{
            if (isInputDOMNode(event)) {
                return;
            }
            if (disableKeyboardA11y) {
                return;
            }
            if (elementSelectionKeys.includes(event.key) && isSelectable) {
                const unselect = event.key === 'Escape';
                handleNodeClick({
                    id,
                    store,
                    unselect,
                    nodeRef
                });
            } else if (isDraggable && selected && Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {
                store.setState({
                    ariaLiveMessage: `Moved selected node ${event.key.replace('Arrow', '').toLowerCase()}. New position, x: ${~~xPos}, y: ${~~yPos}`
                });
                updatePositions({
                    x: arrowKeyDiffs[event.key].x,
                    y: arrowKeyDiffs[event.key].y,
                    isShiftPressed: event.shiftKey
                });
            }
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
            return ()=>{
                if (prevNodeRef.current) {
                    resizeObserver?.unobserve(prevNodeRef.current);
                    prevNodeRef.current = null;
                }
            };
        }, []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
            if (nodeRef.current && !hidden) {
                const currNode = nodeRef.current;
                if (!initialized || !hasHandleBounds || prevNodeRef.current !== currNode) {
                    // At this point we always want to make sure that the node gets re-measured / re-initialized.
                    // We need to unobserve it first in case it is still observed
                    if (prevNodeRef.current) {
                        resizeObserver?.unobserve(prevNodeRef.current);
                    }
                    resizeObserver?.observe(currNode);
                    prevNodeRef.current = currNode;
                }
            }
        }, [
            hidden,
            initialized,
            hasHandleBounds
        ]);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
            // when the user programmatically changes the source or handle position, we re-initialize the node
            const typeChanged = prevType.current !== type;
            const sourcePosChanged = prevSourcePosition.current !== sourcePosition;
            const targetPosChanged = prevTargetPosition.current !== targetPosition;
            if (nodeRef.current && (typeChanged || sourcePosChanged || targetPosChanged)) {
                if (typeChanged) {
                    prevType.current = type;
                }
                if (sourcePosChanged) {
                    prevSourcePosition.current = sourcePosition;
                }
                if (targetPosChanged) {
                    prevTargetPosition.current = targetPosition;
                }
                store.getState().updateNodeDimensions([
                    {
                        id,
                        nodeElement: nodeRef.current,
                        forceUpdate: true
                    }
                ]);
            }
        }, [
            id,
            type,
            sourcePosition,
            targetPosition
        ]);
        const dragging = useDrag({
            nodeRef,
            disabled: hidden || !isDraggable,
            noDragClassName,
            handleSelector: dragHandle,
            nodeId: id,
            isSelectable,
            selectNodesOnDrag
        });
        if (hidden) {
            return null;
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
                'react-flow__node',
                `react-flow__node-${type}`,
                {
                    // this is overwritable by passing `nopan` as a class name
                    [noPanClassName]: isDraggable
                },
                className,
                {
                    selected,
                    selectable: isSelectable,
                    parent: isParent,
                    dragging
                }
            ]),
            ref: nodeRef,
            style: {
                zIndex,
                transform: `translate(${xPosOrigin}px,${yPosOrigin}px)`,
                pointerEvents: hasPointerEvents ? 'all' : 'none',
                visibility: initialized ? 'visible' : 'hidden',
                ...style
            },
            "data-id": id,
            "data-testid": `rf__node-${id}`,
            onMouseEnter: onMouseEnterHandler,
            onMouseMove: onMouseMoveHandler,
            onMouseLeave: onMouseLeaveHandler,
            onContextMenu: onContextMenuHandler,
            onClick: onSelectNodeHandler,
            onDoubleClick: onDoubleClickHandler,
            onKeyDown: isFocusable ? onKeyDown : undefined,
            tabIndex: isFocusable ? 0 : undefined,
            role: isFocusable ? 'button' : undefined,
            "aria-describedby": disableKeyboardA11y ? undefined : `${ARIA_NODE_DESC_KEY}-${rfId}`,
            "aria-label": ariaLabel
        }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Provider, {
            value: id
        }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(NodeComponent, {
            id: id,
            data: data,
            type: type,
            xPos: xPos,
            yPos: yPos,
            selected: selected,
            isConnectable: isConnectable,
            sourcePosition: sourcePosition,
            targetPosition: targetPosition,
            dragging: dragging,
            dragHandle: dragHandle,
            zIndex: zIndex
        })));
    };
    NodeWrapper.displayName = 'NodeWrapper';
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(NodeWrapper);
};
/**
 * The nodes selection rectangle gets displayed when a user
 * made a selection with on or several nodes
 */ const selector$7 = (s)=>{
    const selectedNodes = s.getNodes().filter((n)=>n.selected);
    return {
        ...getNodesBounds(selectedNodes, s.nodeOrigin),
        transformString: `translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`,
        userSelectionActive: s.userSelectionActive
    };
};
function NodesSelection({ onSelectionContextMenu, noPanClassName, disableKeyboardA11y }) {
    const store = useStoreApi();
    const { width, height, x: left, y: top, transformString, userSelectionActive } = useStore(selector$7, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const updatePositions = useUpdateNodePositions();
    const nodeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!disableKeyboardA11y) {
            nodeRef.current?.focus({
                preventScroll: true
            });
        }
    }, [
        disableKeyboardA11y
    ]);
    useDrag({
        nodeRef
    });
    if (userSelectionActive || !width || !height) {
        return null;
    }
    const onContextMenu = onSelectionContextMenu ? (event)=>{
        const selectedNodes = store.getState().getNodes().filter((n)=>n.selected);
        onSelectionContextMenu(event, selectedNodes);
    } : undefined;
    const onKeyDown = (event)=>{
        if (Object.prototype.hasOwnProperty.call(arrowKeyDiffs, event.key)) {
            updatePositions({
                x: arrowKeyDiffs[event.key].x,
                y: arrowKeyDiffs[event.key].y,
                isShiftPressed: event.shiftKey
            });
        }
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
            'react-flow__nodesselection',
            'react-flow__container',
            noPanClassName
        ]),
        style: {
            transform: transformString
        }
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ref: nodeRef,
        className: "react-flow__nodesselection-rect",
        onContextMenu: onContextMenu,
        tabIndex: disableKeyboardA11y ? undefined : -1,
        onKeyDown: disableKeyboardA11y ? undefined : onKeyDown,
        style: {
            width,
            height,
            top,
            left
        }
    }));
}
var NodesSelection$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(NodesSelection);
const selector$6 = (s)=>s.nodesSelectionActive;
const FlowRenderer = ({ children, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneContextMenu, onPaneScroll, deleteKeyCode, onMove, onMoveStart, onMoveEnd, selectionKeyCode, selectionOnDrag, selectionMode, onSelectionStart, onSelectionEnd, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, elementsSelectable, zoomOnScroll, zoomOnPinch, panOnScroll: _panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag: _panOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, onSelectionContextMenu, noWheelClassName, noPanClassName, disableKeyboardA11y })=>{
    const nodesSelectionActive = useStore(selector$6);
    const selectionKeyPressed = useKeyPress(selectionKeyCode);
    const panActivationKeyPressed = useKeyPress(panActivationKeyCode);
    const panOnDrag = panActivationKeyPressed || _panOnDrag;
    const panOnScroll = panActivationKeyPressed || _panOnScroll;
    const isSelecting = selectionKeyPressed || selectionOnDrag && panOnDrag !== true;
    useGlobalKeyHandler({
        deleteKeyCode,
        multiSelectionKeyCode
    });
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(ZoomPane, {
        onMove: onMove,
        onMoveStart: onMoveStart,
        onMoveEnd: onMoveEnd,
        onPaneContextMenu: onPaneContextMenu,
        elementsSelectable: elementsSelectable,
        zoomOnScroll: zoomOnScroll,
        zoomOnPinch: zoomOnPinch,
        panOnScroll: panOnScroll,
        panOnScrollSpeed: panOnScrollSpeed,
        panOnScrollMode: panOnScrollMode,
        zoomOnDoubleClick: zoomOnDoubleClick,
        panOnDrag: !selectionKeyPressed && panOnDrag,
        defaultViewport: defaultViewport,
        translateExtent: translateExtent,
        minZoom: minZoom,
        maxZoom: maxZoom,
        zoomActivationKeyCode: zoomActivationKeyCode,
        preventScrolling: preventScrolling,
        noWheelClassName: noWheelClassName,
        noPanClassName: noPanClassName
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Pane, {
        onSelectionStart: onSelectionStart,
        onSelectionEnd: onSelectionEnd,
        onPaneClick: onPaneClick,
        onPaneMouseEnter: onPaneMouseEnter,
        onPaneMouseMove: onPaneMouseMove,
        onPaneMouseLeave: onPaneMouseLeave,
        onPaneContextMenu: onPaneContextMenu,
        onPaneScroll: onPaneScroll,
        panOnDrag: panOnDrag,
        isSelecting: !!isSelecting,
        selectionMode: selectionMode
    }, children, nodesSelectionActive && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(NodesSelection$1, {
        onSelectionContextMenu: onSelectionContextMenu,
        noPanClassName: noPanClassName,
        disableKeyboardA11y: disableKeyboardA11y
    })));
};
FlowRenderer.displayName = 'FlowRenderer';
var FlowRenderer$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(FlowRenderer);
function useVisibleNodes(onlyRenderVisible) {
    const nodes = useStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((s)=>onlyRenderVisible ? getNodesInside(s.nodeInternals, {
            x: 0,
            y: 0,
            width: s.width,
            height: s.height
        }, s.transform, true) : s.getNodes(), [
        onlyRenderVisible
    ]));
    return nodes;
}
function createNodeTypes(nodeTypes) {
    const standardTypes = {
        input: wrapNode(nodeTypes.input || InputNode$1),
        default: wrapNode(nodeTypes.default || DefaultNode$1),
        output: wrapNode(nodeTypes.output || OutputNode$1),
        group: wrapNode(nodeTypes.group || GroupNode)
    };
    const wrappedTypes = {};
    const specialTypes = Object.keys(nodeTypes).filter((k)=>![
            'input',
            'default',
            'output',
            'group'
        ].includes(k)).reduce((res, key)=>{
        res[key] = wrapNode(nodeTypes[key] || DefaultNode$1);
        return res;
    }, wrappedTypes);
    return {
        ...standardTypes,
        ...specialTypes
    };
}
const getPositionWithOrigin = ({ x, y, width, height, origin })=>{
    if (!width || !height) {
        return {
            x,
            y
        };
    }
    if (origin[0] < 0 || origin[1] < 0 || origin[0] > 1 || origin[1] > 1) {
        return {
            x,
            y
        };
    }
    return {
        x: x - width * origin[0],
        y: y - height * origin[1]
    };
};
const selector$5 = (s)=>({
        nodesDraggable: s.nodesDraggable,
        nodesConnectable: s.nodesConnectable,
        nodesFocusable: s.nodesFocusable,
        elementsSelectable: s.elementsSelectable,
        updateNodeDimensions: s.updateNodeDimensions,
        onError: s.onError
    });
const NodeRenderer = (props)=>{
    const { nodesDraggable, nodesConnectable, nodesFocusable, elementsSelectable, updateNodeDimensions, onError } = useStore(selector$5, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const nodes = useVisibleNodes(props.onlyRenderVisibleElements);
    const resizeObserverRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    const resizeObserver = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (typeof ResizeObserver === 'undefined') {
            return null;
        }
        const observer = new ResizeObserver((entries)=>{
            const updates = entries.map((entry)=>({
                    id: entry.target.getAttribute('data-id'),
                    nodeElement: entry.target,
                    forceUpdate: true
                }));
            updateNodeDimensions(updates);
        });
        resizeObserverRef.current = observer;
        return observer;
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            resizeObserverRef?.current?.disconnect();
        };
    }, []);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "react-flow__nodes",
        style: containerStyle
    }, nodes.map((node)=>{
        let nodeType = node.type || 'default';
        if (!props.nodeTypes[nodeType]) {
            onError?.('003', errorMessages['error003'](nodeType));
            nodeType = 'default';
        }
        const NodeComponent = props.nodeTypes[nodeType] || props.nodeTypes.default;
        const isDraggable = !!(node.draggable || nodesDraggable && typeof node.draggable === 'undefined');
        const isSelectable = !!(node.selectable || elementsSelectable && typeof node.selectable === 'undefined');
        const isConnectable = !!(node.connectable || nodesConnectable && typeof node.connectable === 'undefined');
        const isFocusable = !!(node.focusable || nodesFocusable && typeof node.focusable === 'undefined');
        const clampedPosition = props.nodeExtent ? clampPosition(node.positionAbsolute, props.nodeExtent) : node.positionAbsolute;
        const posX = clampedPosition?.x ?? 0;
        const posY = clampedPosition?.y ?? 0;
        const posOrigin = getPositionWithOrigin({
            x: posX,
            y: posY,
            width: node.width ?? 0,
            height: node.height ?? 0,
            origin: props.nodeOrigin
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(NodeComponent, {
            key: node.id,
            id: node.id,
            className: node.className,
            style: node.style,
            type: nodeType,
            data: node.data,
            sourcePosition: node.sourcePosition || Position.Bottom,
            targetPosition: node.targetPosition || Position.Top,
            hidden: node.hidden,
            xPos: posX,
            yPos: posY,
            xPosOrigin: posOrigin.x,
            yPosOrigin: posOrigin.y,
            selectNodesOnDrag: props.selectNodesOnDrag,
            onClick: props.onNodeClick,
            onMouseEnter: props.onNodeMouseEnter,
            onMouseMove: props.onNodeMouseMove,
            onMouseLeave: props.onNodeMouseLeave,
            onContextMenu: props.onNodeContextMenu,
            onDoubleClick: props.onNodeDoubleClick,
            selected: !!node.selected,
            isDraggable: isDraggable,
            isSelectable: isSelectable,
            isConnectable: isConnectable,
            isFocusable: isFocusable,
            resizeObserver: resizeObserver,
            dragHandle: node.dragHandle,
            zIndex: node[internalsSymbol]?.z ?? 0,
            isParent: !!node[internalsSymbol]?.isParent,
            noDragClassName: props.noDragClassName,
            noPanClassName: props.noPanClassName,
            initialized: !!node.width && !!node.height,
            rfId: props.rfId,
            disableKeyboardA11y: props.disableKeyboardA11y,
            ariaLabel: node.ariaLabel,
            hasHandleBounds: !!node[internalsSymbol]?.handleBounds
        });
    }));
};
NodeRenderer.displayName = 'NodeRenderer';
var NodeRenderer$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(NodeRenderer);
const shiftX = (x, shift, position)=>{
    if (position === Position.Left) return x - shift;
    if (position === Position.Right) return x + shift;
    return x;
};
const shiftY = (y, shift, position)=>{
    if (position === Position.Top) return y - shift;
    if (position === Position.Bottom) return y + shift;
    return y;
};
const EdgeUpdaterClassName = 'react-flow__edgeupdater';
const EdgeAnchor = ({ position, centerX, centerY, radius = 10, onMouseDown, onMouseEnter, onMouseOut, type })=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("circle", {
        onMouseDown: onMouseDown,
        onMouseEnter: onMouseEnter,
        onMouseOut: onMouseOut,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
            EdgeUpdaterClassName,
            `${EdgeUpdaterClassName}-${type}`
        ]),
        cx: shiftX(centerX, radius, position),
        cy: shiftY(centerY, radius, position),
        r: radius,
        stroke: "transparent",
        fill: "transparent"
    });
const alwaysValidConnection = ()=>true;
var wrapEdge = (EdgeComponent)=>{
    const EdgeWrapper = ({ id, className, type, data, onClick, onEdgeDoubleClick, selected, animated, label, labelStyle, labelShowBg, labelBgStyle, labelBgPadding, labelBgBorderRadius, style, source, target, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, elementsSelectable, hidden, sourceHandleId, targetHandleId, onContextMenu, onMouseEnter, onMouseMove, onMouseLeave, reconnectRadius, onReconnect, onReconnectStart, onReconnectEnd, markerEnd, markerStart, rfId, ariaLabel, isFocusable, isReconnectable, pathOptions, interactionWidth, disableKeyboardA11y })=>{
        const edgeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
        const [updateHover, setUpdateHover] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
        const [updating, setUpdating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
        const store = useStoreApi();
        const markerStartUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>`url('#${getMarkerId(markerStart, rfId)}')`, [
            markerStart,
            rfId
        ]);
        const markerEndUrl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>`url('#${getMarkerId(markerEnd, rfId)}')`, [
            markerEnd,
            rfId
        ]);
        if (hidden) {
            return null;
        }
        const onEdgeClick = (event)=>{
            const { edges, addSelectedEdges, unselectNodesAndEdges, multiSelectionActive } = store.getState();
            const edge = edges.find((e)=>e.id === id);
            if (!edge) {
                return;
            }
            if (elementsSelectable) {
                store.setState({
                    nodesSelectionActive: false
                });
                if (edge.selected && multiSelectionActive) {
                    unselectNodesAndEdges({
                        nodes: [],
                        edges: [
                            edge
                        ]
                    });
                    edgeRef.current?.blur();
                } else {
                    addSelectedEdges([
                        id
                    ]);
                }
            }
            if (onClick) {
                onClick(event, edge);
            }
        };
        const onEdgeDoubleClickHandler = getMouseHandler$1(id, store.getState, onEdgeDoubleClick);
        const onEdgeContextMenu = getMouseHandler$1(id, store.getState, onContextMenu);
        const onEdgeMouseEnter = getMouseHandler$1(id, store.getState, onMouseEnter);
        const onEdgeMouseMove = getMouseHandler$1(id, store.getState, onMouseMove);
        const onEdgeMouseLeave = getMouseHandler$1(id, store.getState, onMouseLeave);
        const handleEdgeUpdater = (event, isSourceHandle)=>{
            // avoid triggering edge updater if mouse btn is not left
            if (event.button !== 0) {
                return;
            }
            const { edges, isValidConnection: isValidConnectionStore } = store.getState();
            const nodeId = isSourceHandle ? target : source;
            const handleId = (isSourceHandle ? targetHandleId : sourceHandleId) || null;
            const handleType = isSourceHandle ? 'target' : 'source';
            const isValidConnection = isValidConnectionStore || alwaysValidConnection;
            const isTarget = isSourceHandle;
            const edge = edges.find((e)=>e.id === id);
            setUpdating(true);
            onReconnectStart?.(event, edge, handleType);
            const _onReconnectEnd = (evt)=>{
                setUpdating(false);
                onReconnectEnd?.(evt, edge, handleType);
            };
            const onConnectEdge = (connection)=>onReconnect?.(edge, connection);
            handlePointerDown({
                event,
                handleId,
                nodeId,
                onConnect: onConnectEdge,
                isTarget,
                getState: store.getState,
                setState: store.setState,
                isValidConnection,
                edgeUpdaterType: handleType,
                onReconnectEnd: _onReconnectEnd
            });
        };
        const onEdgeUpdaterSourceMouseDown = (event)=>handleEdgeUpdater(event, true);
        const onEdgeUpdaterTargetMouseDown = (event)=>handleEdgeUpdater(event, false);
        const onEdgeUpdaterMouseEnter = ()=>setUpdateHover(true);
        const onEdgeUpdaterMouseOut = ()=>setUpdateHover(false);
        const inactive = !elementsSelectable && !onClick;
        const onKeyDown = (event)=>{
            if (!disableKeyboardA11y && elementSelectionKeys.includes(event.key) && elementsSelectable) {
                const { unselectNodesAndEdges, addSelectedEdges, edges } = store.getState();
                const unselect = event.key === 'Escape';
                if (unselect) {
                    edgeRef.current?.blur();
                    unselectNodesAndEdges({
                        edges: [
                            edges.find((e)=>e.id === id)
                        ]
                    });
                } else {
                    addSelectedEdges([
                        id
                    ]);
                }
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("g", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
                'react-flow__edge',
                `react-flow__edge-${type}`,
                className,
                {
                    selected,
                    animated,
                    inactive,
                    updating: updateHover
                }
            ]),
            onClick: onEdgeClick,
            onDoubleClick: onEdgeDoubleClickHandler,
            onContextMenu: onEdgeContextMenu,
            onMouseEnter: onEdgeMouseEnter,
            onMouseMove: onEdgeMouseMove,
            onMouseLeave: onEdgeMouseLeave,
            onKeyDown: isFocusable ? onKeyDown : undefined,
            tabIndex: isFocusable ? 0 : undefined,
            role: isFocusable ? 'button' : 'img',
            "data-testid": `rf__edge-${id}`,
            "aria-label": ariaLabel === null ? undefined : ariaLabel ? ariaLabel : `Edge from ${source} to ${target}`,
            "aria-describedby": isFocusable ? `${ARIA_EDGE_DESC_KEY}-${rfId}` : undefined,
            ref: edgeRef
        }, !updating && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EdgeComponent, {
            id: id,
            source: source,
            target: target,
            selected: selected,
            animated: animated,
            label: label,
            labelStyle: labelStyle,
            labelShowBg: labelShowBg,
            labelBgStyle: labelBgStyle,
            labelBgPadding: labelBgPadding,
            labelBgBorderRadius: labelBgBorderRadius,
            data: data,
            style: style,
            sourceX: sourceX,
            sourceY: sourceY,
            targetX: targetX,
            targetY: targetY,
            sourcePosition: sourcePosition,
            targetPosition: targetPosition,
            sourceHandleId: sourceHandleId,
            targetHandleId: targetHandleId,
            markerStart: markerStartUrl,
            markerEnd: markerEndUrl,
            pathOptions: pathOptions,
            interactionWidth: interactionWidth
        }), isReconnectable && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, (isReconnectable === 'source' || isReconnectable === true) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EdgeAnchor, {
            position: sourcePosition,
            centerX: sourceX,
            centerY: sourceY,
            radius: reconnectRadius,
            onMouseDown: onEdgeUpdaterSourceMouseDown,
            onMouseEnter: onEdgeUpdaterMouseEnter,
            onMouseOut: onEdgeUpdaterMouseOut,
            type: "source"
        }), (isReconnectable === 'target' || isReconnectable === true) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EdgeAnchor, {
            position: targetPosition,
            centerX: targetX,
            centerY: targetY,
            radius: reconnectRadius,
            onMouseDown: onEdgeUpdaterTargetMouseDown,
            onMouseEnter: onEdgeUpdaterMouseEnter,
            onMouseOut: onEdgeUpdaterMouseOut,
            type: "target"
        })));
    };
    EdgeWrapper.displayName = 'EdgeWrapper';
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(EdgeWrapper);
};
function createEdgeTypes(edgeTypes) {
    const standardTypes = {
        default: wrapEdge(edgeTypes.default || BezierEdge),
        straight: wrapEdge(edgeTypes.bezier || StraightEdge),
        step: wrapEdge(edgeTypes.step || StepEdge),
        smoothstep: wrapEdge(edgeTypes.step || SmoothStepEdge),
        simplebezier: wrapEdge(edgeTypes.simplebezier || SimpleBezierEdge)
    };
    const wrappedTypes = {};
    const specialTypes = Object.keys(edgeTypes).filter((k)=>![
            'default',
            'bezier'
        ].includes(k)).reduce((res, key)=>{
        res[key] = wrapEdge(edgeTypes[key] || BezierEdge);
        return res;
    }, wrappedTypes);
    return {
        ...standardTypes,
        ...specialTypes
    };
}
function getHandlePosition(position, nodeRect, handle = null) {
    const x = (handle?.x || 0) + nodeRect.x;
    const y = (handle?.y || 0) + nodeRect.y;
    const width = handle?.width || nodeRect.width;
    const height = handle?.height || nodeRect.height;
    switch(position){
        case Position.Top:
            return {
                x: x + width / 2,
                y
            };
        case Position.Right:
            return {
                x: x + width,
                y: y + height / 2
            };
        case Position.Bottom:
            return {
                x: x + width / 2,
                y: y + height
            };
        case Position.Left:
            return {
                x,
                y: y + height / 2
            };
    }
}
function getHandle(bounds, handleId) {
    if (!bounds) {
        return null;
    }
    if (bounds.length === 1 || !handleId) {
        return bounds[0];
    } else if (handleId) {
        return bounds.find((d)=>d.id === handleId) || null;
    }
    return null;
}
const getEdgePositions = (sourceNodeRect, sourceHandle, sourcePosition, targetNodeRect, targetHandle, targetPosition)=>{
    const sourceHandlePos = getHandlePosition(sourcePosition, sourceNodeRect, sourceHandle);
    const targetHandlePos = getHandlePosition(targetPosition, targetNodeRect, targetHandle);
    return {
        sourceX: sourceHandlePos.x,
        sourceY: sourceHandlePos.y,
        targetX: targetHandlePos.x,
        targetY: targetHandlePos.y
    };
};
function isEdgeVisible({ sourcePos, targetPos, sourceWidth, sourceHeight, targetWidth, targetHeight, width, height, transform }) {
    const edgeBox = {
        x: Math.min(sourcePos.x, targetPos.x),
        y: Math.min(sourcePos.y, targetPos.y),
        x2: Math.max(sourcePos.x + sourceWidth, targetPos.x + targetWidth),
        y2: Math.max(sourcePos.y + sourceHeight, targetPos.y + targetHeight)
    };
    if (edgeBox.x === edgeBox.x2) {
        edgeBox.x2 += 1;
    }
    if (edgeBox.y === edgeBox.y2) {
        edgeBox.y2 += 1;
    }
    const viewBox = rectToBox({
        x: (0 - transform[0]) / transform[2],
        y: (0 - transform[1]) / transform[2],
        width: width / transform[2],
        height: height / transform[2]
    });
    const xOverlap = Math.max(0, Math.min(viewBox.x2, edgeBox.x2) - Math.max(viewBox.x, edgeBox.x));
    const yOverlap = Math.max(0, Math.min(viewBox.y2, edgeBox.y2) - Math.max(viewBox.y, edgeBox.y));
    const overlappingArea = Math.ceil(xOverlap * yOverlap);
    return overlappingArea > 0;
}
function getNodeData(node) {
    const handleBounds = node?.[internalsSymbol]?.handleBounds || null;
    const isValid = handleBounds && node?.width && node?.height && typeof node?.positionAbsolute?.x !== 'undefined' && typeof node?.positionAbsolute?.y !== 'undefined';
    return [
        {
            x: node?.positionAbsolute?.x || 0,
            y: node?.positionAbsolute?.y || 0,
            width: node?.width || 0,
            height: node?.height || 0
        },
        handleBounds,
        !!isValid
    ];
}
const defaultEdgeTree = [
    {
        level: 0,
        isMaxLevel: true,
        edges: []
    }
];
function groupEdgesByZLevel(edges, nodeInternals, elevateEdgesOnSelect = false) {
    let maxLevel = -1;
    const levelLookup = edges.reduce((tree, edge)=>{
        const hasZIndex = isNumeric(edge.zIndex);
        let z = hasZIndex ? edge.zIndex : 0;
        if (elevateEdgesOnSelect) {
            const targetNode = nodeInternals.get(edge.target);
            const sourceNode = nodeInternals.get(edge.source);
            const edgeOrConnectedNodeSelected = edge.selected || targetNode?.selected || sourceNode?.selected;
            const selectedZIndex = Math.max(sourceNode?.[internalsSymbol]?.z || 0, targetNode?.[internalsSymbol]?.z || 0, 1000);
            z = (hasZIndex ? edge.zIndex : 0) + (edgeOrConnectedNodeSelected ? selectedZIndex : 0);
        }
        if (tree[z]) {
            tree[z].push(edge);
        } else {
            tree[z] = [
                edge
            ];
        }
        maxLevel = z > maxLevel ? z : maxLevel;
        return tree;
    }, {});
    const edgeTree = Object.entries(levelLookup).map(([key, edges])=>{
        const level = +key;
        return {
            edges,
            level,
            isMaxLevel: level === maxLevel
        };
    });
    if (edgeTree.length === 0) {
        return defaultEdgeTree;
    }
    return edgeTree;
}
function useVisibleEdges(onlyRenderVisible, nodeInternals, elevateEdgesOnSelect) {
    const edges = useStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((s)=>{
        if (!onlyRenderVisible) {
            return s.edges;
        }
        return s.edges.filter((e)=>{
            const sourceNode = nodeInternals.get(e.source);
            const targetNode = nodeInternals.get(e.target);
            return sourceNode?.width && sourceNode?.height && targetNode?.width && targetNode?.height && isEdgeVisible({
                sourcePos: sourceNode.positionAbsolute || {
                    x: 0,
                    y: 0
                },
                targetPos: targetNode.positionAbsolute || {
                    x: 0,
                    y: 0
                },
                sourceWidth: sourceNode.width,
                sourceHeight: sourceNode.height,
                targetWidth: targetNode.width,
                targetHeight: targetNode.height,
                width: s.width,
                height: s.height,
                transform: s.transform
            });
        });
    }, [
        onlyRenderVisible,
        nodeInternals
    ]));
    return groupEdgesByZLevel(edges, nodeInternals, elevateEdgesOnSelect);
}
const ArrowSymbol = ({ color = 'none', strokeWidth = 1 })=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("polyline", {
        style: {
            stroke: color,
            strokeWidth
        },
        strokeLinecap: "round",
        strokeLinejoin: "round",
        fill: "none",
        points: "-5,-4 0,0 -5,4"
    });
};
const ArrowClosedSymbol = ({ color = 'none', strokeWidth = 1 })=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("polyline", {
        style: {
            stroke: color,
            fill: color,
            strokeWidth
        },
        strokeLinecap: "round",
        strokeLinejoin: "round",
        points: "-5,-4 0,0 -5,4 -5,-4"
    });
};
const MarkerSymbols = {
    [MarkerType.Arrow]: ArrowSymbol,
    [MarkerType.ArrowClosed]: ArrowClosedSymbol
};
function useMarkerSymbol(type) {
    const store = useStoreApi();
    const symbol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        const symbolExists = Object.prototype.hasOwnProperty.call(MarkerSymbols, type);
        if (!symbolExists) {
            store.getState().onError?.('009', errorMessages['error009'](type));
            return null;
        }
        return MarkerSymbols[type];
    }, [
        type
    ]);
    return symbol;
}
const Marker = ({ id, type, color, width = 12.5, height = 12.5, markerUnits = 'strokeWidth', strokeWidth, orient = 'auto-start-reverse' })=>{
    const Symbol1 = useMarkerSymbol(type);
    if (!Symbol1) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("marker", {
        className: "react-flow__arrowhead",
        id: id,
        markerWidth: `${width}`,
        markerHeight: `${height}`,
        viewBox: "-10 -10 20 20",
        markerUnits: markerUnits,
        orient: orient,
        refX: "0",
        refY: "0"
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Symbol1, {
        color: color,
        strokeWidth: strokeWidth
    }));
};
const markerSelector = ({ defaultColor, rfId })=>(s)=>{
        const ids = [];
        return s.edges.reduce((markers, edge)=>{
            [
                edge.markerStart,
                edge.markerEnd
            ].forEach((marker)=>{
                if (marker && typeof marker === 'object') {
                    const markerId = getMarkerId(marker, rfId);
                    if (!ids.includes(markerId)) {
                        markers.push({
                            id: markerId,
                            color: marker.color || defaultColor,
                            ...marker
                        });
                        ids.push(markerId);
                    }
                }
            });
            return markers;
        }, []).sort((a, b)=>a.id.localeCompare(b.id));
    };
// when you have multiple flows on a page and you hide the first one, the other ones have no markers anymore
// when they do have markers with the same ids. To prevent this the user can pass a unique id to the react flow wrapper
// that we can then use for creating our unique marker ids
const MarkerDefinitions = ({ defaultColor, rfId })=>{
    const markers = useStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(markerSelector({
        defaultColor,
        rfId
    }), [
        defaultColor,
        rfId
    ]), // the id includes all marker options, so we just need to look at that part of the marker
    (a, b)=>!(a.length !== b.length || a.some((m, i)=>m.id !== b[i].id)));
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("defs", null, markers.map((marker)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Marker, {
            id: marker.id,
            key: marker.id,
            type: marker.type,
            color: marker.color,
            width: marker.width,
            height: marker.height,
            markerUnits: marker.markerUnits,
            strokeWidth: marker.strokeWidth,
            orient: marker.orient
        })));
};
MarkerDefinitions.displayName = 'MarkerDefinitions';
var MarkerDefinitions$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(MarkerDefinitions);
const selector$4 = (s)=>({
        nodesConnectable: s.nodesConnectable,
        edgesFocusable: s.edgesFocusable,
        edgesUpdatable: s.edgesUpdatable,
        elementsSelectable: s.elementsSelectable,
        width: s.width,
        height: s.height,
        connectionMode: s.connectionMode,
        nodeInternals: s.nodeInternals,
        onError: s.onError
    });
const EdgeRenderer = ({ defaultMarkerColor, onlyRenderVisibleElements, elevateEdgesOnSelect, rfId, edgeTypes, noPanClassName, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeClick, onEdgeDoubleClick, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius, children, disableKeyboardA11y })=>{
    const { edgesFocusable, edgesUpdatable, elementsSelectable, width, height, connectionMode, nodeInternals, onError } = useStore(selector$4, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const edgeTree = useVisibleEdges(onlyRenderVisibleElements, nodeInternals, elevateEdgesOnSelect);
    if (!width) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, edgeTree.map(({ level, edges, isMaxLevel })=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
            key: level,
            style: {
                zIndex: level
            },
            width: width,
            height: height,
            className: "react-flow__edges react-flow__container"
        }, isMaxLevel && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(MarkerDefinitions$1, {
            defaultColor: defaultMarkerColor,
            rfId: rfId
        }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("g", null, edges.map((edge)=>{
            const [sourceNodeRect, sourceHandleBounds, sourceIsValid] = getNodeData(nodeInternals.get(edge.source));
            const [targetNodeRect, targetHandleBounds, targetIsValid] = getNodeData(nodeInternals.get(edge.target));
            if (!sourceIsValid || !targetIsValid) {
                return null;
            }
            let edgeType = edge.type || 'default';
            if (!edgeTypes[edgeType]) {
                onError?.('011', errorMessages['error011'](edgeType));
                edgeType = 'default';
            }
            const EdgeComponent = edgeTypes[edgeType] || edgeTypes.default;
            // when connection type is loose we can define all handles as sources and connect source -> source
            const targetNodeHandles = connectionMode === ConnectionMode.Strict ? targetHandleBounds.target : (targetHandleBounds.target ?? []).concat(targetHandleBounds.source ?? []);
            const sourceHandle = getHandle(sourceHandleBounds.source, edge.sourceHandle);
            const targetHandle = getHandle(targetNodeHandles, edge.targetHandle);
            const sourcePosition = sourceHandle?.position || Position.Bottom;
            const targetPosition = targetHandle?.position || Position.Top;
            const isFocusable = !!(edge.focusable || edgesFocusable && typeof edge.focusable === 'undefined');
            const edgeReconnectable = edge.reconnectable || edge.updatable;
            const isReconnectable = typeof onReconnect !== 'undefined' && (edgeReconnectable || edgesUpdatable && typeof edgeReconnectable === 'undefined');
            if (!sourceHandle || !targetHandle) {
                onError?.('008', errorMessages['error008'](sourceHandle, edge));
                return null;
            }
            const { sourceX, sourceY, targetX, targetY } = getEdgePositions(sourceNodeRect, sourceHandle, sourcePosition, targetNodeRect, targetHandle, targetPosition);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EdgeComponent, {
                key: edge.id,
                id: edge.id,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
                    edge.className,
                    noPanClassName
                ]),
                type: edgeType,
                data: edge.data,
                selected: !!edge.selected,
                animated: !!edge.animated,
                hidden: !!edge.hidden,
                label: edge.label,
                labelStyle: edge.labelStyle,
                labelShowBg: edge.labelShowBg,
                labelBgStyle: edge.labelBgStyle,
                labelBgPadding: edge.labelBgPadding,
                labelBgBorderRadius: edge.labelBgBorderRadius,
                style: edge.style,
                source: edge.source,
                target: edge.target,
                sourceHandleId: edge.sourceHandle,
                targetHandleId: edge.targetHandle,
                markerEnd: edge.markerEnd,
                markerStart: edge.markerStart,
                sourceX: sourceX,
                sourceY: sourceY,
                targetX: targetX,
                targetY: targetY,
                sourcePosition: sourcePosition,
                targetPosition: targetPosition,
                elementsSelectable: elementsSelectable,
                onContextMenu: onEdgeContextMenu,
                onMouseEnter: onEdgeMouseEnter,
                onMouseMove: onEdgeMouseMove,
                onMouseLeave: onEdgeMouseLeave,
                onClick: onEdgeClick,
                onEdgeDoubleClick: onEdgeDoubleClick,
                onReconnect: onReconnect,
                onReconnectStart: onReconnectStart,
                onReconnectEnd: onReconnectEnd,
                reconnectRadius: reconnectRadius,
                rfId: rfId,
                ariaLabel: edge.ariaLabel,
                isFocusable: isFocusable,
                isReconnectable: isReconnectable,
                pathOptions: 'pathOptions' in edge ? edge.pathOptions : undefined,
                interactionWidth: edge.interactionWidth,
                disableKeyboardA11y: disableKeyboardA11y
            });
        })))), children);
};
EdgeRenderer.displayName = 'EdgeRenderer';
var EdgeRenderer$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(EdgeRenderer);
const selector$3 = (s)=>`translate(${s.transform[0]}px,${s.transform[1]}px) scale(${s.transform[2]})`;
function Viewport({ children }) {
    const transform = useStore(selector$3);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "react-flow__viewport react-flow__container",
        style: {
            transform
        }
    }, children);
}
function useOnInitHandler(onInit) {
    const rfInstance = useReactFlow();
    const isInitialized = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isInitialized.current && rfInstance.viewportInitialized && onInit) {
            setTimeout(()=>onInit(rfInstance), 1);
            isInitialized.current = true;
        }
    }, [
        onInit,
        rfInstance.viewportInitialized
    ]);
}
const oppositePosition = {
    [Position.Left]: Position.Right,
    [Position.Right]: Position.Left,
    [Position.Top]: Position.Bottom,
    [Position.Bottom]: Position.Top
};
const ConnectionLine = ({ nodeId, handleType, style, type = ConnectionLineType.Bezier, CustomComponent, connectionStatus })=>{
    const { fromNode, handleId, toX, toY, connectionMode } = useStore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((s)=>({
            fromNode: s.nodeInternals.get(nodeId),
            handleId: s.connectionHandleId,
            toX: (s.connectionPosition.x - s.transform[0]) / s.transform[2],
            toY: (s.connectionPosition.y - s.transform[1]) / s.transform[2],
            connectionMode: s.connectionMode
        }), [
        nodeId
    ]), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const fromHandleBounds = fromNode?.[internalsSymbol]?.handleBounds;
    let handleBounds = fromHandleBounds?.[handleType];
    if (connectionMode === ConnectionMode.Loose) {
        handleBounds = handleBounds ? handleBounds : fromHandleBounds?.[handleType === 'source' ? 'target' : 'source'];
    }
    if (!fromNode || !handleBounds) {
        return null;
    }
    const fromHandle = handleId ? handleBounds.find((d)=>d.id === handleId) : handleBounds[0];
    const fromHandleX = fromHandle ? fromHandle.x + fromHandle.width / 2 : (fromNode.width ?? 0) / 2;
    const fromHandleY = fromHandle ? fromHandle.y + fromHandle.height / 2 : fromNode.height ?? 0;
    const fromX = (fromNode.positionAbsolute?.x ?? 0) + fromHandleX;
    const fromY = (fromNode.positionAbsolute?.y ?? 0) + fromHandleY;
    const fromPosition = fromHandle?.position;
    const toPosition = fromPosition ? oppositePosition[fromPosition] : null;
    if (!fromPosition || !toPosition) {
        return null;
    }
    if (CustomComponent) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(CustomComponent, {
            connectionLineType: type,
            connectionLineStyle: style,
            fromNode: fromNode,
            fromHandle: fromHandle,
            fromX: fromX,
            fromY: fromY,
            toX: toX,
            toY: toY,
            fromPosition: fromPosition,
            toPosition: toPosition,
            connectionStatus: connectionStatus
        });
    }
    let dAttr = '';
    const pathParams = {
        sourceX: fromX,
        sourceY: fromY,
        sourcePosition: fromPosition,
        targetX: toX,
        targetY: toY,
        targetPosition: toPosition
    };
    if (type === ConnectionLineType.Bezier) {
        // we assume the destination position is opposite to the source position
        [dAttr] = getBezierPath(pathParams);
    } else if (type === ConnectionLineType.Step) {
        [dAttr] = getSmoothStepPath({
            ...pathParams,
            borderRadius: 0
        });
    } else if (type === ConnectionLineType.SmoothStep) {
        [dAttr] = getSmoothStepPath(pathParams);
    } else if (type === ConnectionLineType.SimpleBezier) {
        [dAttr] = getSimpleBezierPath(pathParams);
    } else {
        dAttr = `M${fromX},${fromY} ${toX},${toY}`;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        d: dAttr,
        fill: "none",
        className: "react-flow__connection-path",
        style: style
    });
};
ConnectionLine.displayName = 'ConnectionLine';
const selector$2 = (s)=>({
        nodeId: s.connectionNodeId,
        handleType: s.connectionHandleType,
        nodesConnectable: s.nodesConnectable,
        connectionStatus: s.connectionStatus,
        width: s.width,
        height: s.height
    });
function ConnectionLineWrapper({ containerStyle, style, type, component }) {
    const { nodeId, handleType, nodesConnectable, width, height, connectionStatus } = useStore(selector$2, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    const isValid = !!(nodeId && handleType && width && nodesConnectable);
    if (!isValid) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        style: containerStyle,
        width: width,
        height: height,
        className: "react-flow__edges react-flow__connectionline react-flow__container"
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("g", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
            'react-flow__connection',
            connectionStatus
        ])
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(ConnectionLine, {
        nodeId: nodeId,
        handleType: handleType,
        style: style,
        type: type,
        CustomComponent: component,
        connectionStatus: connectionStatus
    })));
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function useNodeOrEdgeTypes(nodeOrEdgeTypes, createTypes) {
    const typesKeysRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const store = useStoreApi();
    const typesParsed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) {
            const typeKeys = Object.keys(nodeOrEdgeTypes);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"])(typesKeysRef.current, typeKeys)) {
                store.getState().onError?.('002', errorMessages['error002']());
            }
            typesKeysRef.current = typeKeys;
        }
        return createTypes(nodeOrEdgeTypes);
    }, [
        nodeOrEdgeTypes
    ]);
    return typesParsed;
}
const GraphView = ({ nodeTypes, edgeTypes, onMove, onMoveStart, onMoveEnd, onInit, onNodeClick, onEdgeClick, onNodeDoubleClick, onEdgeDoubleClick, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionLineType, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, selectionKeyCode, selectionOnDrag, selectionMode, multiSelectionKeyCode, panActivationKeyCode, zoomActivationKeyCode, deleteKeyCode, onlyRenderVisibleElements, elementsSelectable, selectNodesOnDrag, defaultViewport, translateExtent, minZoom, maxZoom, preventScrolling, defaultMarkerColor, zoomOnScroll, zoomOnPinch, panOnScroll, panOnScrollSpeed, panOnScrollMode, zoomOnDoubleClick, panOnDrag, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, onEdgeContextMenu, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius, noDragClassName, noWheelClassName, noPanClassName, elevateEdgesOnSelect, disableKeyboardA11y, nodeOrigin, nodeExtent, rfId })=>{
    const nodeTypesWrapped = useNodeOrEdgeTypes(nodeTypes, createNodeTypes);
    const edgeTypesWrapped = useNodeOrEdgeTypes(edgeTypes, createEdgeTypes);
    useOnInitHandler(onInit);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(FlowRenderer$1, {
        onPaneClick: onPaneClick,
        onPaneMouseEnter: onPaneMouseEnter,
        onPaneMouseMove: onPaneMouseMove,
        onPaneMouseLeave: onPaneMouseLeave,
        onPaneContextMenu: onPaneContextMenu,
        onPaneScroll: onPaneScroll,
        deleteKeyCode: deleteKeyCode,
        selectionKeyCode: selectionKeyCode,
        selectionOnDrag: selectionOnDrag,
        selectionMode: selectionMode,
        onSelectionStart: onSelectionStart,
        onSelectionEnd: onSelectionEnd,
        multiSelectionKeyCode: multiSelectionKeyCode,
        panActivationKeyCode: panActivationKeyCode,
        zoomActivationKeyCode: zoomActivationKeyCode,
        elementsSelectable: elementsSelectable,
        onMove: onMove,
        onMoveStart: onMoveStart,
        onMoveEnd: onMoveEnd,
        zoomOnScroll: zoomOnScroll,
        zoomOnPinch: zoomOnPinch,
        zoomOnDoubleClick: zoomOnDoubleClick,
        panOnScroll: panOnScroll,
        panOnScrollSpeed: panOnScrollSpeed,
        panOnScrollMode: panOnScrollMode,
        panOnDrag: panOnDrag,
        defaultViewport: defaultViewport,
        translateExtent: translateExtent,
        minZoom: minZoom,
        maxZoom: maxZoom,
        onSelectionContextMenu: onSelectionContextMenu,
        preventScrolling: preventScrolling,
        noDragClassName: noDragClassName,
        noWheelClassName: noWheelClassName,
        noPanClassName: noPanClassName,
        disableKeyboardA11y: disableKeyboardA11y
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Viewport, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(EdgeRenderer$1, {
        edgeTypes: edgeTypesWrapped,
        onEdgeClick: onEdgeClick,
        onEdgeDoubleClick: onEdgeDoubleClick,
        onlyRenderVisibleElements: onlyRenderVisibleElements,
        onEdgeContextMenu: onEdgeContextMenu,
        onEdgeMouseEnter: onEdgeMouseEnter,
        onEdgeMouseMove: onEdgeMouseMove,
        onEdgeMouseLeave: onEdgeMouseLeave,
        onReconnect: onReconnect,
        onReconnectStart: onReconnectStart,
        onReconnectEnd: onReconnectEnd,
        reconnectRadius: reconnectRadius,
        defaultMarkerColor: defaultMarkerColor,
        noPanClassName: noPanClassName,
        elevateEdgesOnSelect: !!elevateEdgesOnSelect,
        disableKeyboardA11y: disableKeyboardA11y,
        rfId: rfId
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(ConnectionLineWrapper, {
        style: connectionLineStyle,
        type: connectionLineType,
        component: connectionLineComponent,
        containerStyle: connectionLineContainerStyle
    })), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: "react-flow__edgelabel-renderer"
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(NodeRenderer$1, {
        nodeTypes: nodeTypesWrapped,
        onNodeClick: onNodeClick,
        onNodeDoubleClick: onNodeDoubleClick,
        onNodeMouseEnter: onNodeMouseEnter,
        onNodeMouseMove: onNodeMouseMove,
        onNodeMouseLeave: onNodeMouseLeave,
        onNodeContextMenu: onNodeContextMenu,
        selectNodesOnDrag: selectNodesOnDrag,
        onlyRenderVisibleElements: onlyRenderVisibleElements,
        noPanClassName: noPanClassName,
        noDragClassName: noDragClassName,
        disableKeyboardA11y: disableKeyboardA11y,
        nodeOrigin: nodeOrigin,
        nodeExtent: nodeExtent,
        rfId: rfId
    })));
};
GraphView.displayName = 'GraphView';
var GraphView$1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(GraphView);
const infiniteExtent = [
    [
        Number.NEGATIVE_INFINITY,
        Number.NEGATIVE_INFINITY
    ],
    [
        Number.POSITIVE_INFINITY,
        Number.POSITIVE_INFINITY
    ]
];
const initialState = {
    rfId: '1',
    width: 0,
    height: 0,
    transform: [
        0,
        0,
        1
    ],
    nodeInternals: new Map(),
    edges: [],
    onNodesChange: null,
    onEdgesChange: null,
    hasDefaultNodes: false,
    hasDefaultEdges: false,
    d3Zoom: null,
    d3Selection: null,
    d3ZoomHandler: undefined,
    minZoom: 0.5,
    maxZoom: 2,
    translateExtent: infiniteExtent,
    nodeExtent: infiniteExtent,
    nodesSelectionActive: false,
    userSelectionActive: false,
    userSelectionRect: null,
    connectionNodeId: null,
    connectionHandleId: null,
    connectionHandleType: 'source',
    connectionPosition: {
        x: 0,
        y: 0
    },
    connectionStatus: null,
    connectionMode: ConnectionMode.Strict,
    domNode: null,
    paneDragging: false,
    noPanClassName: 'nopan',
    nodeOrigin: [
        0,
        0
    ],
    nodeDragThreshold: 0,
    snapGrid: [
        15,
        15
    ],
    snapToGrid: false,
    nodesDraggable: true,
    nodesConnectable: true,
    nodesFocusable: true,
    edgesFocusable: true,
    edgesUpdatable: true,
    elementsSelectable: true,
    elevateNodesOnSelect: true,
    fitViewOnInit: false,
    fitViewOnInitDone: false,
    fitViewOnInitOptions: undefined,
    onSelectionChange: [],
    multiSelectionActive: false,
    connectionStartHandle: null,
    connectionEndHandle: null,
    connectionClickStartHandle: null,
    connectOnClick: true,
    ariaLiveMessage: '',
    autoPanOnConnect: true,
    autoPanOnNodeDrag: true,
    connectionRadius: 20,
    onError: devWarn,
    isValidConnection: undefined
};
const createRFStore = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$traditional$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createWithEqualityFn"])((set, get)=>({
            ...initialState,
            setNodes: (nodes)=>{
                const { nodeInternals, nodeOrigin, elevateNodesOnSelect } = get();
                set({
                    nodeInternals: createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect)
                });
            },
            getNodes: ()=>{
                return Array.from(get().nodeInternals.values());
            },
            setEdges: (edges)=>{
                const { defaultEdgeOptions = {} } = get();
                set({
                    edges: edges.map((e)=>({
                            ...defaultEdgeOptions,
                            ...e
                        }))
                });
            },
            setDefaultNodesAndEdges: (nodes, edges)=>{
                const hasDefaultNodes = typeof nodes !== 'undefined';
                const hasDefaultEdges = typeof edges !== 'undefined';
                const nodeInternals = hasDefaultNodes ? createNodeInternals(nodes, new Map(), get().nodeOrigin, get().elevateNodesOnSelect) : new Map();
                const nextEdges = hasDefaultEdges ? edges : [];
                set({
                    nodeInternals,
                    edges: nextEdges,
                    hasDefaultNodes,
                    hasDefaultEdges
                });
            },
            updateNodeDimensions: (updates)=>{
                const { onNodesChange, nodeInternals, fitViewOnInit, fitViewOnInitDone, fitViewOnInitOptions, domNode, nodeOrigin } = get();
                const viewportNode = domNode?.querySelector('.react-flow__viewport');
                if (!viewportNode) {
                    return;
                }
                const style = window.getComputedStyle(viewportNode);
                const { m22: zoom } = new window.DOMMatrixReadOnly(style.transform);
                const changes = updates.reduce((res, update)=>{
                    const node = nodeInternals.get(update.id);
                    if (node?.hidden) {
                        nodeInternals.set(node.id, {
                            ...node,
                            [internalsSymbol]: {
                                ...node[internalsSymbol],
                                // we need to reset the handle bounds when the node is hidden
                                // in order to force a new observation when the node is shown again
                                handleBounds: undefined
                            }
                        });
                    } else if (node) {
                        const dimensions = getDimensions(update.nodeElement);
                        const doUpdate = !!(dimensions.width && dimensions.height && (node.width !== dimensions.width || node.height !== dimensions.height || update.forceUpdate));
                        if (doUpdate) {
                            nodeInternals.set(node.id, {
                                ...node,
                                [internalsSymbol]: {
                                    ...node[internalsSymbol],
                                    handleBounds: {
                                        source: getHandleBounds('.source', update.nodeElement, zoom, nodeOrigin),
                                        target: getHandleBounds('.target', update.nodeElement, zoom, nodeOrigin)
                                    }
                                },
                                ...dimensions
                            });
                            res.push({
                                id: node.id,
                                type: 'dimensions',
                                dimensions
                            });
                        }
                    }
                    return res;
                }, []);
                updateAbsoluteNodePositions(nodeInternals, nodeOrigin);
                const nextFitViewOnInitDone = fitViewOnInitDone || fitViewOnInit && !fitViewOnInitDone && fitView(get, {
                    initial: true,
                    ...fitViewOnInitOptions
                });
                set({
                    nodeInternals: new Map(nodeInternals),
                    fitViewOnInitDone: nextFitViewOnInitDone
                });
                if (changes?.length > 0) {
                    onNodesChange?.(changes);
                }
            },
            updateNodePositions: (nodeDragItems, positionChanged = true, dragging = false)=>{
                const { triggerNodeChanges } = get();
                const changes = nodeDragItems.map((node)=>{
                    const change = {
                        id: node.id,
                        type: 'position',
                        dragging
                    };
                    if (positionChanged) {
                        change.positionAbsolute = node.positionAbsolute;
                        change.position = node.position;
                    }
                    return change;
                });
                triggerNodeChanges(changes);
            },
            triggerNodeChanges: (changes)=>{
                const { onNodesChange, nodeInternals, hasDefaultNodes, nodeOrigin, getNodes, elevateNodesOnSelect } = get();
                if (changes?.length) {
                    if (hasDefaultNodes) {
                        const nodes = applyNodeChanges(changes, getNodes());
                        const nextNodeInternals = createNodeInternals(nodes, nodeInternals, nodeOrigin, elevateNodesOnSelect);
                        set({
                            nodeInternals: nextNodeInternals
                        });
                    }
                    onNodesChange?.(changes);
                }
            },
            addSelectedNodes: (selectedNodeIds)=>{
                const { multiSelectionActive, edges, getNodes } = get();
                let changedNodes;
                let changedEdges = null;
                if (multiSelectionActive) {
                    changedNodes = selectedNodeIds.map((nodeId)=>createSelectionChange(nodeId, true));
                } else {
                    changedNodes = getSelectionChanges(getNodes(), selectedNodeIds);
                    changedEdges = getSelectionChanges(edges, []);
                }
                updateNodesAndEdgesSelections({
                    changedNodes,
                    changedEdges,
                    get,
                    set
                });
            },
            addSelectedEdges: (selectedEdgeIds)=>{
                const { multiSelectionActive, edges, getNodes } = get();
                let changedEdges;
                let changedNodes = null;
                if (multiSelectionActive) {
                    changedEdges = selectedEdgeIds.map((edgeId)=>createSelectionChange(edgeId, true));
                } else {
                    changedEdges = getSelectionChanges(edges, selectedEdgeIds);
                    changedNodes = getSelectionChanges(getNodes(), []);
                }
                updateNodesAndEdgesSelections({
                    changedNodes,
                    changedEdges,
                    get,
                    set
                });
            },
            unselectNodesAndEdges: ({ nodes, edges } = {})=>{
                const { edges: storeEdges, getNodes } = get();
                const nodesToUnselect = nodes ? nodes : getNodes();
                const edgesToUnselect = edges ? edges : storeEdges;
                const changedNodes = nodesToUnselect.map((n)=>{
                    n.selected = false;
                    return createSelectionChange(n.id, false);
                });
                const changedEdges = edgesToUnselect.map((edge)=>createSelectionChange(edge.id, false));
                updateNodesAndEdgesSelections({
                    changedNodes,
                    changedEdges,
                    get,
                    set
                });
            },
            setMinZoom: (minZoom)=>{
                const { d3Zoom, maxZoom } = get();
                d3Zoom?.scaleExtent([
                    minZoom,
                    maxZoom
                ]);
                set({
                    minZoom
                });
            },
            setMaxZoom: (maxZoom)=>{
                const { d3Zoom, minZoom } = get();
                d3Zoom?.scaleExtent([
                    minZoom,
                    maxZoom
                ]);
                set({
                    maxZoom
                });
            },
            setTranslateExtent: (translateExtent)=>{
                get().d3Zoom?.translateExtent(translateExtent);
                set({
                    translateExtent
                });
            },
            resetSelectedElements: ()=>{
                const { edges, getNodes } = get();
                const nodes = getNodes();
                const nodesToUnselect = nodes.filter((e)=>e.selected).map((n)=>createSelectionChange(n.id, false));
                const edgesToUnselect = edges.filter((e)=>e.selected).map((e)=>createSelectionChange(e.id, false));
                updateNodesAndEdgesSelections({
                    changedNodes: nodesToUnselect,
                    changedEdges: edgesToUnselect,
                    get,
                    set
                });
            },
            setNodeExtent: (nodeExtent)=>{
                const { nodeInternals } = get();
                nodeInternals.forEach((node)=>{
                    node.positionAbsolute = clampPosition(node.position, nodeExtent);
                });
                set({
                    nodeExtent,
                    nodeInternals: new Map(nodeInternals)
                });
            },
            panBy: (delta)=>{
                const { transform, width, height, d3Zoom, d3Selection, translateExtent } = get();
                if (!d3Zoom || !d3Selection || !delta.x && !delta.y) {
                    return false;
                }
                const nextTransform = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$d3$2d$zoom$2f$src$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__identity__as__zoomIdentity$3e$__["zoomIdentity"].translate(transform[0] + delta.x, transform[1] + delta.y).scale(transform[2]);
                const extent = [
                    [
                        0,
                        0
                    ],
                    [
                        width,
                        height
                    ]
                ];
                const constrainedTransform = d3Zoom?.constrain()(nextTransform, extent, translateExtent);
                d3Zoom.transform(d3Selection, constrainedTransform);
                const transformChanged = transform[0] !== constrainedTransform.x || transform[1] !== constrainedTransform.y || transform[2] !== constrainedTransform.k;
                return transformChanged;
            },
            cancelConnection: ()=>set({
                    connectionNodeId: initialState.connectionNodeId,
                    connectionHandleId: initialState.connectionHandleId,
                    connectionHandleType: initialState.connectionHandleType,
                    connectionStatus: initialState.connectionStatus,
                    connectionStartHandle: initialState.connectionStartHandle,
                    connectionEndHandle: initialState.connectionEndHandle
                }),
            reset: ()=>set({
                    ...initialState
                })
        }), Object.is);
const ReactFlowProvider = ({ children })=>{
    const storeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    if (!storeRef.current) {
        storeRef.current = createRFStore();
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Provider$1, {
        value: storeRef.current
    }, children);
};
ReactFlowProvider.displayName = 'ReactFlowProvider';
const Wrapper = ({ children })=>{
    const isWrapped = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(StoreContext);
    if (isWrapped) {
        // we need to wrap it with a fragment because it's not allowed for children to be a ReactNode
        // https://github.com/DefinitelyTyped/DefinitelyTyped/issues/18051
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, children);
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(ReactFlowProvider, null, children);
};
Wrapper.displayName = 'ReactFlowWrapper';
const defaultNodeTypes = {
    input: InputNode$1,
    default: DefaultNode$1,
    output: OutputNode$1,
    group: GroupNode
};
const defaultEdgeTypes = {
    default: BezierEdge,
    straight: StraightEdge,
    step: StepEdge,
    smoothstep: SmoothStepEdge,
    simplebezier: SimpleBezierEdge
};
const initNodeOrigin = [
    0,
    0
];
const initSnapGrid = [
    15,
    15
];
const initDefaultViewport = {
    x: 0,
    y: 0,
    zoom: 1
};
const wrapperStyle = {
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    position: 'relative',
    zIndex: 0
};
const ReactFlow = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ nodes, edges, defaultNodes, defaultEdges, className, nodeTypes = defaultNodeTypes, edgeTypes = defaultEdgeTypes, onNodeClick, onEdgeClick, onInit, onMove, onMoveStart, onMoveEnd, onConnect, onConnectStart, onConnectEnd, onClickConnectStart, onClickConnectEnd, onNodeMouseEnter, onNodeMouseMove, onNodeMouseLeave, onNodeContextMenu, onNodeDoubleClick, onNodeDragStart, onNodeDrag, onNodeDragStop, onNodesDelete, onEdgesDelete, onSelectionChange, onSelectionDragStart, onSelectionDrag, onSelectionDragStop, onSelectionContextMenu, onSelectionStart, onSelectionEnd, connectionMode = ConnectionMode.Strict, connectionLineType = ConnectionLineType.Bezier, connectionLineStyle, connectionLineComponent, connectionLineContainerStyle, deleteKeyCode = 'Backspace', selectionKeyCode = 'Shift', selectionOnDrag = false, selectionMode = SelectionMode.Full, panActivationKeyCode = 'Space', multiSelectionKeyCode = isMacOs() ? 'Meta' : 'Control', zoomActivationKeyCode = isMacOs() ? 'Meta' : 'Control', snapToGrid = false, snapGrid = initSnapGrid, onlyRenderVisibleElements = false, selectNodesOnDrag = true, nodesDraggable, nodesConnectable, nodesFocusable, nodeOrigin = initNodeOrigin, edgesFocusable, edgesUpdatable, elementsSelectable, defaultViewport = initDefaultViewport, minZoom = 0.5, maxZoom = 2, translateExtent = infiniteExtent, preventScrolling = true, nodeExtent, defaultMarkerColor = '#b1b1b7', zoomOnScroll = true, zoomOnPinch = true, panOnScroll = false, panOnScrollSpeed = 0.5, panOnScrollMode = PanOnScrollMode.Free, zoomOnDoubleClick = true, panOnDrag = true, onPaneClick, onPaneMouseEnter, onPaneMouseMove, onPaneMouseLeave, onPaneScroll, onPaneContextMenu, children, onEdgeContextMenu, onEdgeDoubleClick, onEdgeMouseEnter, onEdgeMouseMove, onEdgeMouseLeave, onEdgeUpdate, onEdgeUpdateStart, onEdgeUpdateEnd, onReconnect, onReconnectStart, onReconnectEnd, reconnectRadius = 10, edgeUpdaterRadius = 10, onNodesChange, onEdgesChange, noDragClassName = 'nodrag', noWheelClassName = 'nowheel', noPanClassName = 'nopan', fitView = false, fitViewOptions, connectOnClick = true, attributionPosition, proOptions, defaultEdgeOptions, elevateNodesOnSelect = true, elevateEdgesOnSelect = false, disableKeyboardA11y = false, autoPanOnConnect = true, autoPanOnNodeDrag = true, connectionRadius = 20, isValidConnection, onError, style, id, nodeDragThreshold, ...rest }, ref)=>{
    const rfId = id || '1';
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        ...rest,
        style: {
            ...style,
            ...wrapperStyle
        },
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classcat$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])([
            'react-flow',
            className
        ]),
        "data-testid": "rf__wrapper",
        id: id
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Wrapper, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(GraphView$1, {
        onInit: onInit,
        onMove: onMove,
        onMoveStart: onMoveStart,
        onMoveEnd: onMoveEnd,
        onNodeClick: onNodeClick,
        onEdgeClick: onEdgeClick,
        onNodeMouseEnter: onNodeMouseEnter,
        onNodeMouseMove: onNodeMouseMove,
        onNodeMouseLeave: onNodeMouseLeave,
        onNodeContextMenu: onNodeContextMenu,
        onNodeDoubleClick: onNodeDoubleClick,
        nodeTypes: nodeTypes,
        edgeTypes: edgeTypes,
        connectionLineType: connectionLineType,
        connectionLineStyle: connectionLineStyle,
        connectionLineComponent: connectionLineComponent,
        connectionLineContainerStyle: connectionLineContainerStyle,
        selectionKeyCode: selectionKeyCode,
        selectionOnDrag: selectionOnDrag,
        selectionMode: selectionMode,
        deleteKeyCode: deleteKeyCode,
        multiSelectionKeyCode: multiSelectionKeyCode,
        panActivationKeyCode: panActivationKeyCode,
        zoomActivationKeyCode: zoomActivationKeyCode,
        onlyRenderVisibleElements: onlyRenderVisibleElements,
        selectNodesOnDrag: selectNodesOnDrag,
        defaultViewport: defaultViewport,
        translateExtent: translateExtent,
        minZoom: minZoom,
        maxZoom: maxZoom,
        preventScrolling: preventScrolling,
        zoomOnScroll: zoomOnScroll,
        zoomOnPinch: zoomOnPinch,
        zoomOnDoubleClick: zoomOnDoubleClick,
        panOnScroll: panOnScroll,
        panOnScrollSpeed: panOnScrollSpeed,
        panOnScrollMode: panOnScrollMode,
        panOnDrag: panOnDrag,
        onPaneClick: onPaneClick,
        onPaneMouseEnter: onPaneMouseEnter,
        onPaneMouseMove: onPaneMouseMove,
        onPaneMouseLeave: onPaneMouseLeave,
        onPaneScroll: onPaneScroll,
        onPaneContextMenu: onPaneContextMenu,
        onSelectionContextMenu: onSelectionContextMenu,
        onSelectionStart: onSelectionStart,
        onSelectionEnd: onSelectionEnd,
        onEdgeContextMenu: onEdgeContextMenu,
        onEdgeDoubleClick: onEdgeDoubleClick,
        onEdgeMouseEnter: onEdgeMouseEnter,
        onEdgeMouseMove: onEdgeMouseMove,
        onEdgeMouseLeave: onEdgeMouseLeave,
        onReconnect: onReconnect ?? onEdgeUpdate,
        onReconnectStart: onReconnectStart ?? onEdgeUpdateStart,
        onReconnectEnd: onReconnectEnd ?? onEdgeUpdateEnd,
        reconnectRadius: reconnectRadius ?? edgeUpdaterRadius,
        defaultMarkerColor: defaultMarkerColor,
        noDragClassName: noDragClassName,
        noWheelClassName: noWheelClassName,
        noPanClassName: noPanClassName,
        elevateEdgesOnSelect: elevateEdgesOnSelect,
        rfId: rfId,
        disableKeyboardA11y: disableKeyboardA11y,
        nodeOrigin: nodeOrigin,
        nodeExtent: nodeExtent
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(StoreUpdater, {
        nodes: nodes,
        edges: edges,
        defaultNodes: defaultNodes,
        defaultEdges: defaultEdges,
        onConnect: onConnect,
        onConnectStart: onConnectStart,
        onConnectEnd: onConnectEnd,
        onClickConnectStart: onClickConnectStart,
        onClickConnectEnd: onClickConnectEnd,
        nodesDraggable: nodesDraggable,
        nodesConnectable: nodesConnectable,
        nodesFocusable: nodesFocusable,
        edgesFocusable: edgesFocusable,
        edgesUpdatable: edgesUpdatable,
        elementsSelectable: elementsSelectable,
        elevateNodesOnSelect: elevateNodesOnSelect,
        minZoom: minZoom,
        maxZoom: maxZoom,
        nodeExtent: nodeExtent,
        onNodesChange: onNodesChange,
        onEdgesChange: onEdgesChange,
        snapToGrid: snapToGrid,
        snapGrid: snapGrid,
        connectionMode: connectionMode,
        translateExtent: translateExtent,
        connectOnClick: connectOnClick,
        defaultEdgeOptions: defaultEdgeOptions,
        fitView: fitView,
        fitViewOptions: fitViewOptions,
        onNodesDelete: onNodesDelete,
        onEdgesDelete: onEdgesDelete,
        onNodeDragStart: onNodeDragStart,
        onNodeDrag: onNodeDrag,
        onNodeDragStop: onNodeDragStop,
        onSelectionDrag: onSelectionDrag,
        onSelectionDragStart: onSelectionDragStart,
        onSelectionDragStop: onSelectionDragStop,
        noPanClassName: noPanClassName,
        nodeOrigin: nodeOrigin,
        rfId: rfId,
        autoPanOnConnect: autoPanOnConnect,
        autoPanOnNodeDrag: autoPanOnNodeDrag,
        onError: onError,
        connectionRadius: connectionRadius,
        isValidConnection: isValidConnection,
        nodeDragThreshold: nodeDragThreshold
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Wrapper$1, {
        onSelectionChange: onSelectionChange
    }), children, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(Attribution, {
        proOptions: proOptions,
        position: attributionPosition
    }), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(A11yDescriptions, {
        rfId: rfId,
        disableKeyboardA11y: disableKeyboardA11y
    })));
});
ReactFlow.displayName = 'ReactFlow';
const selector$1 = (s)=>s.domNode?.querySelector('.react-flow__edgelabel-renderer');
function EdgeLabelRenderer({ children }) {
    const edgeLabelRenderer = useStore(selector$1);
    if (!edgeLabelRenderer) {
        return null;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createPortal"])(children, edgeLabelRenderer);
}
function useUpdateNodeInternals() {
    const store = useStoreApi();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((id)=>{
        const { domNode, updateNodeDimensions } = store.getState();
        const updateIds = Array.isArray(id) ? id : [
            id
        ];
        const updates = updateIds.reduce((res, updateId)=>{
            const nodeElement = domNode?.querySelector(`.react-flow__node[data-id="${updateId}"]`);
            if (nodeElement) {
                res.push({
                    id: updateId,
                    nodeElement,
                    forceUpdate: true
                });
            }
            return res;
        }, []);
        requestAnimationFrame(()=>updateNodeDimensions(updates));
    }, []);
}
const nodesSelector = (state)=>state.getNodes();
function useNodes() {
    const nodes = useStore(nodesSelector, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    return nodes;
}
const edgesSelector = (state)=>state.edges;
function useEdges() {
    const edges = useStore(edgesSelector, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    return edges;
}
const viewportSelector = (state)=>({
        x: state.transform[0],
        y: state.transform[1],
        zoom: state.transform[2]
    });
function useViewport() {
    const viewport = useStore(viewportSelector, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$node_modules$2f$zustand$2f$esm$2f$shallow$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["shallow"]);
    return viewport;
}
/* eslint-disable @typescript-eslint/no-explicit-any */ function createUseItemsState(applyChanges) {
    return (initialItems)=>{
        const [items, setItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialItems);
        const onItemsChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((changes)=>setItems((items)=>applyChanges(changes, items)), []);
        return [
            items,
            setItems,
            onItemsChange
        ];
    };
}
const useNodesState = createUseItemsState(applyNodeChanges);
const useEdgesState = createUseItemsState(applyEdgeChanges);
function useOnViewportChange({ onStart, onChange, onEnd }) {
    const store = useStoreApi();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        store.setState({
            onViewportChangeStart: onStart
        });
    }, [
        onStart
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        store.setState({
            onViewportChange: onChange
        });
    }, [
        onChange
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        store.setState({
            onViewportChangeEnd: onEnd
        });
    }, [
        onEnd
    ]);
}
function useOnSelectionChange({ onChange }) {
    const store = useStoreApi();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const nextSelectionChangeHandlers = [
            ...store.getState().onSelectionChange,
            onChange
        ];
        store.setState({
            onSelectionChange: nextSelectionChangeHandlers
        });
        return ()=>{
            const nextHandlers = store.getState().onSelectionChange.filter((fn)=>fn !== onChange);
            store.setState({
                onSelectionChange: nextHandlers
            });
        };
    }, [
        onChange
    ]);
}
const selector = (options)=>(s)=>{
        if (s.nodeInternals.size === 0) {
            return false;
        }
        return s.getNodes().filter((n)=>options.includeHiddenNodes ? true : !n.hidden).every((n)=>n[internalsSymbol]?.handleBounds !== undefined);
    };
const defaultOptions = {
    includeHiddenNodes: false
};
function useNodesInitialized(options = defaultOptions) {
    const initialized = useStore(selector(options));
    return initialized;
}
;
}}),
"[project]/node_modules/@reactflow/core/dist/esm/index.mjs [app-ssr] (ecmascript) <export ReactFlow as default>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactFlow"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reactflow$2f$core$2f$dist$2f$esm$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@reactflow/core/dist/esm/index.mjs [app-ssr] (ecmascript)");
}}),

};

//# sourceMappingURL=node_modules_%40reactflow_core_dist_esm_index_mjs_7cdd66ff._.js.map