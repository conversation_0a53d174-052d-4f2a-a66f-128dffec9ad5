!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactFlowNodeResizer={},e.<PERSON>act)}(this,(function(e,t){"use strict";function n(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let o,r=0;r<e.length;r++)""!==(o=n(e[r]))&&(t+=(t&&" ")+o);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}var o={value:()=>{}};function r(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new i(o)}function i(e){this._=e}function a(e,t){return e.trim().split(/^|\s+/).map((function(e){var n="",o=e.indexOf(".");if(o>=0&&(n=e.slice(o+1),e=e.slice(0,o)),e&&!t.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:n}}))}function s(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function l(e,t,n){for(var r=0,i=e.length;r<i;++r)if(e[r].name===t){e[r]=o,e=e.slice(0,r).concat(e.slice(r+1));break}return null!=n&&e.push({name:t,value:n}),e}i.prototype=r.prototype={constructor:i,on:function(e,t){var n,o=this._,r=a(e+"",o),i=-1,c=r.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++i<c;)if(n=(e=r[i]).type)o[n]=l(o[n],e.name,t);else if(null==t)for(n in o)o[n]=l(o[n],e.name,null);return this}for(;++i<c;)if((n=(e=r[i]).type)&&(n=s(o[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new i(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var c="http://www.w3.org/1999/xhtml",u={svg:"http://www.w3.org/2000/svg",xhtml:c,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function d(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),u.hasOwnProperty(t)?{space:u[t],local:e}:e}function h(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===c&&t.documentElement.namespaceURI===c?t.createElement(e):t.createElementNS(n,e)}}function f(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function g(e){var t=d(e);return(t.local?f:h)(t)}function p(){}function m(e){return null==e?p:function(){return this.querySelector(e)}}function y(e){return null==e?[]:Array.isArray(e)?e:Array.from(e)}function v(){return[]}function b(e){return null==e?v:function(){return this.querySelectorAll(e)}}function x(e){return function(){return this.matches(e)}}function w(e){return function(t){return t.matches(e)}}var S=Array.prototype.find;function E(){return this.firstElementChild}var _=Array.prototype.filter;function C(){return Array.from(this.children)}function N(e){return new Array(e.length)}function M(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function k(e){return function(){return e}}function A(e,t,n,o,r,i){for(var a,s=0,l=t.length,c=i.length;s<c;++s)(a=t[s])?(a.__data__=i[s],o[s]=a):n[s]=new M(e,i[s]);for(;s<l;++s)(a=t[s])&&(r[s]=a)}function P(e,t,n,o,r,i,a){var s,l,c,u=new Map,d=t.length,h=i.length,f=new Array(d);for(s=0;s<d;++s)(l=t[s])&&(f[s]=c=a.call(l,l.__data__,s,t)+"",u.has(c)?r[s]=l:u.set(c,l));for(s=0;s<h;++s)c=a.call(e,i[s],s,i)+"",(l=u.get(c))?(o[s]=l,l.__data__=i[s],u.delete(c)):n[s]=new M(e,i[s]);for(s=0;s<d;++s)(l=t[s])&&u.get(f[s])===l&&(r[s]=l)}function I(e){return e.__data__}function O(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function R(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function z(e){return function(){this.removeAttribute(e)}}function D(e){return function(){this.removeAttributeNS(e.space,e.local)}}function $(e,t){return function(){this.setAttribute(e,t)}}function T(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function B(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function L(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function H(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function X(e){return function(){this.style.removeProperty(e)}}function Y(e,t,n){return function(){this.style.setProperty(e,t,n)}}function V(e,t,n){return function(){var o=t.apply(this,arguments);null==o?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function K(e,t){return e.style.getPropertyValue(t)||H(e).getComputedStyle(e,null).getPropertyValue(t)}function F(e){return function(){delete this[e]}}function Z(e,t){return function(){this[e]=t}}function W(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function j(e){return e.trim().split(/^|\s+/)}function q(e){return e.classList||new U(e)}function U(e){this._node=e,this._names=j(e.getAttribute("class")||"")}function G(e,t){for(var n=q(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function Q(e,t){for(var n=q(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function J(e){return function(){G(this,e)}}function ee(e){return function(){Q(this,e)}}function te(e,t){return function(){(t.apply(this,arguments)?G:Q)(this,e)}}function ne(){this.textContent=""}function oe(e){return function(){this.textContent=e}}function re(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function ie(){this.innerHTML=""}function ae(e){return function(){this.innerHTML=e}}function se(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function le(){this.nextSibling&&this.parentNode.appendChild(this)}function ce(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function ue(){return null}function de(){var e=this.parentNode;e&&e.removeChild(this)}function he(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function fe(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ge(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}function pe(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function me(e,t,n){return function(){var o,r=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(r)for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t);this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function ye(e,t,n){var o=H(e),r=o.CustomEvent;"function"==typeof r?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function ve(e,t){return function(){return ye(this,e,t)}}function be(e,t){return function(){return ye(this,e,t.apply(this,arguments))}}M.prototype={constructor:M,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},U.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var xe=[null];function we(e,t){this._groups=e,this._parents=t}function Se(){return new we([[document.documentElement]],xe)}function Ee(e){return"string"==typeof e?new we([[document.querySelector(e)]],[document.documentElement]):new we([[e]],xe)}function _e(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}we.prototype=Se.prototype={constructor:we,select:function(e){"function"!=typeof e&&(e=m(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a,s=t[r],l=s.length,c=o[r]=new Array(l),u=0;u<l;++u)(i=s[u])&&(a=e.call(i,i.__data__,u,s))&&("__data__"in i&&(a.__data__=i.__data__),c[u]=a);return new we(o,this._parents)},selectAll:function(e){e="function"==typeof e?function(e){return function(){return y(e.apply(this,arguments))}}(e):b(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var a,s=t[i],l=s.length,c=0;c<l;++c)(a=s[c])&&(o.push(e.call(a,a.__data__,c,s)),r.push(a));return new we(o,r)},selectChild:function(e){return this.select(null==e?E:function(e){return function(){return S.call(this.children,e)}}("function"==typeof e?e:w(e)))},selectChildren:function(e){return this.selectAll(null==e?C:function(e){return function(){return _.call(this.children,e)}}("function"==typeof e?e:w(e)))},filter:function(e){"function"!=typeof e&&(e=x(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new we(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,I);var n=t?P:A,o=this._parents,r=this._groups;"function"!=typeof e&&(e=k(e));for(var i=r.length,a=new Array(i),s=new Array(i),l=new Array(i),c=0;c<i;++c){var u=o[c],d=r[c],h=d.length,f=O(e.call(u,u&&u.__data__,c,o)),g=f.length,p=s[c]=new Array(g),m=a[c]=new Array(g),y=l[c]=new Array(h);n(u,d,p,m,y,f,t);for(var v,b,x=0,w=0;x<g;++x)if(v=p[x]){for(x>=w&&(w=x+1);!(b=m[w])&&++w<g;);v._next=b||null}}return(a=new we(a,o))._enter=s,a._exit=l,a},enter:function(){return new we(this._enter||this._groups.map(N),this._parents)},exit:function(){return new we(this._exit||this._groups.map(N),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof e?(o=e(o))&&(o=o.selection()):o=o.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,a=Math.min(r,i),s=new Array(r),l=0;l<a;++l)for(var c,u=n[l],d=o[l],h=u.length,f=s[l]=new Array(h),g=0;g<h;++g)(c=u[g]||d[g])&&(f[g]=c);for(;l<r;++l)s[l]=n[l];return new we(s,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,a=r[i];--i>=0;)(o=r[i])&&(a&&4^o.compareDocumentPosition(a)&&a.parentNode.insertBefore(o,a),a=o);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=R);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var a,s=n[i],l=s.length,c=r[i]=new Array(l),u=0;u<l;++u)(a=s[u])&&(c[u]=a);c.sort(t)}return new we(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var a=o[r];if(a)return a}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],a=0,s=i.length;a<s;++a)(r=i[a])&&e.call(r,r.__data__,a,i);return this},attr:function(e,t){var n=d(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==t?n.local?D:z:"function"==typeof t?n.local?L:B:n.local?T:$)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?X:"function"==typeof t?V:Y)(e,t,null==n?"":n)):K(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?F:"function"==typeof t?W:Z)(e,t)):this.node()[e]},classed:function(e,t){var n=j(e+"");if(arguments.length<2){for(var o=q(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof t?te:t?J:ee)(n,t))},text:function(e){return arguments.length?this.each(null==e?ne:("function"==typeof e?re:oe)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?ie:("function"==typeof e?se:ae)(e)):this.node().innerHTML},raise:function(){return this.each(le)},lower:function(){return this.each(ce)},append:function(e){var t="function"==typeof e?e:g(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:g(e),o=null==t?ue:"function"==typeof t?t:m(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each(de)},clone:function(e){return this.select(e?fe:he)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=ge(e+""),a=i.length;if(!(arguments.length<2)){for(s=t?me:pe,o=0;o<a;++o)this.each(s(i[o],t,n));return this}var s=this.node().__on;if(s)for(var l,c=0,u=s.length;c<u;++c)for(o=0,l=s[c];o<a;++o)if((r=i[o]).type===l.type&&r.name===l.name)return l.value},dispatch:function(e,t){return this.each(("function"==typeof t?be:ve)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,a=r.length;i<a;++i)(o=r[i])&&(yield o)}};const Ce={passive:!1},Ne={capture:!0,passive:!1};function Me(e){e.stopImmediatePropagation()}function ke(e){e.preventDefault(),e.stopImmediatePropagation()}function Ae(e){var t=e.document.documentElement,n=Ee(e).on("dragstart.drag",ke,Ne);"onselectstart"in t?n.on("selectstart.drag",ke,Ne):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Pe(e,t){var n=e.document.documentElement,o=Ee(e).on("dragstart.drag",null);t&&(o.on("click.drag",ke,Ne),setTimeout((function(){o.on("click.drag",null)}),0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var Ie=e=>()=>e;function Oe(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:a,y:s,dx:l,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}function Re(e){return!e.ctrlKey&&!e.button}function ze(){return this.parentNode}function De(e,t){return null==t?{x:e.x,y:e.y}:t}function $e(){return navigator.maxTouchPoints||"ontouchstart"in this}function Te(){var e,t,n,o,i=Re,a=ze,s=De,l=$e,c={},u=r("start","drag","end"),d=0,h=0;function f(e){e.on("mousedown.drag",g).filter(l).on("touchstart.drag",y).on("touchmove.drag",v,Ce).on("touchend.drag touchcancel.drag",b).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(r,s){if(!o&&i.call(this,r,s)){var l=x(this,a.call(this,r,s),r,s,"mouse");l&&(Ee(r.view).on("mousemove.drag",p,Ne).on("mouseup.drag",m,Ne),Ae(r.view),Me(r),n=!1,e=r.clientX,t=r.clientY,l("start",r))}}function p(o){if(ke(o),!n){var r=o.clientX-e,i=o.clientY-t;n=r*r+i*i>h}c.mouse("drag",o)}function m(e){Ee(e.view).on("mousemove.drag mouseup.drag",null),Pe(e.view,n),ke(e),c.mouse("end",e)}function y(e,t){if(i.call(this,e,t)){var n,o,r=e.changedTouches,s=a.call(this,e,t),l=r.length;for(n=0;n<l;++n)(o=x(this,s,e,t,r[n].identifier,r[n]))&&(Me(e),o("start",e,r[n]))}}function v(e){var t,n,o=e.changedTouches,r=o.length;for(t=0;t<r;++t)(n=c[o[t].identifier])&&(ke(e),n("drag",e,o[t]))}function b(e){var t,n,r=e.changedTouches,i=r.length;for(o&&clearTimeout(o),o=setTimeout((function(){o=null}),500),t=0;t<i;++t)(n=c[r[t].identifier])&&(Me(e),n("end",e,r[t]))}function x(e,t,n,o,r,i){var a,l,h,g=u.copy(),p=_e(i||n,t);if(null!=(h=s.call(e,new Oe("beforestart",{sourceEvent:n,target:f,identifier:r,active:d,x:p[0],y:p[1],dx:0,dy:0,dispatch:g}),o)))return a=h.x-p[0]||0,l=h.y-p[1]||0,function n(i,s,u){var m,y=p;switch(i){case"start":c[r]=n,m=d++;break;case"end":delete c[r],--d;case"drag":p=_e(u||s,t),m=d}g.call(i,e,new Oe(i,{sourceEvent:s,subject:h,target:f,identifier:r,active:m,x:p[0]+a,y:p[1]+l,dx:p[0]-y[0],dy:p[1]-y[1],dispatch:g}),o)}}return f.filter=function(e){return arguments.length?(i="function"==typeof e?e:Ie(!!e),f):i},f.container=function(e){return arguments.length?(a="function"==typeof e?e:Ie(e),f):a},f.subject=function(e){return arguments.length?(s="function"==typeof e?e:Ie(e),f):s},f.touchable=function(e){return arguments.length?(l="function"==typeof e?e:Ie(!!e),f):l},f.on=function(){var e=u.on.apply(u,arguments);return e===u?f:e},f.clickDistance=function(e){return arguments.length?(h=(e=+e)*e,f):Math.sqrt(h)},f}function Be(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}Oe.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var Le,He,Xe,Ye={},Ve={},Ke={},Fe={get exports(){return Ke},set exports(e){Ke=e}},Ze={};function We(){return He||(He=1,function(e){e.exports=function(){if(Le)return Ze;Le=1;var e=t,n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=e.useState,r=e.useEffect,i=e.useLayoutEffect,a=e.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var o=t();return!n(e,o)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),l=o({inst:{value:n,getSnapshot:t}}),c=l[0].inst,u=l[1];return i((function(){c.value=n,c.getSnapshot=t,s(c)&&u({inst:c})}),[e,n,t]),r((function(){return s(c)&&u({inst:c}),e((function(){s(c)&&u({inst:c})}))}),[e]),a(n),n};return Ze.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:l,Ze}()}(Fe)),Ke}
/**
   * @license React
   * use-sync-external-store-shim/with-selector.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */!function(e){e.exports=function(){if(Xe)return Ve;Xe=1;var e=t,n=We(),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=n.useSyncExternalStore,i=e.useRef,a=e.useEffect,s=e.useMemo,l=e.useDebugValue;return Ve.useSyncExternalStoreWithSelector=function(e,t,n,c,u){var d=i(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;d=s((function(){function e(e){if(!a){if(a=!0,r=e,e=c(e),void 0!==u&&h.hasValue){var t=h.value;if(u(t,e))return i=t}return i=e}if(t=i,o(r,e))return t;var n=c(e);return void 0!==u&&u(t,n)?t:(r=e,i=n)}var r,i,a=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]}),[t,n,c,u]);var f=r(e,d[0],d[1]);return a((function(){h.hasValue=!0,h.value=f}),[f]),l(f),f},Ve}()}({get exports(){return Ye},set exports(e){Ye=e}});var je=Be(Ye);const qe=e=>{let t;const n=new Set,o=(e,o)=>{const r="function"==typeof e?e(t):e;if(!Object.is(r,t)){const e=t;t=(null!=o?o:"object"!=typeof r)?r:Object.assign({},t,r),n.forEach((n=>n(t,e)))}},r=()=>t,i={setState:o,getState:r,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}};return t=e(o,r,i),i},{useSyncExternalStoreWithSelector:Ue}=je;function Ge(e,n=e.getState,o){const r=Ue(e.subscribe,e.getState,e.getServerState||e.getState,n,o);return t.useDebugValue(r),r}const Qe=(e,t)=>{const n=(e=>e?qe(e):qe)(e),o=(e,o=t)=>Ge(n,e,o);return Object.assign(o,n),o};function Je(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Object.is(e[n[o]],t[n[o]]))return!1;return!0}function et(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function tt(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function nt(){}var ot=.7,rt=1/ot,it="\\s*([+-]?\\d+)\\s*",at="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",st="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",lt=/^#([0-9a-f]{3,8})$/,ct=new RegExp(`^rgb\\(${it},${it},${it}\\)$`),ut=new RegExp(`^rgb\\(${st},${st},${st}\\)$`),dt=new RegExp(`^rgba\\(${it},${it},${it},${at}\\)$`),ht=new RegExp(`^rgba\\(${st},${st},${st},${at}\\)$`),ft=new RegExp(`^hsl\\(${at},${st},${st}\\)$`),gt=new RegExp(`^hsla\\(${at},${st},${st},${at}\\)$`),pt={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function mt(){return this.rgb().formatHex()}function yt(){return this.rgb().formatRgb()}function vt(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=lt.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?bt(t):3===n?new Et(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?xt(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?xt(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=ct.exec(e))?new Et(t[1],t[2],t[3],1):(t=ut.exec(e))?new Et(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=dt.exec(e))?xt(t[1],t[2],t[3],t[4]):(t=ht.exec(e))?xt(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=ft.exec(e))?At(t[1],t[2]/100,t[3]/100,1):(t=gt.exec(e))?At(t[1],t[2]/100,t[3]/100,t[4]):pt.hasOwnProperty(e)?bt(pt[e]):"transparent"===e?new Et(NaN,NaN,NaN,0):null}function bt(e){return new Et(e>>16&255,e>>8&255,255&e,1)}function xt(e,t,n,o){return o<=0&&(e=t=n=NaN),new Et(e,t,n,o)}function wt(e){return e instanceof nt||(e=vt(e)),e?new Et((e=e.rgb()).r,e.g,e.b,e.opacity):new Et}function St(e,t,n,o){return 1===arguments.length?wt(e):new Et(e,t,n,null==o?1:o)}function Et(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function _t(){return`#${kt(this.r)}${kt(this.g)}${kt(this.b)}`}function Ct(){const e=Nt(this.opacity);return`${1===e?"rgb(":"rgba("}${Mt(this.r)}, ${Mt(this.g)}, ${Mt(this.b)}${1===e?")":`, ${e})`}`}function Nt(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Mt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function kt(e){return((e=Mt(e))<16?"0":"")+e.toString(16)}function At(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new It(e,t,n,o)}function Pt(e){if(e instanceof It)return new It(e.h,e.s,e.l,e.opacity);if(e instanceof nt||(e=vt(e)),!e)return new It;if(e instanceof It)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),a=NaN,s=i-r,l=(i+r)/2;return s?(a=t===i?(n-o)/s+6*(n<o):n===i?(o-t)/s+2:(t-n)/s+4,s/=l<.5?i+r:2-i-r,a*=60):s=l>0&&l<1?0:a,new It(a,s,l,e.opacity)}function It(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function Ot(e){return(e=(e||0)%360)<0?e+360:e}function Rt(e){return Math.max(0,Math.min(1,e||0))}function zt(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}et(nt,vt,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:mt,formatHex:mt,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Pt(this).formatHsl()},formatRgb:yt,toString:yt}),et(Et,St,tt(nt,{brighter(e){return e=null==e?rt:Math.pow(rt,e),new Et(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?ot:Math.pow(ot,e),new Et(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Et(Mt(this.r),Mt(this.g),Mt(this.b),Nt(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:_t,formatHex:_t,formatHex8:function(){return`#${kt(this.r)}${kt(this.g)}${kt(this.b)}${kt(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Ct,toString:Ct})),et(It,(function(e,t,n,o){return 1===arguments.length?Pt(e):new It(e,t,n,null==o?1:o)}),tt(nt,{brighter(e){return e=null==e?rt:Math.pow(rt,e),new It(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?ot:Math.pow(ot,e),new It(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new Et(zt(e>=240?e-240:e+120,r,o),zt(e,r,o),zt(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new It(Ot(this.h),Rt(this.s),Rt(this.l),Nt(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Nt(this.opacity);return`${1===e?"hsl(":"hsla("}${Ot(this.h)}, ${100*Rt(this.s)}%, ${100*Rt(this.l)}%${1===e?")":`, ${e})`}`}}));var Dt=e=>()=>e;function $t(e){return 1==(e=+e)?Tt:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}(t,n,e):Dt(isNaN(t)?n:t)}}function Tt(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):Dt(isNaN(e)?t:e)}var Bt=function e(t){var n=$t(t);function o(e,t){var o=n((e=St(e)).r,(t=St(t)).r),r=n(e.g,t.g),i=n(e.b,t.b),a=Tt(e.opacity,t.opacity);return function(t){return e.r=o(t),e.g=r(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function Lt(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Ht=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Xt=new RegExp(Ht.source,"g");function Yt(e,t){var n,o,r,i=Ht.lastIndex=Xt.lastIndex=0,a=-1,s=[],l=[];for(e+="",t+="";(n=Ht.exec(e))&&(o=Xt.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),s[a]?s[a]+=r:s[++a]=r),(n=n[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,l.push({i:a,x:Lt(n,o)})),i=Xt.lastIndex;return i<t.length&&(r=t.slice(i),s[a]?s[a]+=r:s[++a]=r),s.length<2?l[0]?function(e){return function(t){return e(t)+""}}(l[0].x):function(e){return function(){return e}}(t):(t=l.length,function(e){for(var n,o=0;o<t;++o)s[(n=l[o]).i]=n.x(e);return s.join("")})}var Vt,Kt=180/Math.PI,Ft={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Zt(e,t,n,o,r,i){var a,s,l;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(l=e*n+t*o)&&(n-=e*l,o-=t*l),(s=Math.sqrt(n*n+o*o))&&(n/=s,o/=s,l/=s),e*o<t*n&&(e=-e,t=-t,l=-l,a=-a),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*Kt,skewX:Math.atan(l)*Kt,scaleX:a,scaleY:s}}function Wt(e,t,n,o){function r(e){return e.length?e.pop()+" ":""}return function(i,a){var s=[],l=[];return i=e(i),a=e(a),function(e,o,r,i,a,s){if(e!==r||o!==i){var l=a.push("translate(",null,t,null,n);s.push({i:l-4,x:Lt(e,r)},{i:l-2,x:Lt(o,i)})}else(r||i)&&a.push("translate("+r+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,s,l),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:Lt(e,t)})):t&&n.push(r(n)+"rotate("+t+o)}(i.rotate,a.rotate,s,l),function(e,t,n,i){e!==t?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:Lt(e,t)}):t&&n.push(r(n)+"skewX("+t+o)}(i.skewX,a.skewX,s,l),function(e,t,n,o,i,a){if(e!==n||t!==o){var s=i.push(r(i)+"scale(",null,",",null,")");a.push({i:s-4,x:Lt(e,n)},{i:s-2,x:Lt(t,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,s,l),i=a=null,function(e){for(var t,n=-1,o=l.length;++n<o;)s[(t=l[n]).i]=t.x(e);return s.join("")}}}var jt=Wt((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Ft:Zt(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),qt=Wt((function(e){return null==e?Ft:(Vt||(Vt=document.createElementNS("http://www.w3.org/2000/svg","g")),Vt.setAttribute("transform",e),(e=Vt.transform.baseVal.consolidate())?Zt((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):Ft)}),", ",")",")");function Ut(e){return((e=Math.exp(e))+1/e)/2}var Gt,Qt,Jt=function e(t,n,o){function r(e,r){var i,a,s=e[0],l=e[1],c=e[2],u=r[0],d=r[1],h=r[2],f=u-s,g=d-l,p=f*f+g*g;if(p<1e-12)a=Math.log(h/c)/t,i=function(e){return[s+e*f,l+e*g,c*Math.exp(t*e*a)]};else{var m=Math.sqrt(p),y=(h*h-c*c+o*p)/(2*c*n*m),v=(h*h-c*c-o*p)/(2*h*n*m),b=Math.log(Math.sqrt(y*y+1)-y),x=Math.log(Math.sqrt(v*v+1)-v);a=(x-b)/t,i=function(e){var o,r=e*a,i=Ut(b),u=c/(n*m)*(i*(o=t*r+b,((o=Math.exp(2*o))-1)/(o+1))-function(e){return((e=Math.exp(e))-1/e)/2}(b));return[s+u*f,l+u*g,c*i/Ut(t*r+b)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return r.rho=function(t){var n=Math.max(.001,+t),o=n*n;return e(n,o,o*o)},r}(Math.SQRT2,2,4),en=0,tn=0,nn=0,on=0,rn=0,an=0,sn="object"==typeof performance&&performance.now?performance:Date,ln="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function cn(){return rn||(ln(un),rn=sn.now()+an)}function un(){rn=0}function dn(){this._call=this._time=this._next=null}function hn(e,t,n){var o=new dn;return o.restart(e,t,n),o}function fn(){rn=(on=sn.now())+an,en=tn=0;try{!function(){cn(),++en;for(var e,t=Gt;t;)(e=rn-t._time)>=0&&t._call.call(void 0,e),t=t._next;--en}()}finally{en=0,function(){var e,t,n=Gt,o=1/0;for(;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:Gt=t);Qt=e,pn(o)}(),rn=0}}function gn(){var e=sn.now(),t=e-on;t>1e3&&(an-=t,on=e)}function pn(e){en||(tn&&(tn=clearTimeout(tn)),e-rn>24?(e<1/0&&(tn=setTimeout(fn,e-sn.now()-an)),nn&&(nn=clearInterval(nn))):(nn||(on=sn.now(),nn=setInterval(gn,1e3)),en=1,ln(fn)))}function mn(e,t,n){var o=new dn;return t=null==t?0:+t,o.restart((n=>{o.stop(),e(n+t)}),t,n),o}dn.prototype=hn.prototype={constructor:dn,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?cn():+n)+(null==t?0:+t),this._next||Qt===this||(Qt?Qt._next=this:Gt=this,Qt=this),this._call=e,this._time=n,pn()},stop:function(){this._call&&(this._call=null,this._time=1/0,pn())}};var yn=r("start","end","cancel","interrupt"),vn=[];function bn(e,t,n,o,r,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var o,r=e.__transition;function i(e){n.state=1,n.timer.restart(a,n.delay,n.time),n.delay<=e&&a(e-n.delay)}function a(i){var c,u,d,h;if(1!==n.state)return l();for(c in r)if((h=r[c]).name===n.name){if(3===h.state)return mn(a);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[c]):+c<t&&(h.state=6,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[c])}if(mn((function(){3===n.state&&(n.state=4,n.timer.restart(s,n.delay,n.time),s(i))})),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(n.state=3,o=new Array(d=n.tween.length),c=0,u=-1;c<d;++c)(h=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(t){for(var r=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=o.length;++i<a;)o[i].call(e,r);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var o in n.state=6,n.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=n,n.timer=hn(i,0,n.time)}(e,n,{name:t,index:o,group:r,on:yn,tween:vn,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function xn(e,t){var n=Sn(e,t);if(n.state>0)throw new Error("too late; already scheduled");return n}function wn(e,t){var n=Sn(e,t);if(n.state>3)throw new Error("too late; already running");return n}function Sn(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function En(e,t){var n,o,r,i=e.__transition,a=!0;if(i){for(r in t=null==t?null:t+"",i)(n=i[r]).name===t?(o=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):a=!1;a&&delete e.__transition}}function _n(e,t){var n,o;return function(){var r=wn(this,e),i=r.tween;if(i!==n)for(var a=0,s=(o=n=i).length;a<s;++a)if(o[a].name===t){(o=o.slice()).splice(a,1);break}r.tween=o}}function Cn(e,t,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=wn(this,e),a=i.tween;if(a!==o){r=(o=a).slice();for(var s={name:t,value:n},l=0,c=r.length;l<c;++l)if(r[l].name===t){r[l]=s;break}l===c&&r.push(s)}i.tween=r}}function Nn(e,t,n){var o=e._id;return e.each((function(){var e=wn(this,o);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return Sn(e,o).value[t]}}function Mn(e,t){var n;return("number"==typeof t?Lt:t instanceof vt?Bt:(n=vt(t))?(t=n,Bt):Yt)(e,t)}function kn(e){return function(){this.removeAttribute(e)}}function An(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Pn(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===o?r:r=t(o=a,n)}}function In(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===o?r:r=t(o=a,n)}}function On(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttribute(e))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttribute(e)}}function Rn(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttributeNS(e.space,e.local))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttributeNS(e.space,e.local)}}function zn(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Dn(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function $n(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&Dn(e,r)),n}return r._value=t,r}function Tn(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&zn(e,r)),n}return r._value=t,r}function Bn(e,t){return function(){xn(this,e).delay=+t.apply(this,arguments)}}function Ln(e,t){return t=+t,function(){xn(this,e).delay=t}}function Hn(e,t){return function(){wn(this,e).duration=+t.apply(this,arguments)}}function Xn(e,t){return t=+t,function(){wn(this,e).duration=t}}function Yn(e,t){if("function"!=typeof t)throw new Error;return function(){wn(this,e).ease=t}}function Vn(e,t,n){var o,r,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?xn:wn;return function(){var a=i(this,e),s=a.on;s!==o&&(r=(o=s).copy()).on(t,n),a.on=r}}var Kn=Se.prototype.constructor;function Fn(e){return function(){this.style.removeProperty(e)}}function Zn(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}function Wn(e,t,n){var o,r;function i(){var i=t.apply(this,arguments);return i!==r&&(o=(r=i)&&Zn(e,i,n)),o}return i._value=t,i}function jn(e){return function(t){this.textContent=e.call(this,t)}}function qn(e){var t,n;function o(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&jn(o)),t}return o._value=e,o}var Un=0;function Gn(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function Qn(){return++Un}var Jn=Se.prototype;Gn.prototype={constructor:Gn,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=m(e));for(var o=this._groups,r=o.length,i=new Array(r),a=0;a<r;++a)for(var s,l,c=o[a],u=c.length,d=i[a]=new Array(u),h=0;h<u;++h)(s=c[h])&&(l=e.call(s,s.__data__,h,c))&&("__data__"in s&&(l.__data__=s.__data__),d[h]=l,bn(d[h],t,n,h,d,Sn(s,n)));return new Gn(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=b(e));for(var o=this._groups,r=o.length,i=[],a=[],s=0;s<r;++s)for(var l,c=o[s],u=c.length,d=0;d<u;++d)if(l=c[d]){for(var h,f=e.call(l,l.__data__,d,c),g=Sn(l,n),p=0,m=f.length;p<m;++p)(h=f[p])&&bn(h,t,n,p,f,g);i.push(f),a.push(l)}return new Gn(i,a,t,n)},selectChild:Jn.selectChild,selectChildren:Jn.selectChildren,filter:function(e){"function"!=typeof e&&(e=x(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new Gn(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),a=new Array(o),s=0;s<i;++s)for(var l,c=t[s],u=n[s],d=c.length,h=a[s]=new Array(d),f=0;f<d;++f)(l=c[f]||u[f])&&(h[f]=l);for(;s<o;++s)a[s]=t[s];return new Gn(a,this._parents,this._name,this._id)},selection:function(){return new Kn(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=Qn(),o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)if(a=s[c]){var u=Sn(a,t);bn(a,e,n,c,s,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new Gn(o,this._parents,e,n)},call:Jn.call,nodes:Jn.nodes,node:Jn.node,size:Jn.size,empty:Jn.empty,each:Jn.each,on:function(e,t){var n=this._id;return arguments.length<2?Sn(this.node(),n).on.on(e):this.each(Vn(n,e,t))},attr:function(e,t){var n=d(e),o="transform"===n?qt:Mn;return this.attrTween(e,"function"==typeof t?(n.local?Rn:On)(n,o,Nn(this,"attr."+e,t)):null==t?(n.local?An:kn)(n):(n.local?In:Pn)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var o=d(e);return this.tween(n,(o.local?$n:Tn)(o,t))},style:function(e,t,n){var o="transform"==(e+="")?jt:Mn;return null==t?this.styleTween(e,function(e,t){var n,o,r;return function(){var i=K(this,e),a=(this.style.removeProperty(e),K(this,e));return i===a?null:i===n&&a===o?r:r=t(n=i,o=a)}}(e,o)).on("end.style."+e,Fn(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var o,r,i;return function(){var a=K(this,e),s=n(this),l=s+"";return null==s&&(this.style.removeProperty(e),l=s=K(this,e)),a===l?null:a===o&&l===r?i:(r=l,i=t(o=a,s))}}(e,o,Nn(this,"style."+e,t))).each(function(e,t){var n,o,r,i,a="style."+t,s="end."+a;return function(){var l=wn(this,e),c=l.on,u=null==l.value[a]?i||(i=Fn(t)):void 0;c===n&&r===u||(o=(n=c).copy()).on(s,r=u),l.on=o}}(this._id,e)):this.styleTween(e,function(e,t,n){var o,r,i=n+"";return function(){var a=K(this,e);return a===i?null:a===o?r:r=t(o=a,n)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==t)return this.tween(o,null);if("function"!=typeof t)throw new Error;return this.tween(o,Wn(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(Nn(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,qn(e))},remove:function(){return this.on("end.remove",function(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}(this._id))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=Sn(this.node(),n).tween,i=0,a=r.length;i<a;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((null==t?_n:Cn)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Bn:Ln)(t,e)):Sn(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Hn:Xn)(t,e)):Sn(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(Yn(t,e)):Sn(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;wn(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise((function(i,a){var s={value:a},l={value:function(){0==--r&&i()}};n.each((function(){var n=wn(this,o),r=n.on;r!==e&&((t=(e=r).copy())._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),n.on=t})),0===r&&i()}))},[Symbol.iterator]:Jn[Symbol.iterator]};var eo={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function to(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}Se.prototype.interrupt=function(e){return this.each((function(){En(this,e)}))},Se.prototype.transition=function(e){var t,n;e instanceof Gn?(t=e._id,e=e._name):(t=Qn(),(n=eo).time=cn(),e=null==e?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)(a=s[c])&&bn(a,e,t,c,s,n||to(a,t));return new Gn(o,this._parents,e,t)};var no=e=>()=>e;function oo(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function ro(e,t,n){this.k=e,this.x=t,this.y=n}ro.prototype={constructor:ro,scale:function(e){return 1===e?this:new ro(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new ro(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var io=new ro(1,0,0);function ao(e){e.stopImmediatePropagation()}function so(e){e.preventDefault(),e.stopImmediatePropagation()}function lo(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function co(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function uo(){return this.__zoom||io}function ho(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function fo(){return navigator.maxTouchPoints||"ontouchstart"in this}function go(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}ro.prototype;const po=t.createContext(null),mo=po.Provider,yo=e=>`Node type "${e}" not found. Using fallback type "default".`,vo=()=>"The React Flow parent container needs a width and a height to render the graph.",bo=()=>"Only child nodes can use a parent extent.",xo=e=>`Marker type "${e}" doesn't exist.`,wo=(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,So=()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",Eo=e=>`Edge type "${e}" not found. Using fallback type "default".`,_o=e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,Co=(()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001")();function No(e,n){const o=t.useContext(po);if(null===o)throw new Error(Co);return Ge(o,e,n)}const Mo=()=>{const e=t.useContext(po);if(null===e)throw new Error(Co);return t.useMemo((()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy})),[e])},ko=e=>e.userSelectionActive?"none":"all";function Ao({position:e,children:o,className:r,style:i,...a}){const s=No(ko),l=`${e}`.split("-");return t.createElement("div",{className:n(["react-flow__panel",r,...l]),style:{...i,pointerEvents:s},...a},o)}function Po({proOptions:e,position:n="bottom-right"}){return e?.hideAttribution?null:t.createElement(Ao,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},t.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var Io=t.memo((({x:e,y:o,label:r,labelStyle:i={},labelShowBg:a=!0,labelBgStyle:s={},labelBgPadding:l=[2,4],labelBgBorderRadius:c=2,children:u,className:d,...h})=>{const f=t.useRef(null),[g,p]=t.useState({x:0,y:0,width:0,height:0}),m=n(["react-flow__edge-textwrapper",d]);return t.useEffect((()=>{if(f.current){const e=f.current.getBBox();p({x:e.x,y:e.y,width:e.width,height:e.height})}}),[r]),void 0!==r&&r?t.createElement("g",{transform:`translate(${e-g.width/2} ${o-g.height/2})`,className:m,visibility:g.width?"visible":"hidden",...h},a&&t.createElement("rect",{width:g.width+2*l[0],x:-l[0],y:-l[1],height:g.height+2*l[1],className:"react-flow__edge-textbg",style:s,rx:c,ry:c}),t.createElement("text",{className:"react-flow__edge-text",y:g.height/2,dy:"0.3em",ref:f,style:i},r),u):null}));const Oo=e=>({width:e.offsetWidth,height:e.offsetHeight}),Ro=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),zo=(e={x:0,y:0},t)=>({x:Ro(e.x,t[0][0],t[1][0]),y:Ro(e.y,t[0][1],t[1][1])}),Do=(e,t,n)=>e<t?Ro(Math.abs(e-t),1,50)/50:e>n?-Ro(Math.abs(e-n),1,50)/50:0,$o=(e,t)=>[20*Do(e.x,35,t.width-35),20*Do(e.y,35,t.height-35)],To=e=>e.getRootNode?.()||window?.document,Bo=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),Lo=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),Ho=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)},Xo=e=>!isNaN(e)&&isFinite(e),Yo=Symbol.for("internals"),Vo=["Enter"," ","Escape"];function Ko(e){const t=((e=>"nativeEvent"in e)(e)?e.nativeEvent:e).composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(t?.nodeName)||t?.hasAttribute("contenteditable")||!!t?.closest(".nokey")}const Fo=e=>"clientX"in e,Zo=(e,t)=>{const n=Fo(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},Wo=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,jo=({id:e,path:n,labelX:o,labelY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g=20})=>t.createElement(t.Fragment,null,t.createElement("path",{id:e,style:d,d:n,fill:"none",className:"react-flow__edge-path",markerEnd:h,markerStart:f}),g&&t.createElement("path",{d:n,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),i&&Xo(o)&&Xo(r)?t.createElement(Io,{x:o,y:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u}):null);function qo(e,t,n){return void 0===n?n:o=>{const r=t().edges.find((t=>t.id===e));r&&n(o,{...r})}}function Uo({sourceX:e,sourceY:t,targetX:n,targetY:o}){const r=Math.abs(n-e)/2,i=n<e?n+r:n-r,a=Math.abs(o-t)/2;return[i,o<t?o+a:o-a,r,a]}function Go({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:a,targetControlY:s}){const l=.125*e+.375*r+.375*a+.125*n,c=.125*t+.375*i+.375*s+.125*o;return[l,c,Math.abs(l-e),Math.abs(c-t)]}var Qo,Jo,er,tr,nr,or;function rr({pos:e,x1:t,y1:n,x2:o,y2:r}){return e===or.Left||e===or.Right?[.5*(t+o),n]:[t,.5*(n+r)]}function ir({sourceX:e,sourceY:t,sourcePosition:n=or.Bottom,targetX:o,targetY:r,targetPosition:i=or.Top}){const[a,s]=rr({pos:n,x1:e,y1:t,x2:o,y2:r}),[l,c]=rr({pos:i,x1:o,y1:r,x2:e,y2:t}),[u,d,h,f]=Go({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:a,sourceControlY:s,targetControlX:l,targetControlY:c});return[`M${e},${t} C${a},${s} ${l},${c} ${o},${r}`,u,d,h,f]}jo.displayName="BaseEdge",function(e){e.Strict="strict",e.Loose="loose"}(Qo||(Qo={})),function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"}(Jo||(Jo={})),function(e){e.Partial="partial",e.Full="full"}(er||(er={})),function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"}(tr||(tr={})),function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"}(nr||(nr={})),function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"}(or||(or={}));const ar=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,sourcePosition:i=or.Bottom,targetPosition:a=or.Top,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:m})=>{const[y,v,b]=ir({sourceX:e,sourceY:n,sourcePosition:i,targetX:o,targetY:r,targetPosition:a});return t.createElement(jo,{path:y,labelX:v,labelY:b,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:m})}));ar.displayName="SimpleBezierEdge";const sr={[or.Left]:{x:-1,y:0},[or.Right]:{x:1,y:0},[or.Top]:{x:0,y:-1},[or.Bottom]:{x:0,y:1}},lr=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function cr({source:e,sourcePosition:t=or.Bottom,target:n,targetPosition:o=or.Top,center:r,offset:i}){const a=sr[t],s=sr[o],l={x:e.x+a.x*i,y:e.y+a.y*i},c={x:n.x+s.x*i,y:n.y+s.y*i},u=(({source:e,sourcePosition:t=or.Bottom,target:n})=>t===or.Left||t===or.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1})({source:l,sourcePosition:t,target:c}),d=0!==u.x?"x":"y",h=u[d];let f,g,p=[];const m={x:0,y:0},y={x:0,y:0},[v,b,x,w]=Uo({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[d]*s[d]==-1){f=r.x??v,g=r.y??b;const e=[{x:f,y:l.y},{x:f,y:c.y}],t=[{x:l.x,y:g},{x:c.x,y:g}];p=a[d]===h?"x"===d?e:t:"x"===d?t:e}else{const r=[{x:l.x,y:c.y}],u=[{x:c.x,y:l.y}];if(p="x"===d?a.x===h?u:r:a.y===h?r:u,t===o){const t=Math.abs(e[d]-n[d]);if(t<=i){const o=Math.min(i-1,i-t);a[d]===h?m[d]=(l[d]>e[d]?-1:1)*o:y[d]=(c[d]>n[d]?-1:1)*o}}if(t!==o){const e="x"===d?"y":"x",t=a[d]===s[e],n=l[e]>c[e],o=l[e]<c[e];(1===a[d]&&(!t&&n||t&&o)||1!==a[d]&&(!t&&o||t&&n))&&(p="x"===d?r:u)}const v={x:l.x+m.x,y:l.y+m.y},b={x:c.x+y.x,y:c.y+y.y};Math.max(Math.abs(v.x-p[0].x),Math.abs(b.x-p[0].x))>=Math.max(Math.abs(v.y-p[0].y),Math.abs(b.y-p[0].y))?(f=(v.x+b.x)/2,g=p[0].y):(f=p[0].x,g=(v.y+b.y)/2)}return[[e,{x:l.x+m.x,y:l.y+m.y},...p,{x:c.x+y.x,y:c.y+y.y},n],f,g,x,w]}function ur({sourceX:e,sourceY:t,sourcePosition:n=or.Bottom,targetX:o,targetY:r,targetPosition:i=or.Top,borderRadius:a=5,centerX:s,centerY:l,offset:c=20}){const[u,d,h,f,g]=cr({source:{x:e,y:t},sourcePosition:n,target:{x:o,y:r},targetPosition:i,center:{x:s,y:l},offset:c});return[u.reduce(((e,t,n)=>{let o="";return o=n>0&&n<u.length-1?function(e,t,n,o){const r=Math.min(lr(e,t)/2,lr(t,n)/2,o),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a)return`L ${i+r*(e.x<n.x?-1:1)},${a}Q ${i},${a} ${i},${a+r*(e.y<n.y?1:-1)}`;const s=e.x<n.x?1:-1;return`L ${i},${a+r*(e.y<n.y?-1:1)}Q ${i},${a} ${i+r*s},${a}`}(u[n-1],t,u[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`,e+=o}),""),d,h,f,g]}const dr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,sourcePosition:h=or.Bottom,targetPosition:f=or.Top,markerEnd:g,markerStart:p,pathOptions:m,interactionWidth:y})=>{const[v,b,x]=ur({sourceX:e,sourceY:n,sourcePosition:h,targetX:o,targetY:r,targetPosition:f,borderRadius:m?.borderRadius,offset:m?.offset});return t.createElement(jo,{path:v,labelX:b,labelY:x,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:g,markerStart:p,interactionWidth:y})}));dr.displayName="SmoothStepEdge";const hr=t.memo((e=>t.createElement(dr,{...e,pathOptions:t.useMemo((()=>({borderRadius:0,offset:e.pathOptions?.offset})),[e.pathOptions?.offset])})));hr.displayName="StepEdge";const fr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})=>{const[p,m,y]=function({sourceX:e,sourceY:t,targetX:n,targetY:o}){const[r,i,a,s]=Uo({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,i,a,s]}({sourceX:e,sourceY:n,targetX:o,targetY:r});return t.createElement(jo,{path:p,labelX:m,labelY:y,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})}));function gr(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function pr({pos:e,x1:t,y1:n,x2:o,y2:r,c:i}){switch(e){case or.Left:return[t-gr(t-o,i),n];case or.Right:return[t+gr(o-t,i),n];case or.Top:return[t,n-gr(n-r,i)];case or.Bottom:return[t,n+gr(r-n,i)]}}function mr({sourceX:e,sourceY:t,sourcePosition:n=or.Bottom,targetX:o,targetY:r,targetPosition:i=or.Top,curvature:a=.25}){const[s,l]=pr({pos:n,x1:e,y1:t,x2:o,y2:r,c:a}),[c,u]=pr({pos:i,x1:o,y1:r,x2:e,y2:t,c:a}),[d,h,f,g]=Go({sourceX:e,sourceY:t,targetX:o,targetY:r,sourceControlX:s,sourceControlY:l,targetControlX:c,targetControlY:u});return[`M${e},${t} C${s},${l} ${c},${u} ${o},${r}`,d,h,f,g]}fr.displayName="StraightEdge";const yr=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,sourcePosition:i=or.Bottom,targetPosition:a=or.Top,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,pathOptions:m,interactionWidth:y})=>{const[v,b,x]=mr({sourceX:e,sourceY:n,sourcePosition:i,targetX:o,targetY:r,targetPosition:a,curvature:m?.curvature});return t.createElement(jo,{path:v,labelX:b,labelY:x,label:s,labelStyle:l,labelShowBg:c,labelBgStyle:u,labelBgPadding:d,labelBgBorderRadius:h,style:f,markerEnd:g,markerStart:p,interactionWidth:y})}));yr.displayName="BezierEdge";const vr=t.createContext(null),br=vr.Provider;vr.Consumer;const xr=()=>t.useContext(vr),wr=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`reactflow__edge-${e}${t||""}-${n}${o||""}`,Sr=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;return`${t?`${t}__`:""}${Object.keys(e).sort().map((t=>`${t}=${e[t]}`)).join("&")}`},Er=({x:e,y:t},[n,o,r],i,[a,s])=>{const l={x:(e-n)/r,y:(t-o)/r};return i?{x:a*Math.round(l.x/a),y:s*Math.round(l.y/s)}:l},_r=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),Cr=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};const n=(e.width??0)*t[0],o=(e.height??0)*t[1],r={x:e.position.x-n,y:e.position.y-o};return{...r,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-o}:r}},Nr=(e,t=[0,0])=>{if(0===e.length)return{x:0,y:0,width:0,height:0};return(({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}))(e.reduce(((e,n)=>{const{x:o,y:r}=Cr(n,t).positionAbsolute;return i=e,a=Bo({x:o,y:r,width:n.width||0,height:n.height||0}),{x:Math.min(i.x,a.x),y:Math.min(i.y,a.y),x2:Math.max(i.x2,a.x2),y2:Math.max(i.y2,a.y2)};var i,a}),{x:1/0,y:1/0,x2:-1/0,y2:-1/0}))},Mr=(e,t,[n,o,r]=[0,0,1],i=!1,a=!1,s=[0,0])=>{const l={x:(t.x-n)/r,y:(t.y-o)/r,width:t.width/r,height:t.height/r},c=[];return e.forEach((e=>{const{width:t,height:n,selectable:o=!0,hidden:r=!1}=e;if(a&&!o||r)return!1;const{positionAbsolute:u}=Cr(e,s),d={x:u.x,y:u.y,width:t||0,height:n||0},h=Ho(l,d);(void 0===t||void 0===n||null===t||null===n||i&&h>0||h>=(t||0)*(n||0)||e.dragging)&&c.push(e)})),c},kr=(e,t)=>{const n=e.map((e=>e.id));return t.filter((e=>n.includes(e.source)||n.includes(e.target)))},Ar=(e,t,n,o,r,i=.1)=>{const a=t/(e.width*(1+i)),s=n/(e.height*(1+i)),l=Math.min(a,s),c=Ro(l,o,r);return{x:t/2-(e.x+e.width/2)*c,y:n/2-(e.y+e.height/2)*c,zoom:c}},Pr=(e,t=0)=>e.transition().duration(t);function Ir(e,t,n,o){return(t[n]||[]).reduce(((t,r)=>(`${e.id}-${r.id}-${n}`!==o&&t.push({id:r.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+r.x+r.width/2,y:(e.positionAbsolute?.y??0)+r.y+r.height/2}),t)),[])}const Or={source:null,target:null,sourceHandle:null,targetHandle:null},Rr=()=>({handleDomNode:null,isValid:!1,connection:Or,endHandle:null});function zr(e,t,n,o,r,i,a){const s="target"===r,l=a.querySelector(`.react-flow__handle[data-id="${e?.nodeId}-${e?.id}-${e?.type}"]`),c={...Rr(),handleDomNode:l};if(l){const e=Dr(void 0,l),r=l.getAttribute("data-nodeid"),a=l.getAttribute("data-handleid"),u=l.classList.contains("connectable"),d=l.classList.contains("connectableend"),h={source:s?r:n,sourceHandle:s?a:o,target:s?n:r,targetHandle:s?o:a};c.connection=h;u&&d&&(t===Qo.Strict?s&&"source"===e||!s&&"target"===e:r!==n||a!==o)&&(c.endHandle={nodeId:r,handleId:a,type:e},c.isValid=i(h))}return c}function Dr(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function $r(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function Tr(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function Br({event:e,handleId:t,nodeId:n,onConnect:o,isTarget:r,getState:i,setState:a,isValidConnection:s,edgeUpdaterType:l,onReconnectEnd:c}){const u=To(e.target),{connectionMode:d,domNode:h,autoPanOnConnect:f,connectionRadius:g,onConnectStart:p,panBy:m,getNodes:y,cancelConnection:v}=i();let b,x=0;const{x:w,y:S}=Zo(e),E=u?.elementFromPoint(w,S),_=Dr(l,E),C=h?.getBoundingClientRect();if(!C||!_)return;let N,M=Zo(e,C),k=!1,A=null,P=!1,I=null;const O=function({nodes:e,nodeId:t,handleId:n,handleType:o}){return e.reduce(((e,r)=>{if(r[Yo]){const{handleBounds:i}=r[Yo];let a=[],s=[];i&&(a=Ir(r,i,"source",`${t}-${n}-${o}`),s=Ir(r,i,"target",`${t}-${n}-${o}`)),e.push(...a,...s)}return e}),[])}({nodes:y(),nodeId:n,handleId:t,handleType:_}),R=()=>{if(!f)return;const[e,t]=$o(M,C);m({x:e,y:t}),x=requestAnimationFrame(R)};function z(e){const{transform:o}=i();M=Zo(e,C);const{handle:l,validHandleResult:c}=function(e,t,n,o,r,i){const{x:a,y:s}=Zo(e),l=t.elementsFromPoint(a,s).find((e=>e.classList.contains("react-flow__handle")));if(l){const e=l.getAttribute("data-nodeid");if(e){const t=Dr(void 0,l),o=l.getAttribute("data-handleid"),a=i({nodeId:e,id:o,type:t});if(a){const i=r.find((n=>n.nodeId===e&&n.type===t&&n.id===o));return{handle:{id:o,type:t,nodeId:e,x:i?.x||n.x,y:i?.y||n.y},validHandleResult:a}}}}let c=[],u=1/0;if(r.forEach((e=>{const t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){const n=i(e);t<=u&&(t<u?c=[{handle:e,validHandleResult:n}]:t===u&&c.push({handle:e,validHandleResult:n}),u=t)}})),!c.length)return{handle:null,validHandleResult:Rr()};if(1===c.length)return c[0];const d=c.some((({validHandleResult:e})=>e.isValid)),h=c.some((({handle:e})=>"target"===e.type));return c.find((({handle:e,validHandleResult:t})=>h?"target"===e.type:!d||t.isValid))||c[0]}(e,u,Er(M,o,!1,[1,1]),g,O,(e=>zr(e,d,n,t,r?"target":"source",s,u)));if(b=l,k||(R(),k=!0),I=c.handleDomNode,A=c.connection,P=c.isValid,a({connectionPosition:b&&P?_r({x:b.x,y:b.y},o):M,connectionStatus:Tr(!!b,P),connectionEndHandle:c.endHandle}),!b&&!P&&!I)return $r(N);A.source!==A.target&&I&&($r(N),N=I,I.classList.add("connecting","react-flow__handle-connecting"),I.classList.toggle("valid",P),I.classList.toggle("react-flow__handle-valid",P))}function D(e){(b||I)&&A&&P&&o?.(A),i().onConnectEnd?.(e),l&&c?.(e),$r(N),v(),cancelAnimationFrame(x),k=!1,P=!1,A=null,I=null,u.removeEventListener("mousemove",z),u.removeEventListener("mouseup",D),u.removeEventListener("touchmove",z),u.removeEventListener("touchend",D)}a({connectionPosition:M,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:_,connectionStartHandle:{nodeId:n,handleId:t,type:_},connectionEndHandle:null}),p?.(e,{nodeId:n,handleId:t,handleType:_}),u.addEventListener("mousemove",z),u.addEventListener("mouseup",D),u.addEventListener("touchmove",z),u.addEventListener("touchend",D)}const Lr=()=>!0,Hr=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),Xr=t.forwardRef((({type:e="source",position:o=or.Top,isValidConnection:r,isConnectable:i=!0,isConnectableStart:a=!0,isConnectableEnd:s=!0,id:l,onConnect:c,children:u,className:d,onMouseDown:h,onTouchStart:f,...g},p)=>{const m=l||null,y="target"===e,v=Mo(),b=xr(),{connectOnClick:x,noPanClassName:w}=No(Hr,Je),{connecting:S,clickConnecting:E}=No(((e,t,n)=>o=>{const{connectionStartHandle:r,connectionEndHandle:i,connectionClickStartHandle:a}=o;return{connecting:r?.nodeId===e&&r?.handleId===t&&r?.type===n||i?.nodeId===e&&i?.handleId===t&&i?.type===n,clickConnecting:a?.nodeId===e&&a?.handleId===t&&a?.type===n}})(b,m,e),Je);b||v.getState().onError?.("010",So());const _=e=>{const{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=v.getState(),r={...t,...e};if(o){const{edges:e,setEdges:t}=v.getState();t(((e,t)=>{if(!e.source||!e.target)return t;let n;var o;return n="id"in(o=e)&&"source"in o&&"target"in o?{...e}:{...e,id:wr(e)},((e,t)=>t.some((t=>!(t.source!==e.source||t.target!==e.target||t.sourceHandle!==e.sourceHandle&&(t.sourceHandle||e.sourceHandle)||t.targetHandle!==e.targetHandle&&(t.targetHandle||e.targetHandle)))))(n,t)?t:t.concat(n)})(r,e))}n?.(r),c?.(r)},C=e=>{if(!b)return;const t=Fo(e);a&&(t&&0===e.button||!t)&&Br({event:e,handleId:m,nodeId:b,onConnect:_,isTarget:y,getState:v.getState,setState:v.setState,isValidConnection:r||v.getState().isValidConnection||Lr}),t?h?.(e):f?.(e)};return t.createElement("div",{"data-handleid":m,"data-nodeid":b,"data-handlepos":o,"data-id":`${b}-${m}-${e}`,className:n(["react-flow__handle",`react-flow__handle-${o}`,"nodrag",w,d,{source:!y,target:y,connectable:i,connectablestart:a,connectableend:s,connecting:E,connectionindicator:i&&(a&&!S||s&&S)}]),onMouseDown:C,onTouchStart:C,onClick:x?t=>{const{onClickConnectStart:n,onClickConnectEnd:o,connectionClickStartHandle:i,connectionMode:s,isValidConnection:l}=v.getState();if(!b||!i&&!a)return;if(!i)return n?.(t,{nodeId:b,handleId:m,handleType:e}),void v.setState({connectionClickStartHandle:{nodeId:b,type:e,handleId:m}});const c=To(t.target),u=r||l||Lr,{connection:d,isValid:h}=zr({nodeId:b,id:m,type:e},s,i.nodeId,i.handleId||null,i.type,u,c);h&&_(d),o?.(t),v.setState({connectionClickStartHandle:null})}:void 0,ref:p,...g},u)}));Xr.displayName="Handle";var Yr=t.memo(Xr);const Vr=({data:e,isConnectable:n,targetPosition:o=or.Top,sourcePosition:r=or.Bottom})=>t.createElement(t.Fragment,null,t.createElement(Yr,{type:"target",position:o,isConnectable:n}),e?.label,t.createElement(Yr,{type:"source",position:r,isConnectable:n}));Vr.displayName="DefaultNode";var Kr=t.memo(Vr);const Fr=({data:e,isConnectable:n,sourcePosition:o=or.Bottom})=>t.createElement(t.Fragment,null,e?.label,t.createElement(Yr,{type:"source",position:o,isConnectable:n}));Fr.displayName="InputNode";var Zr=t.memo(Fr);const Wr=({data:e,isConnectable:n,targetPosition:o=or.Top})=>t.createElement(t.Fragment,null,t.createElement(Yr,{type:"target",position:o,isConnectable:n}),e?.label);Wr.displayName="OutputNode";var jr=t.memo(Wr);const qr=()=>null;qr.displayName="GroupNode";const Ur=e=>({selectedNodes:e.getNodes().filter((e=>e.selected)),selectedEdges:e.edges.filter((e=>e.selected)).map((e=>({...e})))}),Gr=e=>e.id;function Qr(e,t){return Je(e.selectedNodes.map(Gr),t.selectedNodes.map(Gr))&&Je(e.selectedEdges.map(Gr),t.selectedEdges.map(Gr))}const Jr=t.memo((({onSelectionChange:e})=>{const n=Mo(),{selectedNodes:o,selectedEdges:r}=No(Ur,Qr);return t.useEffect((()=>{const t={nodes:o,edges:r};e?.(t),n.getState().onSelectionChange.forEach((e=>e(t)))}),[o,r,e]),null}));Jr.displayName="SelectionListener";const ei=e=>!!e.onSelectionChange;function ti({onSelectionChange:e}){const n=No(ei);return e||n?t.createElement(Jr,{onSelectionChange:e}):null}const ni=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function oi(e,n){t.useEffect((()=>{void 0!==e&&n(e)}),[e])}function ri(e,n,o){t.useEffect((()=>{void 0!==n&&o({[e]:n})}),[n])}const ii=({nodes:e,edges:n,defaultNodes:o,defaultEdges:r,onConnect:i,onConnectStart:a,onConnectEnd:s,onClickConnectStart:l,onClickConnectEnd:c,nodesDraggable:u,nodesConnectable:d,nodesFocusable:h,edgesFocusable:f,edgesUpdatable:g,elevateNodesOnSelect:p,minZoom:m,maxZoom:y,nodeExtent:v,onNodesChange:b,onEdgesChange:x,elementsSelectable:w,connectionMode:S,snapGrid:E,snapToGrid:_,translateExtent:C,connectOnClick:N,defaultEdgeOptions:M,fitView:k,fitViewOptions:A,onNodesDelete:P,onEdgesDelete:I,onNodeDrag:O,onNodeDragStart:R,onNodeDragStop:z,onSelectionDrag:D,onSelectionDragStart:$,onSelectionDragStop:T,noPanClassName:B,nodeOrigin:L,rfId:H,autoPanOnConnect:X,autoPanOnNodeDrag:Y,onError:V,connectionRadius:K,isValidConnection:F,nodeDragThreshold:Z})=>{const{setNodes:W,setEdges:j,setDefaultNodesAndEdges:q,setMinZoom:U,setMaxZoom:G,setTranslateExtent:Q,setNodeExtent:J,reset:ee}=No(ni,Je),te=Mo();return t.useEffect((()=>{const e=r?.map((e=>({...e,...M})));return q(o,e),()=>{ee()}}),[]),ri("defaultEdgeOptions",M,te.setState),ri("connectionMode",S,te.setState),ri("onConnect",i,te.setState),ri("onConnectStart",a,te.setState),ri("onConnectEnd",s,te.setState),ri("onClickConnectStart",l,te.setState),ri("onClickConnectEnd",c,te.setState),ri("nodesDraggable",u,te.setState),ri("nodesConnectable",d,te.setState),ri("nodesFocusable",h,te.setState),ri("edgesFocusable",f,te.setState),ri("edgesUpdatable",g,te.setState),ri("elementsSelectable",w,te.setState),ri("elevateNodesOnSelect",p,te.setState),ri("snapToGrid",_,te.setState),ri("snapGrid",E,te.setState),ri("onNodesChange",b,te.setState),ri("onEdgesChange",x,te.setState),ri("connectOnClick",N,te.setState),ri("fitViewOnInit",k,te.setState),ri("fitViewOnInitOptions",A,te.setState),ri("onNodesDelete",P,te.setState),ri("onEdgesDelete",I,te.setState),ri("onNodeDrag",O,te.setState),ri("onNodeDragStart",R,te.setState),ri("onNodeDragStop",z,te.setState),ri("onSelectionDrag",D,te.setState),ri("onSelectionDragStart",$,te.setState),ri("onSelectionDragStop",T,te.setState),ri("noPanClassName",B,te.setState),ri("nodeOrigin",L,te.setState),ri("rfId",H,te.setState),ri("autoPanOnConnect",X,te.setState),ri("autoPanOnNodeDrag",Y,te.setState),ri("onError",V,te.setState),ri("connectionRadius",K,te.setState),ri("isValidConnection",F,te.setState),ri("nodeDragThreshold",Z,te.setState),oi(e,W),oi(n,j),oi(m,U),oi(y,G),oi(C,Q),oi(v,J),null},ai={display:"none"},si={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},li="react-flow__node-desc",ci="react-flow__edge-desc",ui=e=>e.ariaLiveMessage;function di({rfId:e}){const n=No(ui);return t.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:si},n)}function hi({rfId:e,disableKeyboardA11y:n}){return t.createElement(t.Fragment,null,t.createElement("div",{id:`${li}-${e}`,style:ai},"Press enter or space to select a node.",!n&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),t.createElement("div",{id:`${ci}-${e}`,style:ai},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!n&&t.createElement(di,{rfId:e}))}var fi=(e=null,n={actInsideInputWithModifier:!0})=>{const[o,r]=t.useState(!1),i=t.useRef(!1),a=t.useRef(new Set([])),[s,l]=t.useMemo((()=>{if(null!==e){const t=(Array.isArray(e)?e:[e]).filter((e=>"string"==typeof e)).map((e=>e.split("+"))),n=t.reduce(((e,t)=>e.concat(...t)),[]);return[t,n]}return[[],[]]}),[e]);return t.useEffect((()=>{const t="undefined"!=typeof document?document:null,o=n?.target||t;if(null!==e){const e=e=>{i.current=e.ctrlKey||e.metaKey||e.shiftKey;if((!i.current||i.current&&!n.actInsideInputWithModifier)&&Ko(e))return!1;const t=pi(e.code,l);a.current.add(e[t]),gi(s,a.current,!1)&&(e.preventDefault(),r(!0))},t=e=>{if((!i.current||i.current&&!n.actInsideInputWithModifier)&&Ko(e))return!1;const t=pi(e.code,l);gi(s,a.current,!0)?(r(!1),a.current.clear()):a.current.delete(e[t]),"Meta"===e.key&&a.current.clear(),i.current=!1},c=()=>{a.current.clear(),r(!1)};return o?.addEventListener("keydown",e),o?.addEventListener("keyup",t),window.addEventListener("blur",c),()=>{o?.removeEventListener("keydown",e),o?.removeEventListener("keyup",t),window.removeEventListener("blur",c)}}}),[e,r]),o};function gi(e,t,n){return e.filter((e=>n||e.length===t.size)).some((e=>e.every((e=>t.has(e)))))}function pi(e,t){return t.includes(e)?"code":"key"}function mi(e,t,n,o){const r=e.parentNode||e.parentId;if(!r)return n;const i=t.get(r),a=Cr(i,o);return mi(i,t,{x:(n.x??0)+a.x,y:(n.y??0)+a.y,z:(i[Yo]?.z??0)>(n.z??0)?i[Yo]?.z??0:n.z??0},o)}function yi(e,t,n){e.forEach((o=>{const r=o.parentNode||o.parentId;if(r&&!e.has(r))throw new Error(`Parent node ${r} not found`);if(r||n?.[o.id]){const{x:r,y:i,z:a}=mi(o,e,{...o.position,z:o[Yo]?.z??0},t);o.positionAbsolute={x:r,y:i},o[Yo].z=a,n?.[o.id]&&(o[Yo].isParent=!0)}}))}function vi(e,t,n,o){const r=new Map,i={},a=o?1e3:0;return e.forEach((e=>{const n=(Xo(e.zIndex)?e.zIndex:0)+(e.selected?a:0),o=t.get(e.id),s={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},l=e.parentNode||e.parentId;l&&(i[l]=!0);const c=o?.type&&o?.type!==e.type;Object.defineProperty(s,Yo,{enumerable:!1,value:{handleBounds:c?void 0:o?.[Yo]?.handleBounds,z:n}}),r.set(e.id,s)})),yi(r,n,i),r}function bi(e,t={}){const{getNodes:n,width:o,height:r,minZoom:i,maxZoom:a,d3Zoom:s,d3Selection:l,fitViewOnInitDone:c,fitViewOnInit:u,nodeOrigin:d}=e(),h=t.initial&&!c&&u;if(s&&l&&(h||!t.initial)){const e=n().filter((e=>{const n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some((t=>t.id===e.id)):n})),c=e.every((e=>e.width&&e.height));if(e.length>0&&c){const n=Nr(e,d),{x:c,y:u,zoom:h}=Ar(n,o,r,t.minZoom??i,t.maxZoom??a,t.padding??.1),f=io.translate(c,u).scale(h);return"number"==typeof t.duration&&t.duration>0?s.transform(Pr(l,t.duration),f):s.transform(l,f),!0}}return!1}function xi(e,t){return e.forEach((e=>{const n=t.get(e.id);n&&t.set(n.id,{...n,[Yo]:n[Yo],selected:e.selected})})),new Map(t)}function wi(e,t){return t.map((t=>{const n=e.find((e=>e.id===t.id));return n&&(t.selected=n.selected),t}))}function Si({changedNodes:e,changedEdges:t,get:n,set:o}){const{nodeInternals:r,edges:i,onNodesChange:a,onEdgesChange:s,hasDefaultNodes:l,hasDefaultEdges:c}=n();e?.length&&(l&&o({nodeInternals:xi(e,r)}),a?.(e)),t?.length&&(c&&o({edges:wi(t,i)}),s?.(t))}const Ei=()=>{},_i={zoomIn:Ei,zoomOut:Ei,zoomTo:Ei,getZoom:()=>1,setViewport:Ei,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:Ei,fitBounds:Ei,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},Ci=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection});function Ni(){const e=(()=>{const e=Mo(),{d3Zoom:n,d3Selection:o}=No(Ci,Je),r=t.useMemo((()=>o&&n?{zoomIn:e=>n.scaleBy(Pr(o,e?.duration),1.2),zoomOut:e=>n.scaleBy(Pr(o,e?.duration),1/1.2),zoomTo:(e,t)=>n.scaleTo(Pr(o,t?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(t,r)=>{const[i,a,s]=e.getState().transform,l=io.translate(t.x??i,t.y??a).scale(t.zoom??s);n.transform(Pr(o,r?.duration),l)},getViewport:()=>{const[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>bi(e.getState,t),setCenter:(t,r,i)=>{const{width:a,height:s,maxZoom:l}=e.getState(),c=void 0!==i?.zoom?i.zoom:l,u=a/2-t*c,d=s/2-r*c,h=io.translate(u,d).scale(c);n.transform(Pr(o,i?.duration),h)},fitBounds:(t,r)=>{const{width:i,height:a,minZoom:s,maxZoom:l}=e.getState(),{x:c,y:u,zoom:d}=Ar(t,i,a,s,l,r?.padding??.1),h=io.translate(c,u).scale(d);n.transform(Pr(o,r?.duration),h)},project:t=>{const{transform:n,snapToGrid:o,snapGrid:r}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),Er(t,n,o,r)},screenToFlowPosition:t=>{const{transform:n,snapToGrid:o,snapGrid:r,domNode:i}=e.getState();if(!i)return t;const{x:a,y:s}=i.getBoundingClientRect(),l={x:t.x-a,y:t.y-s};return Er(l,n,o,r)},flowToScreenPosition:t=>{const{transform:n,domNode:o}=e.getState();if(!o)return t;const{x:r,y:i}=o.getBoundingClientRect(),a=_r(t,n);return{x:a.x+r,y:a.y+i}},viewportInitialized:!0}:_i),[n,o]);return r})(),n=Mo(),o=t.useCallback((()=>n.getState().getNodes().map((e=>({...e})))),[]),r=t.useCallback((e=>n.getState().nodeInternals.get(e)),[]),i=t.useCallback((()=>{const{edges:e=[]}=n.getState();return e.map((e=>({...e})))}),[]),a=t.useCallback((e=>{const{edges:t=[]}=n.getState();return t.find((t=>t.id===e))}),[]),s=t.useCallback((e=>{const{getNodes:t,setNodes:o,hasDefaultNodes:r,onNodesChange:i}=n.getState(),a=t(),s="function"==typeof e?e(a):e;if(r)o(s);else if(i){i(0===s.length?a.map((e=>({type:"remove",id:e.id}))):s.map((e=>({item:e,type:"reset"}))))}}),[]),l=t.useCallback((e=>{const{edges:t=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:i}=n.getState(),a="function"==typeof e?e(t):e;if(r)o(a);else if(i){i(0===a.length?t.map((e=>({type:"remove",id:e.id}))):a.map((e=>({item:e,type:"reset"}))))}}),[]),c=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{getNodes:o,setNodes:r,hasDefaultNodes:i,onNodesChange:a}=n.getState();if(i){r([...o(),...t])}else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),u=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{edges:o=[],setEdges:r,hasDefaultEdges:i,onEdgesChange:a}=n.getState();if(i)r([...o,...t]);else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),d=t.useCallback((()=>{const{getNodes:e,edges:t=[],transform:o}=n.getState(),[r,i,a]=o;return{nodes:e().map((e=>({...e}))),edges:t.map((e=>({...e}))),viewport:{x:r,y:i,zoom:a}}}),[]),h=t.useCallback((({nodes:e,edges:t})=>{const{nodeInternals:o,getNodes:r,edges:i,hasDefaultNodes:a,hasDefaultEdges:s,onNodesDelete:l,onEdgesDelete:c,onNodesChange:u,onEdgesChange:d}=n.getState(),h=(e||[]).map((e=>e.id)),f=(t||[]).map((e=>e.id)),g=r().reduce(((e,t)=>{const n=t.parentNode||t.parentId,o=!h.includes(t.id)&&n&&e.find((e=>e.id===n));return("boolean"!=typeof t.deletable||t.deletable)&&(h.includes(t.id)||o)&&e.push(t),e}),[]),p=i.filter((e=>"boolean"!=typeof e.deletable||e.deletable)),m=p.filter((e=>f.includes(e.id)));if(g||m){const e=kr(g,p),t=[...m,...e],r=t.reduce(((e,t)=>(e.includes(t.id)||e.push(t.id),e)),[]);if((s||a)&&(s&&n.setState({edges:i.filter((e=>!r.includes(e.id)))}),a&&(g.forEach((e=>{o.delete(e.id)})),n.setState({nodeInternals:new Map(o)}))),r.length>0&&(c?.(t),d&&d(r.map((e=>({id:e,type:"remove"}))))),g.length>0&&(l?.(g),u)){u(g.map((e=>({id:e.id,type:"remove"}))))}}}),[]),f=t.useCallback((e=>{const t=Xo((o=e).width)&&Xo(o.height)&&Xo(o.x)&&Xo(o.y);var o;const r=t?null:n.getState().nodeInternals.get(e.id);if(!t&&!r)return[null,null,t];return[t?e:Lo(r),r,t]}),[]),g=t.useCallback(((e,t=!0,o)=>{const[r,i,a]=f(e);return r?(o||n.getState().getNodes()).filter((e=>{if(!(a||e.id!==i.id&&e.positionAbsolute))return!1;const n=Lo(e),o=Ho(n,r);return t&&o>0||o>=r.width*r.height})):[]}),[]),p=t.useCallback(((e,t,n=!0)=>{const[o]=f(e);if(!o)return!1;const r=Ho(o,t);return n&&r>0||r>=o.width*o.height}),[]);return t.useMemo((()=>({...e,getNodes:o,getNode:r,getEdges:i,getEdge:a,setNodes:s,setEdges:l,addNodes:c,addEdges:u,toObject:d,deleteElements:h,getIntersectingNodes:g,isNodeIntersecting:p})),[e,o,r,i,a,s,l,c,u,d,h,g,p])}const Mi={actInsideInputWithModifier:!1};const ki={position:"absolute",width:"100%",height:"100%",top:0,left:0},Ai=e=>({x:e.x,y:e.y,zoom:e.k}),Pi=(e,t)=>e.target.closest(`.${t}`),Ii=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),Oi=e=>{const t=e.ctrlKey&&Wo()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},Ri=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),zi=({onMove:e,onMoveStart:n,onMoveEnd:o,onPaneContextMenu:i,zoomOnScroll:a=!0,zoomOnPinch:s=!0,panOnScroll:l=!1,panOnScrollSpeed:c=.5,panOnScrollMode:u=Jo.Free,zoomOnDoubleClick:d=!0,elementsSelectable:h,panOnDrag:f=!0,defaultViewport:g,translateExtent:p,minZoom:m,maxZoom:y,zoomActivationKeyCode:v,preventScrolling:b=!0,children:x,noWheelClassName:w,noPanClassName:S})=>{const E=t.useRef(),_=Mo(),C=t.useRef(!1),N=t.useRef(!1),M=t.useRef(null),k=t.useRef({x:0,y:0,zoom:0}),{d3Zoom:A,d3Selection:P,d3ZoomHandler:I,userSelectionActive:O}=No(Ri,Je),R=fi(v),z=t.useRef(0),D=t.useRef(!1),$=t.useRef();return function(e){const n=Mo();t.useEffect((()=>{let t;const o=()=>{if(!e.current)return;const t=Oo(e.current);0!==t.height&&0!==t.width||n.getState().onError?.("004",vo()),n.setState({width:t.width||500,height:t.height||500})};return o(),window.addEventListener("resize",o),e.current&&(t=new ResizeObserver((()=>o())),t.observe(e.current)),()=>{window.removeEventListener("resize",o),t&&e.current&&t.unobserve(e.current)}}),[])}(M),t.useEffect((()=>{if(M.current){const e=M.current.getBoundingClientRect(),t=function(){var e,t,n,o=lo,i=co,a=go,s=ho,l=fo,c=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],d=250,h=Jt,f=r("start","zoom","end"),g=500,p=0,m=10;function y(e){e.property("__zoom",uo).on("wheel.zoom",_,{passive:!1}).on("mousedown.zoom",C).on("dblclick.zoom",N).filter(l).on("touchstart.zoom",M).on("touchmove.zoom",k).on("touchend.zoom touchcancel.zoom",A).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function v(e,t){return(t=Math.max(c[0],Math.min(c[1],t)))===e.k?e:new ro(t,e.x,e.y)}function b(e,t,n){var o=t[0]-n[0]*e.k,r=t[1]-n[1]*e.k;return o===e.x&&r===e.y?e:new ro(e.k,o,r)}function x(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function w(e,t,n,o){e.on("start.zoom",(function(){S(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){S(this,arguments).event(o).end()})).tween("zoom",(function(){var e=this,r=arguments,a=S(e,r).event(o),s=i.apply(e,r),l=null==n?x(s):"function"==typeof n?n.apply(e,r):n,c=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),u=e.__zoom,d="function"==typeof t?t.apply(e,r):t,f=h(u.invert(l).concat(c/u.k),d.invert(l).concat(c/d.k));return function(e){if(1===e)e=d;else{var t=f(e),n=c/t[2];e=new ro(n,l[0]-t[0]*n,l[1]-t[1]*n)}a.zoom(null,e)}}))}function S(e,t,n){return!n&&e.__zooming||new E(e,t)}function E(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=i.apply(e,t),this.taps=0}function _(e,...t){if(o.apply(this,arguments)){var n=S(this,t).event(e),r=this.__zoom,i=Math.max(c[0],Math.min(c[1],r.k*Math.pow(2,s.apply(this,arguments)))),l=_e(e);if(n.wheel)n.mouse[0][0]===l[0]&&n.mouse[0][1]===l[1]||(n.mouse[1]=r.invert(n.mouse[0]=l)),clearTimeout(n.wheel);else{if(r.k===i)return;n.mouse=[l,r.invert(l)],En(this),n.start()}so(e),n.wheel=setTimeout(d,150),n.zoom("mouse",a(b(v(r,i),n.mouse[0],n.mouse[1]),n.extent,u))}function d(){n.wheel=null,n.end()}}function C(e,...t){if(!n&&o.apply(this,arguments)){var r=e.currentTarget,i=S(this,t,!0).event(e),s=Ee(e.view).on("mousemove.zoom",h,!0).on("mouseup.zoom",f,!0),l=_e(e,r),c=e.clientX,d=e.clientY;Ae(e.view),ao(e),i.mouse=[l,this.__zoom.invert(l)],En(this),i.start()}function h(e){if(so(e),!i.moved){var t=e.clientX-c,n=e.clientY-d;i.moved=t*t+n*n>p}i.event(e).zoom("mouse",a(b(i.that.__zoom,i.mouse[0]=_e(e,r),i.mouse[1]),i.extent,u))}function f(e){s.on("mousemove.zoom mouseup.zoom",null),Pe(e.view,i.moved),so(e),i.event(e).end()}}function N(e,...t){if(o.apply(this,arguments)){var n=this.__zoom,r=_e(e.changedTouches?e.changedTouches[0]:e,this),s=n.invert(r),l=n.k*(e.shiftKey?.5:2),c=a(b(v(n,l),r,s),i.apply(this,t),u);so(e),d>0?Ee(this).transition().duration(d).call(w,c,r,e):Ee(this).call(y.transform,c,r,e)}}function M(n,...r){if(o.apply(this,arguments)){var i,a,s,l,c=n.touches,u=c.length,d=S(this,r,n.changedTouches.length===u).event(n);for(ao(n),a=0;a<u;++a)l=[l=_e(s=c[a],this),this.__zoom.invert(l),s.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=l[0],e=setTimeout((function(){e=null}),g)),En(this),d.start())}}function k(e,...t){if(this.__zooming){var n,o,r,i,s=S(this,t).event(e),l=e.changedTouches,c=l.length;for(so(e),n=0;n<c;++n)r=_e(o=l[n],this),s.touch0&&s.touch0[2]===o.identifier?s.touch0[0]=r:s.touch1&&s.touch1[2]===o.identifier&&(s.touch1[0]=r);if(o=s.that.__zoom,s.touch1){var d=s.touch0[0],h=s.touch0[1],f=s.touch1[0],g=s.touch1[1],p=(p=f[0]-d[0])*p+(p=f[1]-d[1])*p,m=(m=g[0]-h[0])*m+(m=g[1]-h[1])*m;o=v(o,Math.sqrt(p/m)),r=[(d[0]+f[0])/2,(d[1]+f[1])/2],i=[(h[0]+g[0])/2,(h[1]+g[1])/2]}else{if(!s.touch0)return;r=s.touch0[0],i=s.touch0[1]}s.zoom("touch",a(b(o,r,i),s.extent,u))}}function A(e,...o){if(this.__zooming){var r,i,a=S(this,o).event(e),s=e.changedTouches,l=s.length;for(ao(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),g),r=0;r<l;++r)i=s[r],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=_e(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<m)){var c=Ee(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return y.transform=function(e,t,n,o){var r=e.selection?e.selection():e;r.property("__zoom",uo),e!==r?w(e,t,n,o):r.interrupt().each((function(){S(this,arguments).event(o).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},y.scaleBy=function(e,t,n,o){y.scaleTo(e,(function(){return this.__zoom.k*("function"==typeof t?t.apply(this,arguments):t)}),n,o)},y.scaleTo=function(e,t,n,o){y.transform(e,(function(){var e=i.apply(this,arguments),o=this.__zoom,r=null==n?x(e):"function"==typeof n?n.apply(this,arguments):n,s=o.invert(r),l="function"==typeof t?t.apply(this,arguments):t;return a(b(v(o,l),r,s),e,u)}),n,o)},y.translateBy=function(e,t,n,o){y.transform(e,(function(){return a(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),i.apply(this,arguments),u)}),null,o)},y.translateTo=function(e,t,n,o,r){y.transform(e,(function(){var e=i.apply(this,arguments),r=this.__zoom,s=null==o?x(e):"function"==typeof o?o.apply(this,arguments):o;return a(io.translate(s[0],s[1]).scale(r.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,u)}),o,r)},E.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=Ee(this.that).datum();f.call(e,this.that,new oo(e,{sourceEvent:this.sourceEvent,target:y,type:e,transform:this.that.__zoom,dispatch:f}),t)}},y.wheelDelta=function(e){return arguments.length?(s="function"==typeof e?e:no(+e),y):s},y.filter=function(e){return arguments.length?(o="function"==typeof e?e:no(!!e),y):o},y.touchable=function(e){return arguments.length?(l="function"==typeof e?e:no(!!e),y):l},y.extent=function(e){return arguments.length?(i="function"==typeof e?e:no([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),y):i},y.scaleExtent=function(e){return arguments.length?(c[0]=+e[0],c[1]=+e[1],y):[c[0],c[1]]},y.translateExtent=function(e){return arguments.length?(u[0][0]=+e[0][0],u[1][0]=+e[1][0],u[0][1]=+e[0][1],u[1][1]=+e[1][1],y):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},y.constrain=function(e){return arguments.length?(a=e,y):a},y.duration=function(e){return arguments.length?(d=+e,y):d},y.interpolate=function(e){return arguments.length?(h=e,y):h},y.on=function(){var e=f.on.apply(f,arguments);return e===f?y:e},y.clickDistance=function(e){return arguments.length?(p=(e=+e)*e,y):Math.sqrt(p)},y.tapDistance=function(e){return arguments.length?(m=+e,y):m},y}().scaleExtent([m,y]).translateExtent(p),n=Ee(M.current).call(t),o=io.translate(g.x,g.y).scale(Ro(g.zoom,m,y)),i=[[0,0],[e.width,e.height]],a=t.constrain()(o,i,p);t.transform(n,a),t.wheelDelta(Oi),_.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[a.x,a.y,a.k],domNode:M.current.closest(".react-flow")})}}),[]),t.useEffect((()=>{P&&A&&(!l||R||O?void 0!==I&&P.on("wheel.zoom",(function(e,t){if(!b&&"wheel"===e.type&&!e.ctrlKey||Pi(e,w))return null;e.preventDefault(),I.call(this,e,t)}),{passive:!1}):P.on("wheel.zoom",(t=>{if(Pi(t,w))return!1;t.preventDefault(),t.stopImmediatePropagation();const r=P.property("__zoom").k||1;if(t.ctrlKey&&s){const e=_e(t),n=Oi(t),o=r*Math.pow(2,n);return void A.scaleTo(P,o,e,t)}const i=1===t.deltaMode?20:1;let a=u===Jo.Vertical?0:t.deltaX*i,l=u===Jo.Horizontal?0:t.deltaY*i;!Wo()&&t.shiftKey&&u!==Jo.Vertical&&(a=t.deltaY*i,l=0),A.translateBy(P,-a/r*c,-l/r*c,{internal:!0});const d=Ai(P.property("__zoom")),{onViewportChangeStart:h,onViewportChange:f,onViewportChangeEnd:g}=_.getState();clearTimeout($.current),D.current||(D.current=!0,n?.(t,d),h?.(d)),D.current&&(e?.(t,d),f?.(d),$.current=setTimeout((()=>{o?.(t,d),g?.(d),D.current=!1}),150))}),{passive:!1}))}),[O,l,u,P,A,I,R,s,b,w,n,e,o]),t.useEffect((()=>{A&&A.on("start",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;z.current=e.sourceEvent?.button;const{onViewportChangeStart:t}=_.getState(),o=Ai(e.transform);C.current=!0,k.current=o,"mousedown"===e.sourceEvent?.type&&_.setState({paneDragging:!0}),t?.(o),n?.(e.sourceEvent,o)}))}),[A,n]),t.useEffect((()=>{A&&(O&&!C.current?A.on("zoom",null):O||A.on("zoom",(t=>{const{onViewportChange:n}=_.getState();if(_.setState({transform:[t.transform.x,t.transform.y,t.transform.k]}),N.current=!(!i||!Ii(f,z.current??0)),(e||n)&&!t.sourceEvent?.internal){const o=Ai(t.transform);n?.(o),e?.(t.sourceEvent,o)}})))}),[O,A,e,f,i]),t.useEffect((()=>{A&&A.on("end",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;const{onViewportChangeEnd:t}=_.getState();if(C.current=!1,_.setState({paneDragging:!1}),i&&Ii(f,z.current??0)&&!N.current&&i(e.sourceEvent),N.current=!1,(o||t)&&(n=k.current,r=e.transform,n.x!==r.x||n.y!==r.y||n.zoom!==r.k)){const n=Ai(e.transform);k.current=n,clearTimeout(E.current),E.current=setTimeout((()=>{t?.(n),o?.(e.sourceEvent,n)}),l?150:0)}var n,r}))}),[A,l,f,o,i]),t.useEffect((()=>{A&&A.filter((e=>{const t=R||a,n=s&&e.ctrlKey;if((!0===f||Array.isArray(f)&&f.includes(1))&&1===e.button&&"mousedown"===e.type&&(Pi(e,"react-flow__node")||Pi(e,"react-flow__edge")))return!0;if(!(f||t||l||d||s))return!1;if(O)return!1;if(!d&&"dblclick"===e.type)return!1;if(Pi(e,w)&&"wheel"===e.type)return!1;if(Pi(e,S)&&("wheel"!==e.type||l&&"wheel"===e.type&&!R))return!1;if(!s&&e.ctrlKey&&"wheel"===e.type)return!1;if(!t&&!l&&!n&&"wheel"===e.type)return!1;if(!f&&("mousedown"===e.type||"touchstart"===e.type))return!1;if(Array.isArray(f)&&!f.includes(e.button)&&"mousedown"===e.type)return!1;const o=Array.isArray(f)&&f.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&o}))}),[O,A,a,s,l,d,f,h,R]),t.createElement("div",{className:"react-flow__renderer",ref:M,style:ki},x)},Di=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function $i(){const{userSelectionActive:e,userSelectionRect:n}=No(Di,Je);return e&&n?t.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:n.width,height:n.height,transform:`translate(${n.x}px, ${n.y}px)`}}):null}function Ti(e,t){const n=t.parentNode||t.parentId,o=e.find((e=>e.id===n));if(o){const e=t.position.x+t.width-o.width,n=t.position.y+t.height-o.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(o.style={...o.style}||{},o.style.width=o.style.width??o.width,o.style.height=o.style.height??o.height,e>0&&(o.style.width+=e),n>0&&(o.style.height+=n),t.position.x<0){const e=Math.abs(t.position.x);o.position.x=o.position.x-e,o.style.width+=e,t.position.x=0}if(t.position.y<0){const e=Math.abs(t.position.y);o.position.y=o.position.y-e,o.style.height+=e,t.position.y=0}o.width=o.style.width,o.height=o.style.height}}}function Bi(e,t){return function(e,t){if(e.some((e=>"reset"===e.type)))return e.filter((e=>"reset"===e.type)).map((e=>e.item));const n=e.filter((e=>"add"===e.type)).map((e=>e.item));return t.reduce(((t,n)=>{const o=e.filter((e=>e.id===n.id));if(0===o.length)return t.push(n),t;const r={...n};for(const e of o)if(e)switch(e.type){case"select":r.selected=e.selected;break;case"position":void 0!==e.position&&(r.position=e.position),void 0!==e.positionAbsolute&&(r.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(r.dragging=e.dragging),r.expandParent&&Ti(t,r);break;case"dimensions":void 0!==e.dimensions&&(r.width=e.dimensions.width,r.height=e.dimensions.height),void 0!==e.updateStyle&&(r.style={...r.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(r.resizing=e.resizing),r.expandParent&&Ti(t,r);break;case"remove":return t}return t.push(r),t}),n)}(e,t)}const Li=(e,t)=>({id:e,type:"select",selected:t});function Hi(e,t){return e.reduce(((e,n)=>{const o=t.includes(n.id);return!n.selected&&o?(n.selected=!0,e.push(Li(n.id,!0))):n.selected&&!o&&(n.selected=!1,e.push(Li(n.id,!1))),e}),[])}const Xi=(e,t)=>n=>{n.target===t.current&&e?.(n)},Yi=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),Vi=t.memo((({isSelecting:e,selectionMode:o=er.Full,panOnDrag:r,onSelectionStart:i,onSelectionEnd:a,onPaneClick:s,onPaneContextMenu:l,onPaneScroll:c,onPaneMouseEnter:u,onPaneMouseMove:d,onPaneMouseLeave:h,children:f})=>{const g=t.useRef(null),p=Mo(),m=t.useRef(0),y=t.useRef(0),v=t.useRef(),{userSelectionActive:b,elementsSelectable:x,dragging:w}=No(Yi,Je),S=()=>{p.setState({userSelectionActive:!1,userSelectionRect:null}),m.current=0,y.current=0},E=e=>{s?.(e),p.getState().resetSelectedElements(),p.setState({nodesSelectionActive:!1})},_=c?e=>c(e):void 0,C=x&&(e||b);return t.createElement("div",{className:n(["react-flow__pane",{dragging:w,selection:e}]),onClick:C?void 0:Xi(E,g),onContextMenu:Xi((e=>{Array.isArray(r)&&r?.includes(2)?e.preventDefault():l?.(e)}),g),onWheel:Xi(_,g),onMouseEnter:C?void 0:u,onMouseDown:C?t=>{const{resetSelectedElements:n,domNode:o}=p.getState();if(v.current=o?.getBoundingClientRect(),!x||!e||0!==t.button||t.target!==g.current||!v.current)return;const{x:r,y:a}=Zo(t,v.current);n(),p.setState({userSelectionRect:{width:0,height:0,startX:r,startY:a,x:r,y:a}}),i?.(t)}:void 0,onMouseMove:C?t=>{const{userSelectionRect:n,nodeInternals:r,edges:i,transform:a,onNodesChange:s,onEdgesChange:l,nodeOrigin:c,getNodes:u}=p.getState();if(!e||!v.current||!n)return;p.setState({userSelectionActive:!0,nodesSelectionActive:!1});const d=Zo(t,v.current),h=n.startX??0,f=n.startY??0,g={...n,x:d.x<h?d.x:h,y:d.y<f?d.y:f,width:Math.abs(d.x-h),height:Math.abs(d.y-f)},b=u(),x=Mr(r,g,a,o===er.Partial,!0,c),w=kr(x,i).map((e=>e.id)),S=x.map((e=>e.id));if(m.current!==S.length){m.current=S.length;const e=Hi(b,S);e.length&&s?.(e)}if(y.current!==w.length){y.current=w.length;const e=Hi(i,w);e.length&&l?.(e)}p.setState({userSelectionRect:g})}:d,onMouseUp:C?e=>{if(0!==e.button)return;const{userSelectionRect:t}=p.getState();!b&&t&&e.target===g.current&&E?.(e),p.setState({nodesSelectionActive:m.current>0}),S(),a?.(e)}:void 0,onMouseLeave:C?e=>{b&&(p.setState({nodesSelectionActive:m.current>0}),a?.(e)),S()}:h,ref:g,style:ki},f,t.createElement($i,null))}));function Ki(e,t){const n=e.parentNode||e.parentId;if(!n)return!1;const o=t.get(n);return!!o&&(!!o.selected||Ki(o,t))}function Fi(e,t,n){let o=e;do{if(o?.matches(t))return!0;if(o===n.current)return!1;o=o.parentElement}while(o);return!1}function Zi(e,t,n,o){return Array.from(e.values()).filter((n=>(n.selected||n.id===o)&&(!n.parentNode||n.parentId||!Ki(n,e))&&(n.draggable||t&&void 0===n.draggable))).map((e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:n.x-(e.positionAbsolute?.x??0),y:n.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})))}function Wi(e,t,n,o,r=[0,0],i){const a=function(e,t){return t&&"parent"!==t?[t[0],[t[1][0]-(e.width||0),t[1][1]-(e.height||0)]]:t}(e,e.extent||o);let s=a;const l=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&l&&"parent"!==e.extent){const t=n.get(l),{x:o,y:i}=Cr(t,r).positionAbsolute;s=[[e.extent[0][0]+o,e.extent[0][1]+i],[e.extent[1][0]+o,e.extent[1][1]+i]]}}else if(l&&e.width&&e.height){const t=n.get(l),{x:o,y:i}=Cr(t,r).positionAbsolute;s=t&&Xo(o)&&Xo(i)&&Xo(t.width)&&Xo(t.height)?[[o+e.width*r[0],i+e.height*r[1]],[o+t.width-e.width+e.width*r[0],i+t.height-e.height+e.height*r[1]]]:s}else i?.("005",bo()),s=a;let c={x:0,y:0};if(l){const e=n.get(l);c=Cr(e,r).positionAbsolute}const u=s&&"parent"!==s?zo(t,s):t;return{position:{x:u.x-c.x,y:u.y-c.y},positionAbsolute:u}}function ji({nodeId:e,dragItems:t,nodeInternals:n}){const o=t.map((e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute})));return[e?o.find((t=>t.id===e)):o[0],o]}Vi.displayName="Pane";const qi=(e,t,n,o)=>{const r=t.querySelectorAll(e);if(!r||!r.length)return null;const i=Array.from(r),a=t.getBoundingClientRect(),s=a.width*o[0],l=a.height*o[1];return i.map((e=>{const t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-a.left-s)/n,y:(t.top-a.top-l)/n,...Oo(e)}}))};function Ui(e,t,n){return void 0===n?n:o=>{const r=t().nodeInternals.get(e);r&&n(o,{...r})}}function Gi({id:e,store:t,unselect:n=!1,nodeRef:o}){const{addSelectedNodes:r,unselectNodesAndEdges:i,multiSelectionActive:a,nodeInternals:s,onError:l}=t.getState(),c=s.get(e);c?(t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame((()=>o?.current?.blur()))):r([e])):l?.("012",_o(e))}function Qi(){const e=Mo(),n=t.useCallback((({sourceEvent:t})=>{const{transform:n,snapGrid:o,snapToGrid:r}=e.getState(),i=t.touches?t.touches[0].clientX:t.clientX,a=t.touches?t.touches[0].clientY:t.clientY,s={x:(i-n[0])/n[2],y:(a-n[1])/n[2]};return{xSnapped:r?o[0]*Math.round(s.x/o[0]):s.x,ySnapped:r?o[1]*Math.round(s.y/o[1]):s.y,...s}}),[]);return n}function Ji(e){return(t,n,o)=>e?.(t,o)}function ea({nodeRef:e,disabled:n=!1,noDragClassName:o,handleSelector:r,nodeId:i,isSelectable:a,selectNodesOnDrag:s}){const l=Mo(),[c,u]=t.useState(!1),d=t.useRef([]),h=t.useRef({x:null,y:null}),f=t.useRef(0),g=t.useRef(null),p=t.useRef({x:0,y:0}),m=t.useRef(null),y=t.useRef(!1),v=t.useRef(!1),b=t.useRef(!1),x=Qi();return t.useEffect((()=>{if(e?.current){const t=Ee(e.current),c=({x:e,y:t})=>{const{nodeInternals:n,onNodeDrag:o,onSelectionDrag:r,updateNodePositions:a,nodeExtent:s,snapGrid:c,snapToGrid:f,nodeOrigin:g,onError:p}=l.getState();h.current={x:e,y:t};let y=!1,v={x:0,y:0,x2:0,y2:0};if(d.current.length>1&&s){const e=Nr(d.current,g);v=Bo(e)}if(d.current=d.current.map((o=>{const r={x:e-o.distance.x,y:t-o.distance.y};f&&(r.x=c[0]*Math.round(r.x/c[0]),r.y=c[1]*Math.round(r.y/c[1]));const i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];d.current.length>1&&s&&!o.extent&&(i[0][0]=o.positionAbsolute.x-v.x+s[0][0],i[1][0]=o.positionAbsolute.x+(o.width??0)-v.x2+s[1][0],i[0][1]=o.positionAbsolute.y-v.y+s[0][1],i[1][1]=o.positionAbsolute.y+(o.height??0)-v.y2+s[1][1]);const a=Wi(o,r,n,i,g,p);return y=y||o.position.x!==a.position.x||o.position.y!==a.position.y,o.position=a.position,o.positionAbsolute=a.positionAbsolute,o})),!y)return;a(d.current,!0,!0),u(!0);const b=i?o:Ji(r);if(b&&m.current){const[e,t]=ji({nodeId:i,dragItems:d.current,nodeInternals:n});b(m.current,e,t)}},w=()=>{if(!g.current)return;const[e,t]=$o(p.current,g.current);if(0!==e||0!==t){const{transform:n,panBy:o}=l.getState();h.current.x=(h.current.x??0)-e/n[2],h.current.y=(h.current.y??0)-t/n[2],o({x:e,y:t})&&c(h.current)}f.current=requestAnimationFrame(w)},S=t=>{const{nodeInternals:n,multiSelectionActive:o,nodesDraggable:r,unselectNodesAndEdges:c,onNodeDragStart:u,onSelectionDragStart:f}=l.getState();v.current=!0;const g=i?u:Ji(f);s&&a||o||!i||n.get(i)?.selected||c(),i&&a&&s&&Gi({id:i,store:l,nodeRef:e});const p=x(t);if(h.current=p,d.current=Zi(n,r,p,i),g&&d.current){const[e,o]=ji({nodeId:i,dragItems:d.current,nodeInternals:n});g(t.sourceEvent,e,o)}};if(!n){const n=Te().on("start",(e=>{const{domNode:t,nodeDragThreshold:n}=l.getState();0===n&&S(e),b.current=!1;const o=x(e);h.current=o,g.current=t?.getBoundingClientRect()||null,p.current=Zo(e.sourceEvent,g.current)})).on("drag",(e=>{const t=x(e),{autoPanOnNodeDrag:n,nodeDragThreshold:o}=l.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(b.current=!0),!b.current){if(!y.current&&v.current&&n&&(y.current=!0,w()),!v.current){const n=t.xSnapped-(h?.current?.x??0),r=t.ySnapped-(h?.current?.y??0);Math.sqrt(n*n+r*r)>o&&S(e)}(h.current.x!==t.xSnapped||h.current.y!==t.ySnapped)&&d.current&&v.current&&(m.current=e.sourceEvent,p.current=Zo(e.sourceEvent,g.current),c(t))}})).on("end",(e=>{if(v.current&&!b.current&&(u(!1),y.current=!1,v.current=!1,cancelAnimationFrame(f.current),d.current)){const{updateNodePositions:t,nodeInternals:n,onNodeDragStop:o,onSelectionDragStop:r}=l.getState(),a=i?o:Ji(r);if(t(d.current,!1,!1),a){const[t,o]=ji({nodeId:i,dragItems:d.current,nodeInternals:n});a(e.sourceEvent,t,o)}}})).filter((t=>{const n=t.target;return!t.button&&(!o||!Fi(n,`.${o}`,e))&&(!r||Fi(n,r,e))}));return t.call(n),()=>{t.on(".drag",null)}}t.on(".drag",null)}}),[e,n,o,r,a,l,i,s,x]),c}function ta(){const e=Mo();return t.useCallback((t=>{const{nodeInternals:n,nodeExtent:o,updateNodePositions:r,getNodes:i,snapToGrid:a,snapGrid:s,onError:l,nodesDraggable:c}=e.getState(),u=i().filter((e=>e.selected&&(e.draggable||c&&void 0===e.draggable))),d=a?s[0]:5,h=a?s[1]:5,f=t.isShiftPressed?4:1,g=t.x*d*f,p=t.y*h*f;r(u.map((e=>{if(e.positionAbsolute){const t={x:e.positionAbsolute.x+g,y:e.positionAbsolute.y+p};a&&(t.x=s[0]*Math.round(t.x/s[0]),t.y=s[1]*Math.round(t.y/s[1]));const{positionAbsolute:r,position:i}=Wi(e,t,n,o,void 0,l);e.position=i,e.positionAbsolute=r}return e})),!0,!1)}),[])}const na={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var oa=e=>{const o=({id:o,type:r,data:i,xPos:a,yPos:s,xPosOrigin:l,yPosOrigin:c,selected:u,onClick:d,onMouseEnter:h,onMouseMove:f,onMouseLeave:g,onContextMenu:p,onDoubleClick:m,style:y,className:v,isDraggable:b,isSelectable:x,isConnectable:w,isFocusable:S,selectNodesOnDrag:E,sourcePosition:_,targetPosition:C,hidden:N,resizeObserver:M,dragHandle:k,zIndex:A,isParent:P,noDragClassName:I,noPanClassName:O,initialized:R,disableKeyboardA11y:z,ariaLabel:D,rfId:$,hasHandleBounds:T})=>{const B=Mo(),L=t.useRef(null),H=t.useRef(null),X=t.useRef(_),Y=t.useRef(C),V=t.useRef(r),K=x||b||d||h||f||g,F=ta(),Z=Ui(o,B.getState,h),W=Ui(o,B.getState,f),j=Ui(o,B.getState,g),q=Ui(o,B.getState,p),U=Ui(o,B.getState,m);t.useEffect((()=>()=>{H.current&&(M?.unobserve(H.current),H.current=null)}),[]),t.useEffect((()=>{if(L.current&&!N){const e=L.current;R&&T&&H.current===e||(H.current&&M?.unobserve(H.current),M?.observe(e),H.current=e)}}),[N,R,T]),t.useEffect((()=>{const e=V.current!==r,t=X.current!==_,n=Y.current!==C;L.current&&(e||t||n)&&(e&&(V.current=r),t&&(X.current=_),n&&(Y.current=C),B.getState().updateNodeDimensions([{id:o,nodeElement:L.current,forceUpdate:!0}]))}),[o,r,_,C]);const G=ea({nodeRef:L,disabled:N||!b,noDragClassName:I,handleSelector:k,nodeId:o,isSelectable:x,selectNodesOnDrag:E});return N?null:t.createElement("div",{className:n(["react-flow__node",`react-flow__node-${r}`,{[O]:b},v,{selected:u,selectable:x,parent:P,dragging:G}]),ref:L,style:{zIndex:A,transform:`translate(${l}px,${c}px)`,pointerEvents:K?"all":"none",visibility:R?"visible":"hidden",...y},"data-id":o,"data-testid":`rf__node-${o}`,onMouseEnter:Z,onMouseMove:W,onMouseLeave:j,onContextMenu:q,onClick:e=>{const{nodeDragThreshold:t}=B.getState();if(x&&(!E||!b||t>0)&&Gi({id:o,store:B,nodeRef:L}),d){const t=B.getState().nodeInternals.get(o);t&&d(e,{...t})}},onDoubleClick:U,onKeyDown:S?e=>{if(!Ko(e)&&!z)if(Vo.includes(e.key)&&x){const t="Escape"===e.key;Gi({id:o,store:B,unselect:t,nodeRef:L})}else b&&u&&Object.prototype.hasOwnProperty.call(na,e.key)&&(B.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~a}, y: ${~~s}`}),F({x:na[e.key].x,y:na[e.key].y,isShiftPressed:e.shiftKey}))}:void 0,tabIndex:S?0:void 0,role:S?"button":void 0,"aria-describedby":z?void 0:`${li}-${$}`,"aria-label":D},t.createElement(br,{value:o},t.createElement(e,{id:o,data:i,type:r,xPos:a,yPos:s,selected:u,isConnectable:w,sourcePosition:_,targetPosition:C,dragging:G,dragHandle:k,zIndex:A})))};return o.displayName="NodeWrapper",t.memo(o)};const ra=e=>{const t=e.getNodes().filter((e=>e.selected));return{...Nr(t,e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive}};var ia=t.memo((function({onSelectionContextMenu:e,noPanClassName:o,disableKeyboardA11y:r}){const i=Mo(),{width:a,height:s,x:l,y:c,transformString:u,userSelectionActive:d}=No(ra,Je),h=ta(),f=t.useRef(null);if(t.useEffect((()=>{r||f.current?.focus({preventScroll:!0})}),[r]),ea({nodeRef:f}),d||!a||!s)return null;const g=e?t=>{const n=i.getState().getNodes().filter((e=>e.selected));e(t,n)}:void 0;return t.createElement("div",{className:n(["react-flow__nodesselection","react-flow__container",o]),style:{transform:u}},t.createElement("div",{ref:f,className:"react-flow__nodesselection-rect",onContextMenu:g,tabIndex:r?void 0:-1,onKeyDown:r?void 0:e=>{Object.prototype.hasOwnProperty.call(na,e.key)&&h({x:na[e.key].x,y:na[e.key].y,isShiftPressed:e.shiftKey})},style:{width:a,height:s,top:c,left:l}}))}));const aa=e=>e.nodesSelectionActive,sa=({children:e,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,deleteKeyCode:l,onMove:c,onMoveStart:u,onMoveEnd:d,selectionKeyCode:h,selectionOnDrag:f,selectionMode:g,onSelectionStart:p,onSelectionEnd:m,multiSelectionKeyCode:y,panActivationKeyCode:v,zoomActivationKeyCode:b,elementsSelectable:x,zoomOnScroll:w,zoomOnPinch:S,panOnScroll:E,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:N,panOnDrag:M,defaultViewport:k,translateExtent:A,minZoom:P,maxZoom:I,preventScrolling:O,onSelectionContextMenu:R,noWheelClassName:z,noPanClassName:D,disableKeyboardA11y:$})=>{const T=No(aa),B=fi(h),L=fi(v),H=L||M,X=L||E,Y=B||f&&!0!==H;return(({deleteKeyCode:e,multiSelectionKeyCode:n})=>{const o=Mo(),{deleteElements:r}=Ni(),i=fi(e,Mi),a=fi(n);t.useEffect((()=>{if(i){const{edges:e,getNodes:t}=o.getState(),n=t().filter((e=>e.selected)),i=e.filter((e=>e.selected));r({nodes:n,edges:i}),o.setState({nodesSelectionActive:!1})}}),[i]),t.useEffect((()=>{o.setState({multiSelectionActive:a})}),[a])})({deleteKeyCode:l,multiSelectionKeyCode:y}),t.createElement(zi,{onMove:c,onMoveStart:u,onMoveEnd:d,onPaneContextMenu:a,elementsSelectable:x,zoomOnScroll:w,zoomOnPinch:S,panOnScroll:X,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:N,panOnDrag:!B&&H,defaultViewport:k,translateExtent:A,minZoom:P,maxZoom:I,zoomActivationKeyCode:b,preventScrolling:O,noWheelClassName:z,noPanClassName:D},t.createElement(Vi,{onSelectionStart:p,onSelectionEnd:m,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,panOnDrag:H,isSelecting:!!Y,selectionMode:g},e,T&&t.createElement(ia,{onSelectionContextMenu:R,noPanClassName:D,disableKeyboardA11y:$})))};sa.displayName="FlowRenderer";var la=t.memo(sa);function ca(e){return{...{input:oa(e.input||Zr),default:oa(e.default||Kr),output:oa(e.output||jr),group:oa(e.group||qr)},...Object.keys(e).filter((e=>!["input","default","output","group"].includes(e))).reduce(((t,n)=>(t[n]=oa(e[n]||Kr),t)),{})}}const ua=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),da=e=>{const{nodesDraggable:n,nodesConnectable:o,nodesFocusable:r,elementsSelectable:i,updateNodeDimensions:a,onError:s}=No(ua,Je),l=(c=e.onlyRenderVisibleElements,No(t.useCallback((e=>c?Mr(e.nodeInternals,{x:0,y:0,width:e.width,height:e.height},e.transform,!0):e.getNodes()),[c])));var c;const u=t.useRef(),d=t.useMemo((()=>{if("undefined"==typeof ResizeObserver)return null;const e=new ResizeObserver((e=>{const t=e.map((e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})));a(t)}));return u.current=e,e}),[]);return t.useEffect((()=>()=>{u?.current?.disconnect()}),[]),t.createElement("div",{className:"react-flow__nodes",style:ki},l.map((a=>{let l=a.type||"default";e.nodeTypes[l]||(s?.("003",yo(l)),l="default");const c=e.nodeTypes[l]||e.nodeTypes.default,u=!!(a.draggable||n&&void 0===a.draggable),h=!!(a.selectable||i&&void 0===a.selectable),f=!!(a.connectable||o&&void 0===a.connectable),g=!!(a.focusable||r&&void 0===a.focusable),p=e.nodeExtent?zo(a.positionAbsolute,e.nodeExtent):a.positionAbsolute,m=p?.x??0,y=p?.y??0,v=(({x:e,y:t,width:n,height:o,origin:r})=>n&&o?r[0]<0||r[1]<0||r[0]>1||r[1]>1?{x:e,y:t}:{x:e-n*r[0],y:t-o*r[1]}:{x:e,y:t})({x:m,y:y,width:a.width??0,height:a.height??0,origin:e.nodeOrigin});return t.createElement(c,{key:a.id,id:a.id,className:a.className,style:a.style,type:l,data:a.data,sourcePosition:a.sourcePosition||or.Bottom,targetPosition:a.targetPosition||or.Top,hidden:a.hidden,xPos:m,yPos:y,xPosOrigin:v.x,yPosOrigin:v.y,selectNodesOnDrag:e.selectNodesOnDrag,onClick:e.onNodeClick,onMouseEnter:e.onNodeMouseEnter,onMouseMove:e.onNodeMouseMove,onMouseLeave:e.onNodeMouseLeave,onContextMenu:e.onNodeContextMenu,onDoubleClick:e.onNodeDoubleClick,selected:!!a.selected,isDraggable:u,isSelectable:h,isConnectable:f,isFocusable:g,resizeObserver:d,dragHandle:a.dragHandle,zIndex:a[Yo]?.z??0,isParent:!!a[Yo]?.isParent,noDragClassName:e.noDragClassName,noPanClassName:e.noPanClassName,initialized:!!a.width&&!!a.height,rfId:e.rfId,disableKeyboardA11y:e.disableKeyboardA11y,ariaLabel:a.ariaLabel,hasHandleBounds:!!a[Yo]?.handleBounds})})))};da.displayName="NodeRenderer";var ha=t.memo(da);const fa=(e,t,n)=>n===or.Left?e-t:n===or.Right?e+t:e,ga=(e,t,n)=>n===or.Top?e-t:n===or.Bottom?e+t:e,pa="react-flow__edgeupdater",ma=({position:e,centerX:o,centerY:r,radius:i=10,onMouseDown:a,onMouseEnter:s,onMouseOut:l,type:c})=>t.createElement("circle",{onMouseDown:a,onMouseEnter:s,onMouseOut:l,className:n([pa,`${pa}-${c}`]),cx:fa(o,i,e),cy:ga(r,i,e),r:i,stroke:"transparent",fill:"transparent"}),ya=()=>!0;var va=e=>{const o=({id:o,className:r,type:i,data:a,onClick:s,onEdgeDoubleClick:l,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:y,source:v,target:b,sourceX:x,sourceY:w,targetX:S,targetY:E,sourcePosition:_,targetPosition:C,elementsSelectable:N,hidden:M,sourceHandleId:k,targetHandleId:A,onContextMenu:P,onMouseEnter:I,onMouseMove:O,onMouseLeave:R,reconnectRadius:z,onReconnect:D,onReconnectStart:$,onReconnectEnd:T,markerEnd:B,markerStart:L,rfId:H,ariaLabel:X,isFocusable:Y,isReconnectable:V,pathOptions:K,interactionWidth:F,disableKeyboardA11y:Z})=>{const W=t.useRef(null),[j,q]=t.useState(!1),[U,G]=t.useState(!1),Q=Mo(),J=t.useMemo((()=>`url('#${Sr(L,H)}')`),[L,H]),ee=t.useMemo((()=>`url('#${Sr(B,H)}')`),[B,H]);if(M)return null;const te=qo(o,Q.getState,l),ne=qo(o,Q.getState,P),oe=qo(o,Q.getState,I),re=qo(o,Q.getState,O),ie=qo(o,Q.getState,R),ae=(e,t)=>{if(0!==e.button)return;const{edges:n,isValidConnection:r}=Q.getState(),i=t?b:v,a=(t?A:k)||null,s=t?"target":"source",l=r||ya,c=t,u=n.find((e=>e.id===o));G(!0),$?.(e,u,s);Br({event:e,handleId:a,nodeId:i,onConnect:e=>D?.(u,e),isTarget:c,getState:Q.getState,setState:Q.setState,isValidConnection:l,edgeUpdaterType:s,onReconnectEnd:e=>{G(!1),T?.(e,u,s)}})},se=()=>q(!0),le=()=>q(!1),ce=!N&&!s;return t.createElement("g",{className:n(["react-flow__edge",`react-flow__edge-${i}`,r,{selected:c,animated:u,inactive:ce,updating:j}]),onClick:e=>{const{edges:t,addSelectedEdges:n,unselectNodesAndEdges:r,multiSelectionActive:i}=Q.getState(),a=t.find((e=>e.id===o));a&&(N&&(Q.setState({nodesSelectionActive:!1}),a.selected&&i?(r({nodes:[],edges:[a]}),W.current?.blur()):n([o])),s&&s(e,a))},onDoubleClick:te,onContextMenu:ne,onMouseEnter:oe,onMouseMove:re,onMouseLeave:ie,onKeyDown:Y?e=>{if(!Z&&Vo.includes(e.key)&&N){const{unselectNodesAndEdges:t,addSelectedEdges:n,edges:r}=Q.getState();"Escape"===e.key?(W.current?.blur(),t({edges:[r.find((e=>e.id===o))]})):n([o])}}:void 0,tabIndex:Y?0:void 0,role:Y?"button":"img","data-testid":`rf__edge-${o}`,"aria-label":null===X?void 0:X||`Edge from ${v} to ${b}`,"aria-describedby":Y?`${ci}-${H}`:void 0,ref:W},!U&&t.createElement(e,{id:o,source:v,target:b,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,data:a,style:y,sourceX:x,sourceY:w,targetX:S,targetY:E,sourcePosition:_,targetPosition:C,sourceHandleId:k,targetHandleId:A,markerStart:J,markerEnd:ee,pathOptions:K,interactionWidth:F}),V&&t.createElement(t.Fragment,null,("source"===V||!0===V)&&t.createElement(ma,{position:_,centerX:x,centerY:w,radius:z,onMouseDown:e=>ae(e,!0),onMouseEnter:se,onMouseOut:le,type:"source"}),("target"===V||!0===V)&&t.createElement(ma,{position:C,centerX:S,centerY:E,radius:z,onMouseDown:e=>ae(e,!1),onMouseEnter:se,onMouseOut:le,type:"target"})))};return o.displayName="EdgeWrapper",t.memo(o)};function ba(e){return{...{default:va(e.default||yr),straight:va(e.bezier||fr),step:va(e.step||hr),smoothstep:va(e.step||dr),simplebezier:va(e.simplebezier||ar)},...Object.keys(e).filter((e=>!["default","bezier"].includes(e))).reduce(((t,n)=>(t[n]=va(e[n]||yr),t)),{})}}function xa(e,t,n=null){const o=(n?.x||0)+t.x,r=(n?.y||0)+t.y,i=n?.width||t.width,a=n?.height||t.height;switch(e){case or.Top:return{x:o+i/2,y:r};case or.Right:return{x:o+i,y:r+a/2};case or.Bottom:return{x:o+i/2,y:r+a};case or.Left:return{x:o,y:r+a/2}}}function wa(e,t){return e?1!==e.length&&t?t&&e.find((e=>e.id===t))||null:e[0]:null}function Sa(e){const t=e?.[Yo]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}const Ea=[{level:0,isMaxLevel:!0,edges:[]}];function _a(e,n,o){return function(e,t,n=!1){let o=-1;const r=e.reduce(((e,r)=>{const i=Xo(r.zIndex);let a=i?r.zIndex:0;if(n){const e=t.get(r.target),n=t.get(r.source),o=r.selected||e?.selected||n?.selected,s=Math.max(n?.[Yo]?.z||0,e?.[Yo]?.z||0,1e3);a=(i?r.zIndex:0)+(o?s:0)}return e[a]?e[a].push(r):e[a]=[r],o=a>o?a:o,e}),{}),i=Object.entries(r).map((([e,t])=>{const n=+e;return{edges:t,level:n,isMaxLevel:n===o}}));return 0===i.length?Ea:i}(No(t.useCallback((t=>e?t.edges.filter((e=>{const o=n.get(e.source),r=n.get(e.target);return o?.width&&o?.height&&r?.width&&r?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:i,width:a,height:s,transform:l}){const c={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+i)};c.x===c.x2&&(c.x2+=1),c.y===c.y2&&(c.y2+=1);const u=Bo({x:(0-l[0])/l[2],y:(0-l[1])/l[2],width:a/l[2],height:s/l[2]}),d=Math.max(0,Math.min(u.x2,c.x2)-Math.max(u.x,c.x)),h=Math.max(0,Math.min(u.y2,c.y2)-Math.max(u.y,c.y));return Math.ceil(d*h)>0}({sourcePos:o.positionAbsolute||{x:0,y:0},targetPos:r.positionAbsolute||{x:0,y:0},sourceWidth:o.width,sourceHeight:o.height,targetWidth:r.width,targetHeight:r.height,width:t.width,height:t.height,transform:t.transform})})):t.edges),[e,n])),n,o)}const Ca={[nr.Arrow]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[nr.ArrowClosed]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})};const Na=({id:e,type:n,color:o,width:r=12.5,height:i=12.5,markerUnits:a="strokeWidth",strokeWidth:s,orient:l="auto-start-reverse"})=>{const c=function(e){const n=Mo();return t.useMemo((()=>Object.prototype.hasOwnProperty.call(Ca,e)?Ca[e]:(n.getState().onError?.("009",xo(e)),null)),[e])}(n);return c?t.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${i}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:l,refX:"0",refY:"0"},t.createElement(c,{color:o,strokeWidth:s})):null},Ma=({defaultColor:e,rfId:n})=>{const o=No(t.useCallback((({defaultColor:e,rfId:t})=>n=>{const o=[];return n.edges.reduce(((n,r)=>([r.markerStart,r.markerEnd].forEach((r=>{if(r&&"object"==typeof r){const i=Sr(r,t);o.includes(i)||(n.push({id:i,color:r.color||e,...r}),o.push(i))}})),n)),[]).sort(((e,t)=>e.id.localeCompare(t.id)))})({defaultColor:e,rfId:n}),[e,n]),((e,t)=>!(e.length!==t.length||e.some(((e,n)=>e.id!==t[n].id)))));return t.createElement("defs",null,o.map((e=>t.createElement(Na,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient}))))};Ma.displayName="MarkerDefinitions";var ka=t.memo(Ma);const Aa=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),Pa=({defaultMarkerColor:e,onlyRenderVisibleElements:o,elevateEdgesOnSelect:r,rfId:i,edgeTypes:a,noPanClassName:s,onEdgeContextMenu:l,onEdgeMouseEnter:c,onEdgeMouseMove:u,onEdgeMouseLeave:d,onEdgeClick:h,onEdgeDoubleClick:f,onReconnect:g,onReconnectStart:p,onReconnectEnd:m,reconnectRadius:y,children:v,disableKeyboardA11y:b})=>{const{edgesFocusable:x,edgesUpdatable:w,elementsSelectable:S,width:E,height:_,connectionMode:C,nodeInternals:N,onError:M}=No(Aa,Je),k=_a(o,N,r);return E?t.createElement(t.Fragment,null,k.map((({level:o,edges:r,isMaxLevel:v})=>t.createElement("svg",{key:o,style:{zIndex:o},width:E,height:_,className:"react-flow__edges react-flow__container"},v&&t.createElement(ka,{defaultColor:e,rfId:i}),t.createElement("g",null,r.map((e=>{const[o,r,v]=Sa(N.get(e.source)),[E,_,k]=Sa(N.get(e.target));if(!v||!k)return null;let A=e.type||"default";a[A]||(M?.("011",Eo(A)),A="default");const P=a[A]||a.default,I=C===Qo.Strict?_.target:(_.target??[]).concat(_.source??[]),O=wa(r.source,e.sourceHandle),R=wa(I,e.targetHandle),z=O?.position||or.Bottom,D=R?.position||or.Top,$=!!(e.focusable||x&&void 0===e.focusable),T=e.reconnectable||e.updatable,B=void 0!==g&&(T||w&&void 0===T);if(!O||!R)return M?.("008",wo(O,e)),null;const{sourceX:L,sourceY:H,targetX:X,targetY:Y}=((e,t,n,o,r,i)=>{const a=xa(n,e,t),s=xa(i,o,r);return{sourceX:a.x,sourceY:a.y,targetX:s.x,targetY:s.y}})(o,O,z,E,R,D);return t.createElement(P,{key:e.id,id:e.id,className:n([e.className,s]),type:A,data:e.data,selected:!!e.selected,animated:!!e.animated,hidden:!!e.hidden,label:e.label,labelStyle:e.labelStyle,labelShowBg:e.labelShowBg,labelBgStyle:e.labelBgStyle,labelBgPadding:e.labelBgPadding,labelBgBorderRadius:e.labelBgBorderRadius,style:e.style,source:e.source,target:e.target,sourceHandleId:e.sourceHandle,targetHandleId:e.targetHandle,markerEnd:e.markerEnd,markerStart:e.markerStart,sourceX:L,sourceY:H,targetX:X,targetY:Y,sourcePosition:z,targetPosition:D,elementsSelectable:S,onContextMenu:l,onMouseEnter:c,onMouseMove:u,onMouseLeave:d,onClick:h,onEdgeDoubleClick:f,onReconnect:g,onReconnectStart:p,onReconnectEnd:m,reconnectRadius:y,rfId:i,ariaLabel:e.ariaLabel,isFocusable:$,isReconnectable:B,pathOptions:"pathOptions"in e?e.pathOptions:void 0,interactionWidth:e.interactionWidth,disableKeyboardA11y:b})})))))),v):null};Pa.displayName="EdgeRenderer";var Ia=t.memo(Pa);const Oa=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function Ra({children:e}){const n=No(Oa);return t.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:n}},e)}const za={[or.Left]:or.Right,[or.Right]:or.Left,[or.Top]:or.Bottom,[or.Bottom]:or.Top},Da=({nodeId:e,handleType:n,style:o,type:r=tr.Bezier,CustomComponent:i,connectionStatus:a})=>{const{fromNode:s,handleId:l,toX:c,toY:u,connectionMode:d}=No(t.useCallback((t=>({fromNode:t.nodeInternals.get(e),handleId:t.connectionHandleId,toX:(t.connectionPosition.x-t.transform[0])/t.transform[2],toY:(t.connectionPosition.y-t.transform[1])/t.transform[2],connectionMode:t.connectionMode})),[e]),Je),h=s?.[Yo]?.handleBounds;let f=h?.[n];if(d===Qo.Loose&&(f=f||h?.["source"===n?"target":"source"]),!s||!f)return null;const g=l?f.find((e=>e.id===l)):f[0],p=g?g.x+g.width/2:(s.width??0)/2,m=g?g.y+g.height/2:s.height??0,y=(s.positionAbsolute?.x??0)+p,v=(s.positionAbsolute?.y??0)+m,b=g?.position,x=b?za[b]:null;if(!b||!x)return null;if(i)return t.createElement(i,{connectionLineType:r,connectionLineStyle:o,fromNode:s,fromHandle:g,fromX:y,fromY:v,toX:c,toY:u,fromPosition:b,toPosition:x,connectionStatus:a});let w="";const S={sourceX:y,sourceY:v,sourcePosition:b,targetX:c,targetY:u,targetPosition:x};return r===tr.Bezier?[w]=mr(S):r===tr.Step?[w]=ur({...S,borderRadius:0}):r===tr.SmoothStep?[w]=ur(S):r===tr.SimpleBezier?[w]=ir(S):w=`M${y},${v} ${c},${u}`,t.createElement("path",{d:w,fill:"none",className:"react-flow__connection-path",style:o})};Da.displayName="ConnectionLine";const $a=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function Ta({containerStyle:e,style:o,type:r,component:i}){const{nodeId:a,handleType:s,nodesConnectable:l,width:c,height:u,connectionStatus:d}=No($a,Je);return!!(a&&s&&c&&l)?t.createElement("svg",{style:e,width:c,height:u,className:"react-flow__edges react-flow__connectionline react-flow__container"},t.createElement("g",{className:n(["react-flow__connection",d])},t.createElement(Da,{nodeId:a,handleType:s,style:o,type:r,CustomComponent:i,connectionStatus:d}))):null}function Ba(e,n){t.useRef(null),Mo();return t.useMemo((()=>n(e)),[e])}const La=({nodeTypes:e,edgeTypes:n,onMove:o,onMoveStart:r,onMoveEnd:i,onInit:a,onNodeClick:s,onEdgeClick:l,onNodeDoubleClick:c,onEdgeDoubleClick:u,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,onSelectionContextMenu:p,onSelectionStart:m,onSelectionEnd:y,connectionLineType:v,connectionLineStyle:b,connectionLineComponent:x,connectionLineContainerStyle:w,selectionKeyCode:S,selectionOnDrag:E,selectionMode:_,multiSelectionKeyCode:C,panActivationKeyCode:N,zoomActivationKeyCode:M,deleteKeyCode:k,onlyRenderVisibleElements:A,elementsSelectable:P,selectNodesOnDrag:I,defaultViewport:O,translateExtent:R,minZoom:z,maxZoom:D,preventScrolling:$,defaultMarkerColor:T,zoomOnScroll:B,zoomOnPinch:L,panOnScroll:H,panOnScrollSpeed:X,panOnScrollMode:Y,zoomOnDoubleClick:V,panOnDrag:K,onPaneClick:F,onPaneMouseEnter:Z,onPaneMouseMove:W,onPaneMouseLeave:j,onPaneScroll:q,onPaneContextMenu:U,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,elevateEdgesOnSelect:le,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})=>{const fe=Ba(e,ca),ge=Ba(n,ba);return function(e){const n=Ni(),o=t.useRef(!1);t.useEffect((()=>{!o.current&&n.viewportInitialized&&e&&(setTimeout((()=>e(n)),1),o.current=!0)}),[e,n.viewportInitialized])}(a),t.createElement(la,{onPaneClick:F,onPaneMouseEnter:Z,onPaneMouseMove:W,onPaneMouseLeave:j,onPaneContextMenu:U,onPaneScroll:q,deleteKeyCode:k,selectionKeyCode:S,selectionOnDrag:E,selectionMode:_,onSelectionStart:m,onSelectionEnd:y,multiSelectionKeyCode:C,panActivationKeyCode:N,zoomActivationKeyCode:M,elementsSelectable:P,onMove:o,onMoveStart:r,onMoveEnd:i,zoomOnScroll:B,zoomOnPinch:L,zoomOnDoubleClick:V,panOnScroll:H,panOnScrollSpeed:X,panOnScrollMode:Y,panOnDrag:K,defaultViewport:O,translateExtent:R,minZoom:z,maxZoom:D,onSelectionContextMenu:p,preventScrolling:$,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,disableKeyboardA11y:ce},t.createElement(Ra,null,t.createElement(Ia,{edgeTypes:ge,onEdgeClick:l,onEdgeDoubleClick:u,onlyRenderVisibleElements:A,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,defaultMarkerColor:T,noPanClassName:se,elevateEdgesOnSelect:!!le,disableKeyboardA11y:ce,rfId:he},t.createElement(Ta,{style:b,type:v,component:x,containerStyle:w})),t.createElement("div",{className:"react-flow__edgelabel-renderer"}),t.createElement(ha,{nodeTypes:fe,onNodeClick:s,onNodeDoubleClick:c,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,selectNodesOnDrag:I,onlyRenderVisibleElements:A,noPanClassName:se,noDragClassName:ie,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})))};La.displayName="GraphView";var Ha=t.memo(La);const Xa=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],Ya={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:Xa,nodeExtent:Xa,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:Qo.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:(e,t)=>{},isValidConnection:void 0},Va=()=>{return e=(e,t)=>({...Ya,setNodes:n=>{const{nodeInternals:o,nodeOrigin:r,elevateNodesOnSelect:i}=t();e({nodeInternals:vi(n,o,r,i)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{const{defaultEdgeOptions:o={}}=t();e({edges:n.map((e=>({...o,...e})))})},setDefaultNodesAndEdges:(n,o)=>{const r=void 0!==n,i=void 0!==o,a=r?vi(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map;e({nodeInternals:a,edges:i?o:[],hasDefaultNodes:r,hasDefaultEdges:i})},updateNodeDimensions:n=>{const{onNodesChange:o,nodeInternals:r,fitViewOnInit:i,fitViewOnInitDone:a,fitViewOnInitOptions:s,domNode:l,nodeOrigin:c}=t(),u=l?.querySelector(".react-flow__viewport");if(!u)return;const d=window.getComputedStyle(u),{m22:h}=new window.DOMMatrixReadOnly(d.transform),f=n.reduce(((e,t)=>{const n=r.get(t.id);if(n?.hidden)r.set(n.id,{...n,[Yo]:{...n[Yo],handleBounds:void 0}});else if(n){const o=Oo(t.nodeElement);o.width&&o.height&&(n.width!==o.width||n.height!==o.height||t.forceUpdate)&&(r.set(n.id,{...n,[Yo]:{...n[Yo],handleBounds:{source:qi(".source",t.nodeElement,h,c),target:qi(".target",t.nodeElement,h,c)}},...o}),e.push({id:n.id,type:"dimensions",dimensions:o}))}return e}),[]);yi(r,c);const g=a||i&&!a&&bi(t,{initial:!0,...s});e({nodeInternals:new Map(r),fitViewOnInitDone:g}),f?.length>0&&o?.(f)},updateNodePositions:(e,n=!0,o=!1)=>{const{triggerNodeChanges:r}=t();r(e.map((e=>{const t={id:e.id,type:"position",dragging:o};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t})))},triggerNodeChanges:n=>{const{onNodesChange:o,nodeInternals:r,hasDefaultNodes:i,nodeOrigin:a,getNodes:s,elevateNodesOnSelect:l}=t();if(n?.length){if(i){const t=vi(Bi(n,s()),r,a,l);e({nodeInternals:t})}o?.(n)}},addSelectedNodes:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Li(e,!0))):(a=Hi(i(),n),s=Hi(r,[])),Si({changedNodes:a,changedEdges:s,get:t,set:e})},addSelectedEdges:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Li(e,!0))):(a=Hi(r,n),s=Hi(i(),[])),Si({changedNodes:s,changedEdges:a,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:o}={})=>{const{edges:r,getNodes:i}=t(),a=o||r;Si({changedNodes:(n||i()).map((e=>(e.selected=!1,Li(e.id,!1)))),changedEdges:a.map((e=>Li(e.id,!1))),get:t,set:e})},setMinZoom:n=>{const{d3Zoom:o,maxZoom:r}=t();o?.scaleExtent([n,r]),e({minZoom:n})},setMaxZoom:n=>{const{d3Zoom:o,minZoom:r}=t();o?.scaleExtent([r,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{const{edges:n,getNodes:o}=t();Si({changedNodes:o().filter((e=>e.selected)).map((e=>Li(e.id,!1))),changedEdges:n.filter((e=>e.selected)).map((e=>Li(e.id,!1))),get:t,set:e})},setNodeExtent:n=>{const{nodeInternals:o}=t();o.forEach((e=>{e.positionAbsolute=zo(e.position,n)})),e({nodeExtent:n,nodeInternals:new Map(o)})},panBy:e=>{const{transform:n,width:o,height:r,d3Zoom:i,d3Selection:a,translateExtent:s}=t();if(!i||!a||!e.x&&!e.y)return!1;const l=io.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),c=[[0,0],[o,r]],u=i?.constrain()(l,c,s);return i.transform(a,u),n[0]!==u.x||n[1]!==u.y||n[2]!==u.k},cancelConnection:()=>e({connectionNodeId:Ya.connectionNodeId,connectionHandleId:Ya.connectionHandleId,connectionHandleType:Ya.connectionHandleType,connectionStatus:Ya.connectionStatus,connectionStartHandle:Ya.connectionStartHandle,connectionEndHandle:Ya.connectionEndHandle}),reset:()=>e({...Ya})}),t=Object.is,e?Qe(e,t):Qe;var e,t},Ka=({children:e})=>{const n=t.useRef(null);return n.current||(n.current=Va()),t.createElement(mo,{value:n.current},e)};Ka.displayName="ReactFlowProvider";const Fa=({children:e})=>t.useContext(po)?t.createElement(t.Fragment,null,e):t.createElement(Ka,null,e);Fa.displayName="ReactFlowWrapper";const Za={input:Zr,default:Kr,output:jr,group:qr},Wa={default:yr,straight:fr,step:hr,smoothstep:dr,simplebezier:ar},ja=[0,0],qa=[15,15],Ua={x:0,y:0,zoom:1},Ga={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},Qa=t.forwardRef((({nodes:e,edges:o,defaultNodes:r,defaultEdges:i,className:a,nodeTypes:s=Za,edgeTypes:l=Wa,onNodeClick:c,onEdgeClick:u,onInit:d,onMove:h,onMoveStart:f,onMoveEnd:g,onConnect:p,onConnectStart:m,onConnectEnd:y,onClickConnectStart:v,onClickConnectEnd:b,onNodeMouseEnter:x,onNodeMouseMove:w,onNodeMouseLeave:S,onNodeContextMenu:E,onNodeDoubleClick:_,onNodeDragStart:C,onNodeDrag:N,onNodeDragStop:M,onNodesDelete:k,onEdgesDelete:A,onSelectionChange:P,onSelectionDragStart:I,onSelectionDrag:O,onSelectionDragStop:R,onSelectionContextMenu:z,onSelectionStart:D,onSelectionEnd:$,connectionMode:T=Qo.Strict,connectionLineType:B=tr.Bezier,connectionLineStyle:L,connectionLineComponent:H,connectionLineContainerStyle:X,deleteKeyCode:Y="Backspace",selectionKeyCode:V="Shift",selectionOnDrag:K=!1,selectionMode:F=er.Full,panActivationKeyCode:Z="Space",multiSelectionKeyCode:W=(Wo()?"Meta":"Control"),zoomActivationKeyCode:j=(Wo()?"Meta":"Control"),snapToGrid:q=!1,snapGrid:U=qa,onlyRenderVisibleElements:G=!1,selectNodesOnDrag:Q=!0,nodesDraggable:J,nodesConnectable:ee,nodesFocusable:te,nodeOrigin:ne=ja,edgesFocusable:oe,edgesUpdatable:re,elementsSelectable:ie,defaultViewport:ae=Ua,minZoom:se=.5,maxZoom:le=2,translateExtent:ce=Xa,preventScrolling:ue=!0,nodeExtent:de,defaultMarkerColor:he="#b1b1b7",zoomOnScroll:fe=!0,zoomOnPinch:ge=!0,panOnScroll:pe=!1,panOnScrollSpeed:me=.5,panOnScrollMode:ye=Jo.Free,zoomOnDoubleClick:ve=!0,panOnDrag:be=!0,onPaneClick:xe,onPaneMouseEnter:we,onPaneMouseMove:Se,onPaneMouseLeave:Ee,onPaneScroll:_e,onPaneContextMenu:Ce,children:Ne,onEdgeContextMenu:Me,onEdgeDoubleClick:ke,onEdgeMouseEnter:Ae,onEdgeMouseMove:Pe,onEdgeMouseLeave:Ie,onEdgeUpdate:Oe,onEdgeUpdateStart:Re,onEdgeUpdateEnd:ze,onReconnect:De,onReconnectStart:$e,onReconnectEnd:Te,reconnectRadius:Be=10,edgeUpdaterRadius:Le=10,onNodesChange:He,onEdgesChange:Xe,noDragClassName:Ye="nodrag",noWheelClassName:Ve="nowheel",noPanClassName:Ke="nopan",fitView:Fe=!1,fitViewOptions:Ze,connectOnClick:We=!0,attributionPosition:je,proOptions:qe,defaultEdgeOptions:Ue,elevateNodesOnSelect:Ge=!0,elevateEdgesOnSelect:Qe=!1,disableKeyboardA11y:Je=!1,autoPanOnConnect:et=!0,autoPanOnNodeDrag:tt=!0,connectionRadius:nt=20,isValidConnection:ot,onError:rt,style:it,id:at,nodeDragThreshold:st,...lt},ct)=>{const ut=at||"1";return t.createElement("div",{...lt,style:{...it,...Ga},ref:ct,className:n(["react-flow",a]),"data-testid":"rf__wrapper",id:at},t.createElement(Fa,null,t.createElement(Ha,{onInit:d,onMove:h,onMoveStart:f,onMoveEnd:g,onNodeClick:c,onEdgeClick:u,onNodeMouseEnter:x,onNodeMouseMove:w,onNodeMouseLeave:S,onNodeContextMenu:E,onNodeDoubleClick:_,nodeTypes:s,edgeTypes:l,connectionLineType:B,connectionLineStyle:L,connectionLineComponent:H,connectionLineContainerStyle:X,selectionKeyCode:V,selectionOnDrag:K,selectionMode:F,deleteKeyCode:Y,multiSelectionKeyCode:W,panActivationKeyCode:Z,zoomActivationKeyCode:j,onlyRenderVisibleElements:G,selectNodesOnDrag:Q,defaultViewport:ae,translateExtent:ce,minZoom:se,maxZoom:le,preventScrolling:ue,zoomOnScroll:fe,zoomOnPinch:ge,zoomOnDoubleClick:ve,panOnScroll:pe,panOnScrollSpeed:me,panOnScrollMode:ye,panOnDrag:be,onPaneClick:xe,onPaneMouseEnter:we,onPaneMouseMove:Se,onPaneMouseLeave:Ee,onPaneScroll:_e,onPaneContextMenu:Ce,onSelectionContextMenu:z,onSelectionStart:D,onSelectionEnd:$,onEdgeContextMenu:Me,onEdgeDoubleClick:ke,onEdgeMouseEnter:Ae,onEdgeMouseMove:Pe,onEdgeMouseLeave:Ie,onReconnect:De??Oe,onReconnectStart:$e??Re,onReconnectEnd:Te??ze,reconnectRadius:Be??Le,defaultMarkerColor:he,noDragClassName:Ye,noWheelClassName:Ve,noPanClassName:Ke,elevateEdgesOnSelect:Qe,rfId:ut,disableKeyboardA11y:Je,nodeOrigin:ne,nodeExtent:de}),t.createElement(ii,{nodes:e,edges:o,defaultNodes:r,defaultEdges:i,onConnect:p,onConnectStart:m,onConnectEnd:y,onClickConnectStart:v,onClickConnectEnd:b,nodesDraggable:J,nodesConnectable:ee,nodesFocusable:te,edgesFocusable:oe,edgesUpdatable:re,elementsSelectable:ie,elevateNodesOnSelect:Ge,minZoom:se,maxZoom:le,nodeExtent:de,onNodesChange:He,onEdgesChange:Xe,snapToGrid:q,snapGrid:U,connectionMode:T,translateExtent:ce,connectOnClick:We,defaultEdgeOptions:Ue,fitView:Fe,fitViewOptions:Ze,onNodesDelete:k,onEdgesDelete:A,onNodeDragStart:C,onNodeDrag:N,onNodeDragStop:M,onSelectionDrag:O,onSelectionDragStart:I,onSelectionDragStop:R,noPanClassName:Ke,nodeOrigin:ne,rfId:ut,autoPanOnConnect:et,autoPanOnNodeDrag:tt,onError:rt,connectionRadius:nt,isValidConnection:ot,nodeDragThreshold:st}),t.createElement(ti,{onSelectionChange:P}),Ne,t.createElement(Po,{proOptions:qe,position:je}),t.createElement(hi,{rfId:ut,disableKeyboardA11y:Je})))}));var Ja;Qa.displayName="ReactFlow",e.ResizeControlVariant=void 0,(Ja=e.ResizeControlVariant||(e.ResizeControlVariant={})).Line="line",Ja.Handle="handle";const es={width:0,height:0,x:0,y:0},ts={...es,pointerX:0,pointerY:0,aspectRatio:1};var ns=t.memo((function({nodeId:o,position:r,variant:i=e.ResizeControlVariant.Handle,className:a,style:s={},children:l,color:c,minWidth:u=10,minHeight:d=10,maxWidth:h=Number.MAX_VALUE,maxHeight:f=Number.MAX_VALUE,keepAspectRatio:g=!1,shouldResize:p,onResizeStart:m,onResize:y,onResizeEnd:v}){const b=xr(),x="string"==typeof o?o:b,w=Mo(),S=t.useRef(null),E=t.useRef(ts),_=t.useRef(es),C=Qi(),N=i===e.ResizeControlVariant.Line?"right":"bottom-right",M=r??N;t.useEffect((()=>{if(!S.current||!x)return;const e=Ee(S.current),t=M.includes("right")||M.includes("left"),n=M.includes("bottom")||M.includes("top"),o=M.includes("left"),r=M.includes("top"),i=Te().on("start",(e=>{const t=w.getState().nodeInternals.get(x),{xSnapped:n,ySnapped:o}=C(e);_.current={width:t?.width??0,height:t?.height??0,x:t?.position.x??0,y:t?.position.y??0},E.current={..._.current,pointerX:n,pointerY:o,aspectRatio:_.current.width/_.current.height},m?.(e,{..._.current})})).on("drag",(e=>{const{nodeInternals:i,triggerNodeChanges:a}=w.getState(),{xSnapped:s,ySnapped:l}=C(e),c=i.get(x);if(c){const i=[],{pointerX:m,pointerY:v,width:b,height:w,x:S,y:C,aspectRatio:N}=E.current,{x:M,y:k,width:A,height:P}=_.current,I=Math.floor(t?s-m:0),O=Math.floor(n?l-v:0);let R=Ro(b+(o?-I:I),u,h),z=Ro(w+(r?-O:O),d,f);if(g){const e=R/z,o=t&&n;R=e<=N&&o||n&&!t?z*N:R,z=e>N&&o||t&&!n?R/N:z,R>=h?(R=h,z=h/N):R<=u&&(R=u,z=u/N),z>=f?(z=f,R=f*N):z<=d&&(z=d,R=d*N)}const D=R!==A,$=z!==P;if(o||r){const e=o?S-(R-b):S,t=r?C-(z-w):C,n=e!==M&&D,a=t!==k&&$;if(n||a){const o={id:c.id,type:"position",position:{x:n?e:M,y:a?t:k}};i.push(o),_.current.x=o.position.x,_.current.y=o.position.y}}if(D||$){const e={id:x,type:"dimensions",updateStyle:!0,resizing:!0,dimensions:{width:R,height:z}};i.push(e),_.current.width=R,_.current.height=z}if(0===i.length)return;const T=function({width:e,prevWidth:t,height:n,prevHeight:o,invertX:r,invertY:i}){const a=e-t,s=n-o,l=[a>0?1:a<0?-1:0,s>0?1:s<0?-1:0];return a&&r&&(l[0]=-1*l[0]),s&&i&&(l[1]=-1*l[1]),l}({width:_.current.width,prevWidth:A,height:_.current.height,prevHeight:P,invertX:o,invertY:r}),B={..._.current,direction:T},L=p?.(e,B);if(!1===L)return;y?.(e,B),a(i)}})).on("end",(e=>{const t={id:x,type:"dimensions",resizing:!1};v?.(e,{..._.current}),w.getState().triggerNodeChanges([t])}));return e.call(i),()=>{e.on(".drag",null)}}),[x,M,u,d,h,f,g,C,m,y,v]);const k=M.split("-"),A=i===e.ResizeControlVariant.Line?"borderColor":"backgroundColor",P=c?{...s,[A]:c}:s;return t.createElement("div",{className:n(["react-flow__resize-control","nodrag",...k,i,a]),ref:S,style:P},l)}));const os=["top-left","top-right","bottom-left","bottom-right"],rs=["top","right","bottom","left"];e.NodeResizeControl=ns,e.NodeResizer=function({nodeId:n,isVisible:o=!0,handleClassName:r,handleStyle:i,lineClassName:a,lineStyle:s,color:l,minWidth:c=10,minHeight:u=10,maxWidth:d=Number.MAX_VALUE,maxHeight:h=Number.MAX_VALUE,keepAspectRatio:f=!1,shouldResize:g,onResizeStart:p,onResize:m,onResizeEnd:y}){return o?t.createElement(t.Fragment,null,rs.map((o=>t.createElement(ns,{key:o,className:a,style:s,nodeId:n,position:o,variant:e.ResizeControlVariant.Line,color:l,minWidth:c,minHeight:u,maxWidth:d,maxHeight:h,onResizeStart:p,keepAspectRatio:f,shouldResize:g,onResize:m,onResizeEnd:y}))),os.map((e=>t.createElement(ns,{key:e,className:r,style:i,nodeId:n,position:e,color:l,minWidth:c,minHeight:u,maxWidth:d,maxHeight:h,onResizeStart:p,keepAspectRatio:f,shouldResize:g,onResize:m,onResizeEnd:y})))):null}}));
