'use client'

import React, { useState } from 'react'
import { useStore } from '@/store/useStore'
import { Plus, Search, Bot, Settings, Play, Pause, Code } from 'lucide-react'
import { AutoGenDemo } from '@/components/Demo/AutoGenDemo'
import { useTranslation } from '@/hooks/useTranslation'

export function AgentsView() {
  const { agents } = useStore()
  const t = useTranslation()
  const [activeTab, setActiveTab] = useState<'agents' | 'demo'>('agents')

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('agents.title')}</h1>
            <p className="text-gray-600 mt-1">
              {t('agents.description')}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('agents')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  activeTab === 'agents'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {t('agents.title')}
              </button>
              <button
                onClick={() => setActiveTab('demo')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  activeTab === 'demo'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Code className="w-4 h-4 mr-1" />
                Demo
              </button>
            </div>
            {activeTab === 'agents' && (
              <button className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                <Plus className="w-4 h-4" />
                <span>New Agent</span>
              </button>
            )}
          </div>
        </div>

        {/* Search - only show for agents tab */}
        {activeTab === 'agents' && (
          <div className="mt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search agents..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'demo' ? (
          <AutoGenDemo />
        ) : (
          <div className="p-6">
            {agents.length === 0 ? (
          /* Empty State */
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Bot className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {t('agents.noAgents')}
            </h3>
            <p className="text-gray-600 mb-6 max-w-md">
              {t('agents.noAgentsDescription')}
            </p>
            <button className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
              <Plus className="w-5 h-5" />
              <span>{t('agents.createFirstAgent')}</span>
            </button>
          </div>
        ) : (
          /* Agents List */
          <div className="space-y-4">
            {agents.map((agent) => (
              <div
                key={agent.id}
                className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Bot className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {agent.name}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {agent.description}
                      </p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="bg-gray-100 px-2 py-1 rounded">
                          {agent.type}
                        </span>
                        <span>
                          Created {agent.createdAt.toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                      <Settings className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                      <Play className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
          </div>
        )}
      </div>
    </div>
  )
}
