'use client'

import React, { useState } from 'react'
import { useStore } from '@/store/useStore'
import { Plus, Search, Bot, Settings, Play, Pause, Code, X } from 'lucide-react'
import { AutoGenDemo } from '@/components/Demo/AutoGenDemo'
import { useTranslation } from '@/hooks/useTranslation'
import * as Dialog from '@radix-ui/react-dialog'
import { Agent } from '@/store/useStore'

export function AgentsView() {
  const { agents, addAgent, aiModels } = useStore()
  const t = useTranslation()
  const [activeTab, setActiveTab] = useState<'agents' | 'demo'>('agents')
  const [isAgentDialogOpen, setIsAgentDialogOpen] = useState(false)
  const [agentForm, setAgentForm] = useState({
    name: '',
    description: '',
    type: 'autogen' as 'autogen' | 'custom',
    modelId: '',
    systemMessage: '',
    temperature: 0.7,
    maxTokens: 1000
  })

  const handleAddAgent = () => {
    setAgentForm({
      name: '',
      description: '',
      type: 'autogen',
      modelId: '',
      systemMessage: '',
      temperature: 0.7,
      maxTokens: 1000
    })
    setIsAgentDialogOpen(true)
  }

  const handleSaveAgent = () => {
    const selectedModel = aiModels.find(m => m.id === agentForm.modelId)

    const newAgent: Agent = {
      id: `agent-${Date.now()}`,
      name: agentForm.name,
      description: agentForm.description,
      type: agentForm.type,
      config: {
        modelId: agentForm.modelId,
        modelName: selectedModel?.name || 'Unknown Model',
        systemMessage: agentForm.systemMessage,
        temperature: agentForm.temperature,
        maxTokens: agentForm.maxTokens,
        provider: selectedModel?.provider || 'Unknown'
      },
      createdAt: new Date()
    }

    addAgent(newAgent)
    setIsAgentDialogOpen(false)
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('agents.title')}</h1>
            <p className="text-gray-600 mt-1">
              {t('agents.description')}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('agents')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  activeTab === 'agents'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {t('agents.title')}
              </button>
              <button
                onClick={() => setActiveTab('demo')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  activeTab === 'demo'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Code className="w-4 h-4 mr-1" />
                Demo
              </button>
            </div>
            {activeTab === 'agents' && (
              <button
                onClick={handleAddAgent}
                className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                <span>新建代理</span>
              </button>
            )}
          </div>
        </div>

        {/* Search - only show for agents tab */}
        {activeTab === 'agents' && (
          <div className="mt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search agents..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'demo' ? (
          <AutoGenDemo />
        ) : (
          <div className="p-6">
            {agents.length === 0 ? (
          /* Empty State */
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Bot className="w-12 h-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {t('agents.noAgents')}
            </h3>
            <p className="text-gray-600 mb-6 max-w-md">
              {t('agents.noAgentsDescription')}
            </p>
            <button
              onClick={handleAddAgent}
              className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              <Plus className="w-5 h-5" />
              <span>{t('agents.createFirstAgent')}</span>
            </button>
          </div>
        ) : (
          /* Agents List */
          <div className="space-y-4">
            {agents.map((agent) => (
              <div
                key={agent.id}
                className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Bot className="w-6 h-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">
                        {agent.name}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {agent.description}
                      </p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="bg-gray-100 px-2 py-1 rounded">
                          {agent.type}
                        </span>
                        <span>
                          Created {agent.createdAt.toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                      <Settings className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                      <Play className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
          </div>
        )}
      </div>

      {/* Agent Creation Dialog */}
      <Dialog.Root open={isAgentDialogOpen} onOpenChange={setIsAgentDialogOpen}>
        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/50 z-50" />
          <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl z-50 w-full max-w-lg p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <Dialog.Title className="text-lg font-semibold text-gray-900">
                创建新的智能代理
              </Dialog.Title>
              <Dialog.Close className="text-gray-400 hover:text-gray-600">
                <X className="w-5 h-5" />
              </Dialog.Close>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  代理名称
                </label>
                <input
                  type="text"
                  value={agentForm.name}
                  onChange={(e) => setAgentForm({ ...agentForm, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                  placeholder="例如：内容分析助手"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  描述
                </label>
                <textarea
                  value={agentForm.description}
                  onChange={(e) => setAgentForm({ ...agentForm, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                  rows={3}
                  placeholder="描述这个代理的功能..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  代理类型
                </label>
                <select
                  value={agentForm.type}
                  onChange={(e) => setAgentForm({ ...agentForm, type: e.target.value as 'autogen' | 'custom' })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                >
                  <option value="autogen" className="text-gray-900">AutoGen 代理</option>
                  <option value="custom" className="text-gray-900">自定义代理</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  AI模型
                </label>
                <select
                  value={agentForm.modelId}
                  onChange={(e) => setAgentForm({ ...agentForm, modelId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                >
                  <option value="" className="text-gray-900">选择AI模型</option>
                  {aiModels.map((model) => (
                    <option key={model.id} value={model.id} className="text-gray-900">
                      {model.name} ({model.provider})
                    </option>
                  ))}
                </select>
                {aiModels.length === 0 && (
                  <p className="text-sm text-gray-500 mt-1">
                    暂无可用的AI模型。请先在设置 → Integrations 中添加模型。
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  系统消息
                </label>
                <textarea
                  value={agentForm.systemMessage}
                  onChange={(e) => setAgentForm({ ...agentForm, systemMessage: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900"
                  rows={4}
                  placeholder="你是一个有用的助手，专门用于..."
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    温度
                  </label>
                  <input
                    type="number"
                    value={agentForm.temperature}
                    onChange={(e) => setAgentForm({ ...agentForm, temperature: parseFloat(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                    max="2"
                    step="0.1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    最大令牌数
                  </label>
                  <input
                    type="number"
                    value={agentForm.maxTokens}
                    onChange={(e) => setAgentForm({ ...agentForm, maxTokens: parseInt(e.target.value) })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="1"
                    max="32000"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <Dialog.Close className="px-4 py-2 text-gray-600 hover:text-gray-700 border border-gray-300 rounded-lg transition-colors">
                取消
              </Dialog.Close>
              <button
                onClick={handleSaveAgent}
                disabled={!agentForm.name || !agentForm.modelId}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                创建代理
              </button>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </div>
  )
}
