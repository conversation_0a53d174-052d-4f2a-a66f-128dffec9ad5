'use client'

import React from 'react'
import { useStore } from '@/store/useStore'
import { TopMenuBar } from './TopMenuBar'
import { LeftNavigation } from './LeftNavigation'
import { CenterContent } from './CenterContent'
import { RightResults } from './RightResults'

export function MainLayout() {
  const { sidebarCollapsed } = useStore()

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Top Menu Bar */}
      <TopMenuBar />
      
      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Navigation */}
        <LeftNavigation />
        
        {/* Center Content */}
        <div className={`flex-1 transition-all duration-300 ${
          sidebarCollapsed ? 'ml-16' : 'ml-64'
        }`}>
          <CenterContent />
        </div>
        
        {/* Right Results Panel */}
        <div className="w-80 border-l border-gray-200 bg-white">
          <RightResults />
        </div>
      </div>
    </div>
  )
}
