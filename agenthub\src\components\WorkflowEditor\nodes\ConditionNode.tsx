'use client'

import React from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { GitBranch } from 'lucide-react'

interface ConditionNodeData {
  label: string
}

export function ConditionNode({ data }: NodeProps<ConditionNodeData>) {
  return (
    <div className="bg-white border-2 border-yellow-300 rounded-lg shadow-md min-w-[140px]">
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      
      <div className="p-3">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
            <GitBranch className="w-3 h-3 text-yellow-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 text-sm">{data.label}</h3>
            <span className="text-xs text-yellow-600 bg-yellow-50 px-2 py-0.5 rounded">
              Condition
            </span>
          </div>
        </div>
      </div>
      
      <Handle type="source" position={Position.Right} id="true" className="w-3 h-3" style={{ top: '30%' }} />
      <Handle type="source" position={Position.Right} id="false" className="w-3 h-3" style={{ top: '70%' }} />
    </div>
  )
}
