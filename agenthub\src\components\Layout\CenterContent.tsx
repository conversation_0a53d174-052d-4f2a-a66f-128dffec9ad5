'use client'

import React from 'react'
import { useStore } from '@/store/useStore'
import { ChatView } from '@/components/Views/ChatView'
import { WorkflowsView } from '@/components/Views/WorkflowsView'
import { MarketplaceView } from '@/components/Views/MarketplaceView'
import { AgentsView } from '@/components/Views/AgentsView'
import { SettingsView } from '@/components/Views/SettingsView'

export function CenterContent() {
  const { currentView } = useStore()

  const renderView = () => {
    switch (currentView) {
      case 'chat':
        return <ChatView />
      case 'workflows':
        return <WorkflowsView />
      case 'marketplace':
        return <MarketplaceView />
      case 'agents':
        return <AgentsView />
      case 'settings':
        return <SettingsView />
      default:
        return <WorkflowsView />
    }
  }

  return (
    <div className="h-full bg-gray-50">
      {renderView()}
    </div>
  )
}
