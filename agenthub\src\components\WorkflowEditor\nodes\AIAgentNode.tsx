'use client'

import React from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { <PERSON><PERSON>, Settings } from 'lucide-react'

interface AIAgentNodeData {
  label: string
  description?: string
  agentType: 'autogen' | 'custom'
  config?: {
    model?: string
    temperature?: number
    maxTokens?: number
    context7Libraries?: string[]
    systemMessage?: string
  }
}

export function AIAgentNode({ data }: NodeProps<AIAgentNodeData>) {
  return (
    <div className="bg-white border-2 border-blue-300 rounded-lg shadow-md min-w-[200px]">
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      
      <div className="p-4">
        <div className="flex items-center space-x-2 mb-2">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <Bot className="w-4 h-4 text-blue-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 text-sm">{data.label}</h3>
            <span className="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded">
              AI Agent
            </span>
          </div>
          <button className="p-1 text-gray-400 hover:text-gray-600">
            <Settings className="w-3 h-3" />
          </button>
        </div>
        
        {data.description && (
          <p className="text-xs text-gray-600 mb-2">{data.description}</p>
        )}
        
        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span className="text-gray-500">Type:</span>
            <span className="font-medium capitalize">{data.agentType}</span>
          </div>
          {data.config?.model && (
            <div className="flex justify-between text-xs">
              <span className="text-gray-500">Model:</span>
              <span className="font-medium">{data.config.model}</span>
            </div>
          )}
          {data.config?.temperature !== undefined && (
            <div className="flex justify-between text-xs">
              <span className="text-gray-500">Temperature:</span>
              <span className="font-medium">{data.config.temperature}</span>
            </div>
          )}
          {data.config?.context7Libraries && data.config.context7Libraries.length > 0 && (
            <div className="flex justify-between text-xs">
              <span className="text-gray-500">Context7:</span>
              <span className="font-medium text-green-600">Enabled</span>
            </div>
          )}
        </div>
      </div>
      
      <Handle type="source" position={Position.Right} className="w-3 h-3" />
    </div>
  )
}
