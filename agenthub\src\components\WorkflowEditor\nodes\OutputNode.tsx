'use client'

import React from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { Flag } from 'lucide-react'

interface OutputNodeData {
  label: string
}

export function OutputNode({ data }: NodeProps<OutputNodeData>) {
  return (
    <div className="bg-white border-2 border-red-300 rounded-lg shadow-md min-w-[120px]">
      <Handle type="target" position={Position.Left} className="w-3 h-3" />
      
      <div className="p-3">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
            <Flag className="w-3 h-3 text-red-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 text-sm">{data.label}</h3>
            <span className="text-xs text-red-600 bg-red-50 px-2 py-0.5 rounded">
              Output
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
