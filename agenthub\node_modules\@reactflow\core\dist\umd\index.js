!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactFlowCore={},e.React,e.ReactDOM)}(this,(function(e,t,n){"use strict";function o(e){if("string"==typeof e||"number"==typeof e)return""+e;let t="";if(Array.isArray(e))for(let n,r=0;r<e.length;r++)""!==(n=o(e[r]))&&(t+=(t&&" ")+n);else for(let n in e)e[n]&&(t+=(t&&" ")+n);return t}function r(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var i,a,s,l={},c={},u={},d={get exports(){return u},set exports(e){u=e}},h={};function f(){return a||(a=1,function(e){e.exports=function(){if(i)return h;i=1;var e=t,n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=e.useState,r=e.useEffect,a=e.useLayoutEffect,s=e.useDebugValue;function l(e){var t=e.getSnapshot;e=e.value;try{var o=t();return!n(e,o)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),i=o({inst:{value:n,getSnapshot:t}}),c=i[0].inst,u=i[1];return a((function(){c.value=n,c.getSnapshot=t,l(c)&&u({inst:c})}),[e,n,t]),r((function(){return l(c)&&u({inst:c}),e((function(){l(c)&&u({inst:c})}))}),[e]),s(n),n};return h.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:c,h}()}(d)),u}
/**
   * @license React
   * use-sync-external-store-shim/with-selector.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   */!function(e){e.exports=function(){if(s)return c;s=1;var e=t,n=f(),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},r=n.useSyncExternalStore,i=e.useRef,a=e.useEffect,l=e.useMemo,u=e.useDebugValue;return c.useSyncExternalStoreWithSelector=function(e,t,n,s,c){var d=i(null);if(null===d.current){var h={hasValue:!1,value:null};d.current=h}else h=d.current;d=l((function(){function e(e){if(!a){if(a=!0,r=e,e=s(e),void 0!==c&&h.hasValue){var t=h.value;if(c(t,e))return i=t}return i=e}if(t=i,o(r,e))return t;var n=s(e);return void 0!==c&&c(t,n)?t:(r=e,i=n)}var r,i,a=!1,l=void 0===n?null:n;return[function(){return e(t())},null===l?void 0:function(){return e(l())}]}),[t,n,s,c]);var f=r(e,d[0],d[1]);return a((function(){h.hasValue=!0,h.value=f}),[f]),u(f),f},c}()}({get exports(){return l},set exports(e){l=e}});var g=r(l);const p=e=>{let t;const n=new Set,o=(e,o)=>{const r="function"==typeof e?e(t):e;if(!Object.is(r,t)){const e=t;t=(null!=o?o:"object"!=typeof r)?r:Object.assign({},t,r),n.forEach((n=>n(t,e)))}},r=()=>t,i={setState:o,getState:r,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}};return t=e(o,r,i),i},{useSyncExternalStoreWithSelector:m}=g;function y(e,n=e.getState,o){const r=m(e.subscribe,e.getState,e.getServerState||e.getState,n,o);return t.useDebugValue(r),r}const v=(e,t)=>{const n=(e=>e?p(e):p)(e),o=(e,o=t)=>y(n,e,o);return Object.assign(o,n),o},b=t.createContext(null),S=b.Provider,w=e=>`Node type "${e}" not found. Using fallback type "default".`,x=()=>"The React Flow parent container needs a width and a height to render the graph.",E=()=>"Only child nodes can use a parent extent.",_=e=>`Marker type "${e}" doesn't exist.`,C=(e,t)=>`Couldn't create edge for ${e?"target":"source"} handle id: "${e?t.targetHandle:t.sourceHandle}", edge id: ${t.id}.`,M=()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",N=e=>`Edge type "${e}" not found. Using fallback type "default".`,P=e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,k=(()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001")();function A(e,n){const o=t.useContext(b);if(null===o)throw new Error(k);return y(o,e,n)}const O=()=>{const e=t.useContext(b);if(null===e)throw new Error(k);return t.useMemo((()=>({getState:e.getState,setState:e.setState,subscribe:e.subscribe,destroy:e.destroy})),[e])},I=e=>e.userSelectionActive?"none":"all";function D({position:e,children:n,className:r,style:i,...a}){const s=A(I),l=`${e}`.split("-");return t.createElement("div",{className:o(["react-flow__panel",r,...l]),style:{...i,pointerEvents:s},...a},n)}function R({proOptions:e,position:n="bottom-right"}){return e?.hideAttribution?null:t.createElement(D,{position:n,className:"react-flow__attribution","data-message":"Please only hide this attribution when you are subscribed to React Flow Pro: https://reactflow.dev/pro"},t.createElement("a",{href:"https://reactflow.dev",target:"_blank",rel:"noopener noreferrer","aria-label":"React Flow attribution"},"React Flow"))}var z=t.memo((({x:e,y:n,label:r,labelStyle:i={},labelShowBg:a=!0,labelBgStyle:s={},labelBgPadding:l=[2,4],labelBgBorderRadius:c=2,children:u,className:d,...h})=>{const f=t.useRef(null),[g,p]=t.useState({x:0,y:0,width:0,height:0}),m=o(["react-flow__edge-textwrapper",d]);return t.useEffect((()=>{if(f.current){const e=f.current.getBBox();p({x:e.x,y:e.y,width:e.width,height:e.height})}}),[r]),void 0!==r&&r?t.createElement("g",{transform:`translate(${e-g.width/2} ${n-g.height/2})`,className:m,visibility:g.width?"visible":"hidden",...h},a&&t.createElement("rect",{width:g.width+2*l[0],x:-l[0],y:-l[1],height:g.height+2*l[1],className:"react-flow__edge-textbg",style:s,rx:c,ry:c}),t.createElement("text",{className:"react-flow__edge-text",y:g.height/2,dy:"0.3em",ref:f,style:i},r),u):null}));const T=e=>({width:e.offsetWidth,height:e.offsetHeight}),$=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),B=(e={x:0,y:0},t)=>({x:$(e.x,t[0][0],t[1][0]),y:$(e.y,t[0][1],t[1][1])}),L=(e,t,n)=>e<t?$(Math.abs(e-t),1,50)/50:e>n?-$(Math.abs(e-n),1,50)/50:0,H=(e,t)=>[20*L(e.x,35,t.width-35),20*L(e.y,35,t.height-35)],X=e=>e.getRootNode?.()||window?.document,Y=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),V=({x:e,y:t,width:n,height:o})=>({x:e,y:t,x2:e+n,y2:t+o}),F=({x:e,y:t,x2:n,y2:o})=>({x:e,y:t,width:n-e,height:o-t}),K=e=>({...e.positionAbsolute||{x:0,y:0},width:e.width||0,height:e.height||0}),Z=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)},j=e=>!isNaN(e)&&isFinite(e),W=Symbol.for("internals"),q=["Enter"," ","Escape"];function U(e){const t=((e=>"nativeEvent"in e)(e)?e.nativeEvent:e).composedPath?.()?.[0]||e.target;return["INPUT","SELECT","TEXTAREA"].includes(t?.nodeName)||t?.hasAttribute("contenteditable")||!!t?.closest(".nokey")}const G=e=>"clientX"in e,Q=(e,t)=>{const n=G(e),o=n?e.clientX:e.touches?.[0].clientX,r=n?e.clientY:e.touches?.[0].clientY;return{x:o-(t?.left??0),y:r-(t?.top??0)}},J=()=>"undefined"!=typeof navigator&&navigator?.userAgent?.indexOf("Mac")>=0,ee=({id:e,path:n,labelX:o,labelY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g=20})=>t.createElement(t.Fragment,null,t.createElement("path",{id:e,style:d,d:n,fill:"none",className:"react-flow__edge-path",markerEnd:h,markerStart:f}),g&&t.createElement("path",{d:n,fill:"none",strokeOpacity:0,strokeWidth:g,className:"react-flow__edge-interaction"}),i&&j(o)&&j(r)?t.createElement(z,{x:o,y:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u}):null);ee.displayName="BaseEdge";function te(e,t,n){return void 0===n?n:o=>{const r=t().edges.find((t=>t.id===e));r&&n(o,{...r})}}function ne({sourceX:e,sourceY:t,targetX:n,targetY:o}){const r=Math.abs(n-e)/2,i=n<e?n+r:n-r,a=Math.abs(o-t)/2;return[i,o<t?o+a:o-a,r,a]}function oe({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:a,targetControlY:s}){const l=.125*e+.375*r+.375*a+.125*n,c=.125*t+.375*i+.375*s+.125*o;return[l,c,Math.abs(l-e),Math.abs(c-t)]}var re,ie,ae,se,le,ce;function ue({pos:t,x1:n,y1:o,x2:r,y2:i}){return t===e.Position.Left||t===e.Position.Right?[.5*(n+r),o]:[n,.5*(o+i)]}function de({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top}){const[s,l]=ue({pos:o,x1:t,y1:n,x2:r,y2:i}),[c,u]=ue({pos:a,x1:r,y1:i,x2:t,y2:n}),[d,h,f,g]=oe({sourceX:t,sourceY:n,targetX:r,targetY:i,sourceControlX:s,sourceControlY:l,targetControlX:c,targetControlY:u});return[`M${t},${n} C${s},${l} ${c},${u} ${r},${i}`,d,h,f,g]}e.ConnectionMode=void 0,(re=e.ConnectionMode||(e.ConnectionMode={})).Strict="strict",re.Loose="loose",e.PanOnScrollMode=void 0,(ie=e.PanOnScrollMode||(e.PanOnScrollMode={})).Free="free",ie.Vertical="vertical",ie.Horizontal="horizontal",e.SelectionMode=void 0,(ae=e.SelectionMode||(e.SelectionMode={})).Partial="partial",ae.Full="full",e.ConnectionLineType=void 0,(se=e.ConnectionLineType||(e.ConnectionLineType={})).Bezier="default",se.Straight="straight",se.Step="step",se.SmoothStep="smoothstep",se.SimpleBezier="simplebezier",e.MarkerType=void 0,(le=e.MarkerType||(e.MarkerType={})).Arrow="arrow",le.ArrowClosed="arrowclosed",e.Position=void 0,(ce=e.Position||(e.Position={})).Left="left",ce.Top="top",ce.Right="right",ce.Bottom="bottom";const he=t.memo((({sourceX:n,sourceY:o,targetX:r,targetY:i,sourcePosition:a=e.Position.Bottom,targetPosition:s=e.Position.Top,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:p,markerStart:m,interactionWidth:y})=>{const[v,b,S]=de({sourceX:n,sourceY:o,sourcePosition:a,targetX:r,targetY:i,targetPosition:s});return t.createElement(ee,{path:v,labelX:b,labelY:S,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:p,markerStart:m,interactionWidth:y})}));he.displayName="SimpleBezierEdge";const fe={[e.Position.Left]:{x:-1,y:0},[e.Position.Right]:{x:1,y:0},[e.Position.Top]:{x:0,y:-1},[e.Position.Bottom]:{x:0,y:1}},ge=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function pe({source:t,sourcePosition:n=e.Position.Bottom,target:o,targetPosition:r=e.Position.Top,center:i,offset:a}){const s=fe[n],l=fe[r],c={x:t.x+s.x*a,y:t.y+s.y*a},u={x:o.x+l.x*a,y:o.y+l.y*a},d=(({source:t,sourcePosition:n=e.Position.Bottom,target:o})=>n===e.Position.Left||n===e.Position.Right?t.x<o.x?{x:1,y:0}:{x:-1,y:0}:t.y<o.y?{x:0,y:1}:{x:0,y:-1})({source:c,sourcePosition:n,target:u}),h=0!==d.x?"x":"y",f=d[h];let g,p,m=[];const y={x:0,y:0},v={x:0,y:0},[b,S,w,x]=ne({sourceX:t.x,sourceY:t.y,targetX:o.x,targetY:o.y});if(s[h]*l[h]==-1){g=i.x??b,p=i.y??S;const e=[{x:g,y:c.y},{x:g,y:u.y}],t=[{x:c.x,y:p},{x:u.x,y:p}];m=s[h]===f?"x"===h?e:t:"x"===h?t:e}else{const e=[{x:c.x,y:u.y}],i=[{x:u.x,y:c.y}];if(m="x"===h?s.x===f?i:e:s.y===f?e:i,n===r){const e=Math.abs(t[h]-o[h]);if(e<=a){const n=Math.min(a-1,a-e);s[h]===f?y[h]=(c[h]>t[h]?-1:1)*n:v[h]=(u[h]>o[h]?-1:1)*n}}if(n!==r){const t="x"===h?"y":"x",n=s[h]===l[t],o=c[t]>u[t],r=c[t]<u[t];(1===s[h]&&(!n&&o||n&&r)||1!==s[h]&&(!n&&r||n&&o))&&(m="x"===h?e:i)}const d={x:c.x+y.x,y:c.y+y.y},b={x:u.x+v.x,y:u.y+v.y};Math.max(Math.abs(d.x-m[0].x),Math.abs(b.x-m[0].x))>=Math.max(Math.abs(d.y-m[0].y),Math.abs(b.y-m[0].y))?(g=(d.x+b.x)/2,p=m[0].y):(g=m[0].x,p=(d.y+b.y)/2)}return[[t,{x:c.x+y.x,y:c.y+y.y},...m,{x:u.x+v.x,y:u.y+v.y},o],g,p,w,x]}function me({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top,borderRadius:s=5,centerX:l,centerY:c,offset:u=20}){const[d,h,f,g,p]=pe({source:{x:t,y:n},sourcePosition:o,target:{x:r,y:i},targetPosition:a,center:{x:l,y:c},offset:u});return[d.reduce(((e,t,n)=>{let o="";return o=n>0&&n<d.length-1?function(e,t,n,o){const r=Math.min(ge(e,t)/2,ge(t,n)/2,o),{x:i,y:a}=t;if(e.x===i&&i===n.x||e.y===a&&a===n.y)return`L${i} ${a}`;if(e.y===a)return`L ${i+r*(e.x<n.x?-1:1)},${a}Q ${i},${a} ${i},${a+r*(e.y<n.y?1:-1)}`;const s=e.x<n.x?1:-1;return`L ${i},${a+r*(e.y<n.y?-1:1)}Q ${i},${a} ${i+r*s},${a}`}(d[n-1],t,d[n+1],s):`${0===n?"M":"L"}${t.x} ${t.y}`,e+=o}),""),h,f,g,p]}const ye=t.memo((({sourceX:n,sourceY:o,targetX:r,targetY:i,label:a,labelStyle:s,labelShowBg:l,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:d,style:h,sourcePosition:f=e.Position.Bottom,targetPosition:g=e.Position.Top,markerEnd:p,markerStart:m,pathOptions:y,interactionWidth:v})=>{const[b,S,w]=me({sourceX:n,sourceY:o,sourcePosition:f,targetX:r,targetY:i,targetPosition:g,borderRadius:y?.borderRadius,offset:y?.offset});return t.createElement(ee,{path:b,labelX:S,labelY:w,label:a,labelStyle:s,labelShowBg:l,labelBgStyle:c,labelBgPadding:u,labelBgBorderRadius:d,style:h,markerEnd:p,markerStart:m,interactionWidth:v})}));ye.displayName="SmoothStepEdge";const ve=t.memo((e=>t.createElement(ye,{...e,pathOptions:t.useMemo((()=>({borderRadius:0,offset:e.pathOptions?.offset})),[e.pathOptions?.offset])})));function be({sourceX:e,sourceY:t,targetX:n,targetY:o}){const[r,i,a,s]=ne({sourceX:e,sourceY:t,targetX:n,targetY:o});return[`M ${e},${t}L ${n},${o}`,r,i,a,s]}ve.displayName="StepEdge";const Se=t.memo((({sourceX:e,sourceY:n,targetX:o,targetY:r,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})=>{const[p,m,y]=be({sourceX:e,sourceY:n,targetX:o,targetY:r});return t.createElement(ee,{path:p,labelX:m,labelY:y,label:i,labelStyle:a,labelShowBg:s,labelBgStyle:l,labelBgPadding:c,labelBgBorderRadius:u,style:d,markerEnd:h,markerStart:f,interactionWidth:g})}));function we(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function xe({pos:t,x1:n,y1:o,x2:r,y2:i,c:a}){switch(t){case e.Position.Left:return[n-we(n-r,a),o];case e.Position.Right:return[n+we(r-n,a),o];case e.Position.Top:return[n,o-we(o-i,a)];case e.Position.Bottom:return[n,o+we(i-o,a)]}}function Ee({sourceX:t,sourceY:n,sourcePosition:o=e.Position.Bottom,targetX:r,targetY:i,targetPosition:a=e.Position.Top,curvature:s=.25}){const[l,c]=xe({pos:o,x1:t,y1:n,x2:r,y2:i,c:s}),[u,d]=xe({pos:a,x1:r,y1:i,x2:t,y2:n,c:s}),[h,f,g,p]=oe({sourceX:t,sourceY:n,targetX:r,targetY:i,sourceControlX:l,sourceControlY:c,targetControlX:u,targetControlY:d});return[`M${t},${n} C${l},${c} ${u},${d} ${r},${i}`,h,f,g,p]}Se.displayName="StraightEdge";const _e=t.memo((({sourceX:n,sourceY:o,targetX:r,targetY:i,sourcePosition:a=e.Position.Bottom,targetPosition:s=e.Position.Top,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:p,markerStart:m,pathOptions:y,interactionWidth:v})=>{const[b,S,w]=Ee({sourceX:n,sourceY:o,sourcePosition:a,targetX:r,targetY:i,targetPosition:s,curvature:y?.curvature});return t.createElement(ee,{path:b,labelX:S,labelY:w,label:l,labelStyle:c,labelShowBg:u,labelBgStyle:d,labelBgPadding:h,labelBgBorderRadius:f,style:g,markerEnd:p,markerStart:m,interactionWidth:v})}));function Ce(e,t){if(Object.is(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(const[n,o]of e)if(!Object.is(o,t.get(n)))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(let o=0;o<n.length;o++)if(!Object.prototype.hasOwnProperty.call(t,n[o])||!Object.is(e[n[o]],t[n[o]]))return!1;return!0}_e.displayName="BezierEdge";const Me=t.createContext(null),Ne=Me.Provider;Me.Consumer;const Pe=()=>t.useContext(Me),ke=e=>"id"in e&&"source"in e&&"target"in e,Ae=e=>"id"in e&&!("source"in e)&&!("target"in e),Oe=({source:e,sourceHandle:t,target:n,targetHandle:o})=>`reactflow__edge-${e}${t||""}-${n}${o||""}`,Ie=(e,t)=>{if(void 0===e)return"";if("string"==typeof e)return e;return`${t?`${t}__`:""}${Object.keys(e).sort().map((t=>`${t}=${e[t]}`)).join("&")}`},De=(e,t)=>{if(!e.source||!e.target)return t;let n;return n=ke(e)?{...e}:{...e,id:Oe(e)},((e,t)=>t.some((t=>!(t.source!==e.source||t.target!==e.target||t.sourceHandle!==e.sourceHandle&&(t.sourceHandle||e.sourceHandle)||t.targetHandle!==e.targetHandle&&(t.targetHandle||e.targetHandle)))))(n,t)?t:t.concat(n)},Re=(e,t,n,o={shouldReplaceId:!0})=>{const{id:r,...i}=e;if(!t.source||!t.target)return n;if(!n.find((e=>e.id===r)))return n;const a={...i,id:o.shouldReplaceId?Oe(t):r,source:t.source,target:t.target,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle};return n.filter((e=>e.id!==r)).concat(a)},ze=({x:e,y:t},[n,o,r],i,[a,s])=>{const l={x:(e-n)/r,y:(t-o)/r};return i?{x:a*Math.round(l.x/a),y:s*Math.round(l.y/s)}:l},Te=({x:e,y:t},[n,o,r])=>({x:e*r+n,y:t*r+o}),$e=(e,t=[0,0])=>{if(!e)return{x:0,y:0,positionAbsolute:{x:0,y:0}};const n=(e.width??0)*t[0],o=(e.height??0)*t[1],r={x:e.position.x-n,y:e.position.y-o};return{...r,positionAbsolute:e.positionAbsolute?{x:e.positionAbsolute.x-n,y:e.positionAbsolute.y-o}:r}},Be=(e,t=[0,0])=>{if(0===e.length)return{x:0,y:0,width:0,height:0};const n=e.reduce(((e,n)=>{const{x:o,y:r}=$e(n,t).positionAbsolute;return Y(e,V({x:o,y:r,width:n.width||0,height:n.height||0}))}),{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return F(n)},Le=(e,t,[n,o,r]=[0,0,1],i=!1,a=!1,s=[0,0])=>{const l={x:(t.x-n)/r,y:(t.y-o)/r,width:t.width/r,height:t.height/r},c=[];return e.forEach((e=>{const{width:t,height:n,selectable:o=!0,hidden:r=!1}=e;if(a&&!o||r)return!1;const{positionAbsolute:u}=$e(e,s),d={x:u.x,y:u.y,width:t||0,height:n||0},h=Z(l,d);(void 0===t||void 0===n||null===t||null===n||i&&h>0||h>=(t||0)*(n||0)||e.dragging)&&c.push(e)})),c},He=(e,t)=>{const n=e.map((e=>e.id));return t.filter((e=>n.includes(e.source)||n.includes(e.target)))},Xe=(e,t,n,o,r,i=.1)=>{const a=t/(e.width*(1+i)),s=n/(e.height*(1+i)),l=Math.min(a,s),c=$(l,o,r);return{x:t/2-(e.x+e.width/2)*c,y:n/2-(e.y+e.height/2)*c,zoom:c}},Ye=(e,t=0)=>e.transition().duration(t);function Ve(e,t,n,o){return(t[n]||[]).reduce(((t,r)=>(`${e.id}-${r.id}-${n}`!==o&&t.push({id:r.id||null,type:n,nodeId:e.id,x:(e.positionAbsolute?.x??0)+r.x+r.width/2,y:(e.positionAbsolute?.y??0)+r.y+r.height/2}),t)),[])}const Fe={source:null,target:null,sourceHandle:null,targetHandle:null},Ke=()=>({handleDomNode:null,isValid:!1,connection:Fe,endHandle:null});function Ze(t,n,o,r,i,a,s){const l="target"===i,c=s.querySelector(`.react-flow__handle[data-id="${t?.nodeId}-${t?.id}-${t?.type}"]`),u={...Ke(),handleDomNode:c};if(c){const t=je(void 0,c),i=c.getAttribute("data-nodeid"),s=c.getAttribute("data-handleid"),d=c.classList.contains("connectable"),h=c.classList.contains("connectableend"),f={source:l?i:o,sourceHandle:l?s:r,target:l?o:i,targetHandle:l?r:s};u.connection=f;d&&h&&(n===e.ConnectionMode.Strict?l&&"source"===t||!l&&"target"===t:i!==o||s!==r)&&(u.endHandle={nodeId:i,handleId:s,type:t},u.isValid=a(f))}return u}function je(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function We(e){e?.classList.remove("valid","connecting","react-flow__handle-valid","react-flow__handle-connecting")}function qe(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}function Ue({event:e,handleId:t,nodeId:n,onConnect:o,isTarget:r,getState:i,setState:a,isValidConnection:s,edgeUpdaterType:l,onReconnectEnd:c}){const u=X(e.target),{connectionMode:d,domNode:h,autoPanOnConnect:f,connectionRadius:g,onConnectStart:p,panBy:m,getNodes:y,cancelConnection:v}=i();let b,S=0;const{x:w,y:x}=Q(e),E=u?.elementFromPoint(w,x),_=je(l,E),C=h?.getBoundingClientRect();if(!C||!_)return;let M,N=Q(e,C),P=!1,k=null,A=!1,O=null;const I=function({nodes:e,nodeId:t,handleId:n,handleType:o}){return e.reduce(((e,r)=>{if(r[W]){const{handleBounds:i}=r[W];let a=[],s=[];i&&(a=Ve(r,i,"source",`${t}-${n}-${o}`),s=Ve(r,i,"target",`${t}-${n}-${o}`)),e.push(...a,...s)}return e}),[])}({nodes:y(),nodeId:n,handleId:t,handleType:_}),D=()=>{if(!f)return;const[e,t]=H(N,C);m({x:e,y:t}),S=requestAnimationFrame(D)};function R(e){const{transform:o}=i();N=Q(e,C);const{handle:l,validHandleResult:c}=function(e,t,n,o,r,i){const{x:a,y:s}=Q(e),l=t.elementsFromPoint(a,s).find((e=>e.classList.contains("react-flow__handle")));if(l){const e=l.getAttribute("data-nodeid");if(e){const t=je(void 0,l),o=l.getAttribute("data-handleid"),a=i({nodeId:e,id:o,type:t});if(a){const i=r.find((n=>n.nodeId===e&&n.type===t&&n.id===o));return{handle:{id:o,type:t,nodeId:e,x:i?.x||n.x,y:i?.y||n.y},validHandleResult:a}}}}let c=[],u=1/0;if(r.forEach((e=>{const t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){const n=i(e);t<=u&&(t<u?c=[{handle:e,validHandleResult:n}]:t===u&&c.push({handle:e,validHandleResult:n}),u=t)}})),!c.length)return{handle:null,validHandleResult:Ke()};if(1===c.length)return c[0];const d=c.some((({validHandleResult:e})=>e.isValid)),h=c.some((({handle:e})=>"target"===e.type));return c.find((({handle:e,validHandleResult:t})=>h?"target"===e.type:!d||t.isValid))||c[0]}(e,u,ze(N,o,!1,[1,1]),g,I,(e=>Ze(e,d,n,t,r?"target":"source",s,u)));if(b=l,P||(D(),P=!0),O=c.handleDomNode,k=c.connection,A=c.isValid,a({connectionPosition:b&&A?Te({x:b.x,y:b.y},o):N,connectionStatus:qe(!!b,A),connectionEndHandle:c.endHandle}),!b&&!A&&!O)return We(M);k.source!==k.target&&O&&(We(M),M=O,O.classList.add("connecting","react-flow__handle-connecting"),O.classList.toggle("valid",A),O.classList.toggle("react-flow__handle-valid",A))}function z(e){(b||O)&&k&&A&&o?.(k),i().onConnectEnd?.(e),l&&c?.(e),We(M),v(),cancelAnimationFrame(S),P=!1,A=!1,k=null,O=null,u.removeEventListener("mousemove",R),u.removeEventListener("mouseup",z),u.removeEventListener("touchmove",R),u.removeEventListener("touchend",z)}a({connectionPosition:N,connectionStatus:null,connectionNodeId:n,connectionHandleId:t,connectionHandleType:_,connectionStartHandle:{nodeId:n,handleId:t,type:_},connectionEndHandle:null}),p?.(e,{nodeId:n,handleId:t,handleType:_}),u.addEventListener("mousemove",R),u.addEventListener("mouseup",z),u.addEventListener("touchmove",R),u.addEventListener("touchend",z)}const Ge=()=>!0,Qe=e=>({connectionStartHandle:e.connectionStartHandle,connectOnClick:e.connectOnClick,noPanClassName:e.noPanClassName}),Je=t.forwardRef((({type:n="source",position:r=e.Position.Top,isValidConnection:i,isConnectable:a=!0,isConnectableStart:s=!0,isConnectableEnd:l=!0,id:c,onConnect:u,children:d,className:h,onMouseDown:f,onTouchStart:g,...p},m)=>{const y=c||null,v="target"===n,b=O(),S=Pe(),{connectOnClick:w,noPanClassName:x}=A(Qe,Ce),{connecting:E,clickConnecting:_}=A(((e,t,n)=>o=>{const{connectionStartHandle:r,connectionEndHandle:i,connectionClickStartHandle:a}=o;return{connecting:r?.nodeId===e&&r?.handleId===t&&r?.type===n||i?.nodeId===e&&i?.handleId===t&&i?.type===n,clickConnecting:a?.nodeId===e&&a?.handleId===t&&a?.type===n}})(S,y,n),Ce);S||b.getState().onError?.("010",M());const C=e=>{const{defaultEdgeOptions:t,onConnect:n,hasDefaultEdges:o}=b.getState(),r={...t,...e};if(o){const{edges:e,setEdges:t}=b.getState();t(De(r,e))}n?.(r),u?.(r)},N=e=>{if(!S)return;const t=G(e);s&&(t&&0===e.button||!t)&&Ue({event:e,handleId:y,nodeId:S,onConnect:C,isTarget:v,getState:b.getState,setState:b.setState,isValidConnection:i||b.getState().isValidConnection||Ge}),t?f?.(e):g?.(e)};return t.createElement("div",{"data-handleid":y,"data-nodeid":S,"data-handlepos":r,"data-id":`${S}-${y}-${n}`,className:o(["react-flow__handle",`react-flow__handle-${r}`,"nodrag",x,h,{source:!v,target:v,connectable:a,connectablestart:s,connectableend:l,connecting:_,connectionindicator:a&&(s&&!E||l&&E)}]),onMouseDown:N,onTouchStart:N,onClick:w?e=>{const{onClickConnectStart:t,onClickConnectEnd:o,connectionClickStartHandle:r,connectionMode:a,isValidConnection:l}=b.getState();if(!S||!r&&!s)return;if(!r)return t?.(e,{nodeId:S,handleId:y,handleType:n}),void b.setState({connectionClickStartHandle:{nodeId:S,type:n,handleId:y}});const c=X(e.target),u=i||l||Ge,{connection:d,isValid:h}=Ze({nodeId:S,id:y,type:n},a,r.nodeId,r.handleId||null,r.type,u,c);h&&C(d),o?.(e),b.setState({connectionClickStartHandle:null})}:void 0,ref:m,...p},d)}));Je.displayName="Handle";var et=t.memo(Je);const tt=({data:n,isConnectable:o,targetPosition:r=e.Position.Top,sourcePosition:i=e.Position.Bottom})=>t.createElement(t.Fragment,null,t.createElement(et,{type:"target",position:r,isConnectable:o}),n?.label,t.createElement(et,{type:"source",position:i,isConnectable:o}));tt.displayName="DefaultNode";var nt=t.memo(tt);const ot=({data:n,isConnectable:o,sourcePosition:r=e.Position.Bottom})=>t.createElement(t.Fragment,null,n?.label,t.createElement(et,{type:"source",position:r,isConnectable:o}));ot.displayName="InputNode";var rt=t.memo(ot);const it=({data:n,isConnectable:o,targetPosition:r=e.Position.Top})=>t.createElement(t.Fragment,null,t.createElement(et,{type:"target",position:r,isConnectable:o}),n?.label);it.displayName="OutputNode";var at=t.memo(it);const st=()=>null;st.displayName="GroupNode";const lt=e=>({selectedNodes:e.getNodes().filter((e=>e.selected)),selectedEdges:e.edges.filter((e=>e.selected)).map((e=>({...e})))}),ct=e=>e.id;function ut(e,t){return Ce(e.selectedNodes.map(ct),t.selectedNodes.map(ct))&&Ce(e.selectedEdges.map(ct),t.selectedEdges.map(ct))}const dt=t.memo((({onSelectionChange:e})=>{const n=O(),{selectedNodes:o,selectedEdges:r}=A(lt,ut);return t.useEffect((()=>{const t={nodes:o,edges:r};e?.(t),n.getState().onSelectionChange.forEach((e=>e(t)))}),[o,r,e]),null}));dt.displayName="SelectionListener";const ht=e=>!!e.onSelectionChange;function ft({onSelectionChange:e}){const n=A(ht);return e||n?t.createElement(dt,{onSelectionChange:e}):null}const gt=e=>({setNodes:e.setNodes,setEdges:e.setEdges,setDefaultNodesAndEdges:e.setDefaultNodesAndEdges,setMinZoom:e.setMinZoom,setMaxZoom:e.setMaxZoom,setTranslateExtent:e.setTranslateExtent,setNodeExtent:e.setNodeExtent,reset:e.reset});function pt(e,n){t.useEffect((()=>{void 0!==e&&n(e)}),[e])}function mt(e,n,o){t.useEffect((()=>{void 0!==n&&o({[e]:n})}),[n])}const yt=({nodes:e,edges:n,defaultNodes:o,defaultEdges:r,onConnect:i,onConnectStart:a,onConnectEnd:s,onClickConnectStart:l,onClickConnectEnd:c,nodesDraggable:u,nodesConnectable:d,nodesFocusable:h,edgesFocusable:f,edgesUpdatable:g,elevateNodesOnSelect:p,minZoom:m,maxZoom:y,nodeExtent:v,onNodesChange:b,onEdgesChange:S,elementsSelectable:w,connectionMode:x,snapGrid:E,snapToGrid:_,translateExtent:C,connectOnClick:M,defaultEdgeOptions:N,fitView:P,fitViewOptions:k,onNodesDelete:I,onEdgesDelete:D,onNodeDrag:R,onNodeDragStart:z,onNodeDragStop:T,onSelectionDrag:$,onSelectionDragStart:B,onSelectionDragStop:L,noPanClassName:H,nodeOrigin:X,rfId:Y,autoPanOnConnect:V,autoPanOnNodeDrag:F,onError:K,connectionRadius:Z,isValidConnection:j,nodeDragThreshold:W})=>{const{setNodes:q,setEdges:U,setDefaultNodesAndEdges:G,setMinZoom:Q,setMaxZoom:J,setTranslateExtent:ee,setNodeExtent:te,reset:ne}=A(gt,Ce),oe=O();return t.useEffect((()=>{const e=r?.map((e=>({...e,...N})));return G(o,e),()=>{ne()}}),[]),mt("defaultEdgeOptions",N,oe.setState),mt("connectionMode",x,oe.setState),mt("onConnect",i,oe.setState),mt("onConnectStart",a,oe.setState),mt("onConnectEnd",s,oe.setState),mt("onClickConnectStart",l,oe.setState),mt("onClickConnectEnd",c,oe.setState),mt("nodesDraggable",u,oe.setState),mt("nodesConnectable",d,oe.setState),mt("nodesFocusable",h,oe.setState),mt("edgesFocusable",f,oe.setState),mt("edgesUpdatable",g,oe.setState),mt("elementsSelectable",w,oe.setState),mt("elevateNodesOnSelect",p,oe.setState),mt("snapToGrid",_,oe.setState),mt("snapGrid",E,oe.setState),mt("onNodesChange",b,oe.setState),mt("onEdgesChange",S,oe.setState),mt("connectOnClick",M,oe.setState),mt("fitViewOnInit",P,oe.setState),mt("fitViewOnInitOptions",k,oe.setState),mt("onNodesDelete",I,oe.setState),mt("onEdgesDelete",D,oe.setState),mt("onNodeDrag",R,oe.setState),mt("onNodeDragStart",z,oe.setState),mt("onNodeDragStop",T,oe.setState),mt("onSelectionDrag",$,oe.setState),mt("onSelectionDragStart",B,oe.setState),mt("onSelectionDragStop",L,oe.setState),mt("noPanClassName",H,oe.setState),mt("nodeOrigin",X,oe.setState),mt("rfId",Y,oe.setState),mt("autoPanOnConnect",V,oe.setState),mt("autoPanOnNodeDrag",F,oe.setState),mt("onError",K,oe.setState),mt("connectionRadius",Z,oe.setState),mt("isValidConnection",j,oe.setState),mt("nodeDragThreshold",W,oe.setState),pt(e,q),pt(n,U),pt(m,Q),pt(y,J),pt(C,ee),pt(v,te),null},vt={display:"none"},bt={position:"absolute",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)",clipPath:"inset(100%)"},St="react-flow__node-desc",wt="react-flow__edge-desc",xt=e=>e.ariaLiveMessage;function Et({rfId:e}){const n=A(xt);return t.createElement("div",{id:`react-flow__aria-live-${e}`,"aria-live":"assertive","aria-atomic":"true",style:bt},n)}function _t({rfId:e,disableKeyboardA11y:n}){return t.createElement(t.Fragment,null,t.createElement("div",{id:`${St}-${e}`,style:vt},"Press enter or space to select a node.",!n&&"You can then use the arrow keys to move the node around."," Press delete to remove it and escape to cancel."," "),t.createElement("div",{id:`${wt}-${e}`,style:vt},"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel."),!n&&t.createElement(Et,{rfId:e}))}var Ct=(e=null,n={actInsideInputWithModifier:!0})=>{const[o,r]=t.useState(!1),i=t.useRef(!1),a=t.useRef(new Set([])),[s,l]=t.useMemo((()=>{if(null!==e){const t=(Array.isArray(e)?e:[e]).filter((e=>"string"==typeof e)).map((e=>e.split("+"))),n=t.reduce(((e,t)=>e.concat(...t)),[]);return[t,n]}return[[],[]]}),[e]);return t.useEffect((()=>{const t="undefined"!=typeof document?document:null,o=n?.target||t;if(null!==e){const e=e=>{i.current=e.ctrlKey||e.metaKey||e.shiftKey;if((!i.current||i.current&&!n.actInsideInputWithModifier)&&U(e))return!1;const t=Nt(e.code,l);a.current.add(e[t]),Mt(s,a.current,!1)&&(e.preventDefault(),r(!0))},t=e=>{if((!i.current||i.current&&!n.actInsideInputWithModifier)&&U(e))return!1;const t=Nt(e.code,l);Mt(s,a.current,!0)?(r(!1),a.current.clear()):a.current.delete(e[t]),"Meta"===e.key&&a.current.clear(),i.current=!1},c=()=>{a.current.clear(),r(!1)};return o?.addEventListener("keydown",e),o?.addEventListener("keyup",t),window.addEventListener("blur",c),()=>{o?.removeEventListener("keydown",e),o?.removeEventListener("keyup",t),window.removeEventListener("blur",c)}}}),[e,r]),o};function Mt(e,t,n){return e.filter((e=>n||e.length===t.size)).some((e=>e.every((e=>t.has(e)))))}function Nt(e,t){return t.includes(e)?"code":"key"}var Pt={value:()=>{}};function kt(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new At(o)}function At(e){this._=e}function Ot(e,t){return e.trim().split(/^|\s+/).map((function(e){var n="",o=e.indexOf(".");if(o>=0&&(n=e.slice(o+1),e=e.slice(0,o)),e&&!t.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:n}}))}function It(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function Dt(e,t,n){for(var o=0,r=e.length;o<r;++o)if(e[o].name===t){e[o]=Pt,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}At.prototype=kt.prototype={constructor:At,on:function(e,t){var n,o=this._,r=Ot(e+"",o),i=-1,a=r.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++i<a;)if(n=(e=r[i]).type)o[n]=Dt(o[n],e.name,t);else if(null==t)for(n in o)o[n]=Dt(o[n],e.name,null);return this}for(;++i<a;)if((n=(e=r[i]).type)&&(n=It(o[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new At(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var Rt="http://www.w3.org/1999/xhtml",zt={svg:"http://www.w3.org/2000/svg",xhtml:Rt,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Tt(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),zt.hasOwnProperty(t)?{space:zt[t],local:e}:e}function $t(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Rt&&t.documentElement.namespaceURI===Rt?t.createElement(e):t.createElementNS(n,e)}}function Bt(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Lt(e){var t=Tt(e);return(t.local?Bt:$t)(t)}function Ht(){}function Xt(e){return null==e?Ht:function(){return this.querySelector(e)}}function Yt(e){return null==e?[]:Array.isArray(e)?e:Array.from(e)}function Vt(){return[]}function Ft(e){return null==e?Vt:function(){return this.querySelectorAll(e)}}function Kt(e){return function(){return this.matches(e)}}function Zt(e){return function(t){return t.matches(e)}}var jt=Array.prototype.find;function Wt(){return this.firstElementChild}var qt=Array.prototype.filter;function Ut(){return Array.from(this.children)}function Gt(e){return new Array(e.length)}function Qt(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function Jt(e){return function(){return e}}function en(e,t,n,o,r,i){for(var a,s=0,l=t.length,c=i.length;s<c;++s)(a=t[s])?(a.__data__=i[s],o[s]=a):n[s]=new Qt(e,i[s]);for(;s<l;++s)(a=t[s])&&(r[s]=a)}function tn(e,t,n,o,r,i,a){var s,l,c,u=new Map,d=t.length,h=i.length,f=new Array(d);for(s=0;s<d;++s)(l=t[s])&&(f[s]=c=a.call(l,l.__data__,s,t)+"",u.has(c)?r[s]=l:u.set(c,l));for(s=0;s<h;++s)c=a.call(e,i[s],s,i)+"",(l=u.get(c))?(o[s]=l,l.__data__=i[s],u.delete(c)):n[s]=new Qt(e,i[s]);for(s=0;s<d;++s)(l=t[s])&&u.get(f[s])===l&&(r[s]=l)}function nn(e){return e.__data__}function on(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function rn(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function an(e){return function(){this.removeAttribute(e)}}function sn(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ln(e,t){return function(){this.setAttribute(e,t)}}function cn(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function un(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function dn(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function hn(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function fn(e){return function(){this.style.removeProperty(e)}}function gn(e,t,n){return function(){this.style.setProperty(e,t,n)}}function pn(e,t,n){return function(){var o=t.apply(this,arguments);null==o?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function mn(e,t){return e.style.getPropertyValue(t)||hn(e).getComputedStyle(e,null).getPropertyValue(t)}function yn(e){return function(){delete this[e]}}function vn(e,t){return function(){this[e]=t}}function bn(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function Sn(e){return e.trim().split(/^|\s+/)}function wn(e){return e.classList||new xn(e)}function xn(e){this._node=e,this._names=Sn(e.getAttribute("class")||"")}function En(e,t){for(var n=wn(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function _n(e,t){for(var n=wn(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function Cn(e){return function(){En(this,e)}}function Mn(e){return function(){_n(this,e)}}function Nn(e,t){return function(){(t.apply(this,arguments)?En:_n)(this,e)}}function Pn(){this.textContent=""}function kn(e){return function(){this.textContent=e}}function An(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function On(){this.innerHTML=""}function In(e){return function(){this.innerHTML=e}}function Dn(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function Rn(){this.nextSibling&&this.parentNode.appendChild(this)}function zn(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Tn(){return null}function $n(){var e=this.parentNode;e&&e.removeChild(this)}function Bn(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Ln(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Hn(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}function Xn(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function Yn(e,t,n){return function(){var o,r=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(r)for(var a=0,s=r.length;a<s;++a)if((o=r[a]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t);this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function Vn(e,t,n){var o=hn(e),r=o.CustomEvent;"function"==typeof r?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function Fn(e,t){return function(){return Vn(this,e,t)}}function Kn(e,t){return function(){return Vn(this,e,t.apply(this,arguments))}}Qt.prototype={constructor:Qt,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},xn.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var Zn=[null];function jn(e,t){this._groups=e,this._parents=t}function Wn(){return new jn([[document.documentElement]],Zn)}function qn(e){return"string"==typeof e?new jn([[document.querySelector(e)]],[document.documentElement]):new jn([[e]],Zn)}function Un(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}jn.prototype=Wn.prototype={constructor:jn,select:function(e){"function"!=typeof e&&(e=Xt(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a,s=t[r],l=s.length,c=o[r]=new Array(l),u=0;u<l;++u)(i=s[u])&&(a=e.call(i,i.__data__,u,s))&&("__data__"in i&&(a.__data__=i.__data__),c[u]=a);return new jn(o,this._parents)},selectAll:function(e){e="function"==typeof e?function(e){return function(){return Yt(e.apply(this,arguments))}}(e):Ft(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var a,s=t[i],l=s.length,c=0;c<l;++c)(a=s[c])&&(o.push(e.call(a,a.__data__,c,s)),r.push(a));return new jn(o,r)},selectChild:function(e){return this.select(null==e?Wt:function(e){return function(){return jt.call(this.children,e)}}("function"==typeof e?e:Zt(e)))},selectChildren:function(e){return this.selectAll(null==e?Ut:function(e){return function(){return qt.call(this.children,e)}}("function"==typeof e?e:Zt(e)))},filter:function(e){"function"!=typeof e&&(e=Kt(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new jn(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,nn);var n=t?tn:en,o=this._parents,r=this._groups;"function"!=typeof e&&(e=Jt(e));for(var i=r.length,a=new Array(i),s=new Array(i),l=new Array(i),c=0;c<i;++c){var u=o[c],d=r[c],h=d.length,f=on(e.call(u,u&&u.__data__,c,o)),g=f.length,p=s[c]=new Array(g),m=a[c]=new Array(g),y=l[c]=new Array(h);n(u,d,p,m,y,f,t);for(var v,b,S=0,w=0;S<g;++S)if(v=p[S]){for(S>=w&&(w=S+1);!(b=m[w])&&++w<g;);v._next=b||null}}return(a=new jn(a,o))._enter=s,a._exit=l,a},enter:function(){return new jn(this._enter||this._groups.map(Gt),this._parents)},exit:function(){return new jn(this._exit||this._groups.map(Gt),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof e?(o=e(o))&&(o=o.selection()):o=o.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,a=Math.min(r,i),s=new Array(r),l=0;l<a;++l)for(var c,u=n[l],d=o[l],h=u.length,f=s[l]=new Array(h),g=0;g<h;++g)(c=u[g]||d[g])&&(f[g]=c);for(;l<r;++l)s[l]=n[l];return new jn(s,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,a=r[i];--i>=0;)(o=r[i])&&(a&&4^o.compareDocumentPosition(a)&&a.parentNode.insertBefore(o,a),a=o);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=rn);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var a,s=n[i],l=s.length,c=r[i]=new Array(l),u=0;u<l;++u)(a=s[u])&&(c[u]=a);c.sort(t)}return new jn(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var a=o[r];if(a)return a}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],a=0,s=i.length;a<s;++a)(r=i[a])&&e.call(r,r.__data__,a,i);return this},attr:function(e,t){var n=Tt(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==t?n.local?sn:an:"function"==typeof t?n.local?dn:un:n.local?cn:ln)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?fn:"function"==typeof t?pn:gn)(e,t,null==n?"":n)):mn(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?yn:"function"==typeof t?bn:vn)(e,t)):this.node()[e]},classed:function(e,t){var n=Sn(e+"");if(arguments.length<2){for(var o=wn(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof t?Nn:t?Cn:Mn)(n,t))},text:function(e){return arguments.length?this.each(null==e?Pn:("function"==typeof e?An:kn)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?On:("function"==typeof e?Dn:In)(e)):this.node().innerHTML},raise:function(){return this.each(Rn)},lower:function(){return this.each(zn)},append:function(e){var t="function"==typeof e?e:Lt(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:Lt(e),o=null==t?Tn:"function"==typeof t?t:Xt(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each($n)},clone:function(e){return this.select(e?Ln:Bn)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=Hn(e+""),a=i.length;if(!(arguments.length<2)){for(s=t?Yn:Xn,o=0;o<a;++o)this.each(s(i[o],t,n));return this}var s=this.node().__on;if(s)for(var l,c=0,u=s.length;c<u;++c)for(o=0,l=s[c];o<a;++o)if((r=i[o]).type===l.type&&r.name===l.name)return l.value},dispatch:function(e,t){return this.each(("function"==typeof t?Kn:Fn)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,a=r.length;i<a;++i)(o=r[i])&&(yield o)}};const Gn={passive:!1},Qn={capture:!0,passive:!1};function Jn(e){e.stopImmediatePropagation()}function eo(e){e.preventDefault(),e.stopImmediatePropagation()}function to(e){var t=e.document.documentElement,n=qn(e).on("dragstart.drag",eo,Qn);"onselectstart"in t?n.on("selectstart.drag",eo,Qn):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function no(e,t){var n=e.document.documentElement,o=qn(e).on("dragstart.drag",null);t&&(o.on("click.drag",eo,Qn),setTimeout((function(){o.on("click.drag",null)}),0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}var oo=e=>()=>e;function ro(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:a,y:s,dx:l,dy:c,dispatch:u}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:s,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:c,enumerable:!0,configurable:!0},_:{value:u}})}function io(e){return!e.ctrlKey&&!e.button}function ao(){return this.parentNode}function so(e,t){return null==t?{x:e.x,y:e.y}:t}function lo(){return navigator.maxTouchPoints||"ontouchstart"in this}function co(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function uo(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function ho(){}ro.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var fo=.7,go=1/fo,po="\\s*([+-]?\\d+)\\s*",mo="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",yo="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",vo=/^#([0-9a-f]{3,8})$/,bo=new RegExp(`^rgb\\(${po},${po},${po}\\)$`),So=new RegExp(`^rgb\\(${yo},${yo},${yo}\\)$`),wo=new RegExp(`^rgba\\(${po},${po},${po},${mo}\\)$`),xo=new RegExp(`^rgba\\(${yo},${yo},${yo},${mo}\\)$`),Eo=new RegExp(`^hsl\\(${mo},${yo},${yo}\\)$`),_o=new RegExp(`^hsla\\(${mo},${yo},${yo},${mo}\\)$`),Co={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Mo(){return this.rgb().formatHex()}function No(){return this.rgb().formatRgb()}function Po(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=vo.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?ko(t):3===n?new Do(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?Ao(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?Ao(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=bo.exec(e))?new Do(t[1],t[2],t[3],1):(t=So.exec(e))?new Do(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=wo.exec(e))?Ao(t[1],t[2],t[3],t[4]):(t=xo.exec(e))?Ao(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Eo.exec(e))?Lo(t[1],t[2]/100,t[3]/100,1):(t=_o.exec(e))?Lo(t[1],t[2]/100,t[3]/100,t[4]):Co.hasOwnProperty(e)?ko(Co[e]):"transparent"===e?new Do(NaN,NaN,NaN,0):null}function ko(e){return new Do(e>>16&255,e>>8&255,255&e,1)}function Ao(e,t,n,o){return o<=0&&(e=t=n=NaN),new Do(e,t,n,o)}function Oo(e){return e instanceof ho||(e=Po(e)),e?new Do((e=e.rgb()).r,e.g,e.b,e.opacity):new Do}function Io(e,t,n,o){return 1===arguments.length?Oo(e):new Do(e,t,n,null==o?1:o)}function Do(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function Ro(){return`#${Bo(this.r)}${Bo(this.g)}${Bo(this.b)}`}function zo(){const e=To(this.opacity);return`${1===e?"rgb(":"rgba("}${$o(this.r)}, ${$o(this.g)}, ${$o(this.b)}${1===e?")":`, ${e})`}`}function To(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function $o(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Bo(e){return((e=$o(e))<16?"0":"")+e.toString(16)}function Lo(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Xo(e,t,n,o)}function Ho(e){if(e instanceof Xo)return new Xo(e.h,e.s,e.l,e.opacity);if(e instanceof ho||(e=Po(e)),!e)return new Xo;if(e instanceof Xo)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),a=NaN,s=i-r,l=(i+r)/2;return s?(a=t===i?(n-o)/s+6*(n<o):n===i?(o-t)/s+2:(t-n)/s+4,s/=l<.5?i+r:2-i-r,a*=60):s=l>0&&l<1?0:a,new Xo(a,s,l,e.opacity)}function Xo(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function Yo(e){return(e=(e||0)%360)<0?e+360:e}function Vo(e){return Math.max(0,Math.min(1,e||0))}function Fo(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}co(ho,Po,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Mo,formatHex:Mo,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Ho(this).formatHsl()},formatRgb:No,toString:No}),co(Do,Io,uo(ho,{brighter(e){return e=null==e?go:Math.pow(go,e),new Do(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?fo:Math.pow(fo,e),new Do(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Do($o(this.r),$o(this.g),$o(this.b),To(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Ro,formatHex:Ro,formatHex8:function(){return`#${Bo(this.r)}${Bo(this.g)}${Bo(this.b)}${Bo(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:zo,toString:zo})),co(Xo,(function(e,t,n,o){return 1===arguments.length?Ho(e):new Xo(e,t,n,null==o?1:o)}),uo(ho,{brighter(e){return e=null==e?go:Math.pow(go,e),new Xo(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?fo:Math.pow(fo,e),new Xo(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new Do(Fo(e>=240?e-240:e+120,r,o),Fo(e,r,o),Fo(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new Xo(Yo(this.h),Vo(this.s),Vo(this.l),To(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=To(this.opacity);return`${1===e?"hsl(":"hsla("}${Yo(this.h)}, ${100*Vo(this.s)}%, ${100*Vo(this.l)}%${1===e?")":`, ${e})`}`}}));var Ko=e=>()=>e;function Zo(e){return 1==(e=+e)?jo:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}(t,n,e):Ko(isNaN(t)?n:t)}}function jo(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):Ko(isNaN(e)?t:e)}var Wo=function e(t){var n=Zo(t);function o(e,t){var o=n((e=Io(e)).r,(t=Io(t)).r),r=n(e.g,t.g),i=n(e.b,t.b),a=jo(e.opacity,t.opacity);return function(t){return e.r=o(t),e.g=r(t),e.b=i(t),e.opacity=a(t),e+""}}return o.gamma=e,o}(1);function qo(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Uo=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Go=new RegExp(Uo.source,"g");function Qo(e,t){var n,o,r,i=Uo.lastIndex=Go.lastIndex=0,a=-1,s=[],l=[];for(e+="",t+="";(n=Uo.exec(e))&&(o=Go.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),s[a]?s[a]+=r:s[++a]=r),(n=n[0])===(o=o[0])?s[a]?s[a]+=o:s[++a]=o:(s[++a]=null,l.push({i:a,x:qo(n,o)})),i=Go.lastIndex;return i<t.length&&(r=t.slice(i),s[a]?s[a]+=r:s[++a]=r),s.length<2?l[0]?function(e){return function(t){return e(t)+""}}(l[0].x):function(e){return function(){return e}}(t):(t=l.length,function(e){for(var n,o=0;o<t;++o)s[(n=l[o]).i]=n.x(e);return s.join("")})}var Jo,er=180/Math.PI,tr={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function nr(e,t,n,o,r,i){var a,s,l;return(a=Math.sqrt(e*e+t*t))&&(e/=a,t/=a),(l=e*n+t*o)&&(n-=e*l,o-=t*l),(s=Math.sqrt(n*n+o*o))&&(n/=s,o/=s,l/=s),e*o<t*n&&(e=-e,t=-t,l=-l,a=-a),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*er,skewX:Math.atan(l)*er,scaleX:a,scaleY:s}}function or(e,t,n,o){function r(e){return e.length?e.pop()+" ":""}return function(i,a){var s=[],l=[];return i=e(i),a=e(a),function(e,o,r,i,a,s){if(e!==r||o!==i){var l=a.push("translate(",null,t,null,n);s.push({i:l-4,x:qo(e,r)},{i:l-2,x:qo(o,i)})}else(r||i)&&a.push("translate("+r+t+i+n)}(i.translateX,i.translateY,a.translateX,a.translateY,s,l),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:qo(e,t)})):t&&n.push(r(n)+"rotate("+t+o)}(i.rotate,a.rotate,s,l),function(e,t,n,i){e!==t?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:qo(e,t)}):t&&n.push(r(n)+"skewX("+t+o)}(i.skewX,a.skewX,s,l),function(e,t,n,o,i,a){if(e!==n||t!==o){var s=i.push(r(i)+"scale(",null,",",null,")");a.push({i:s-4,x:qo(e,n)},{i:s-2,x:qo(t,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,a.scaleX,a.scaleY,s,l),i=a=null,function(e){for(var t,n=-1,o=l.length;++n<o;)s[(t=l[n]).i]=t.x(e);return s.join("")}}}var rr=or((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?tr:nr(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),ir=or((function(e){return null==e?tr:(Jo||(Jo=document.createElementNS("http://www.w3.org/2000/svg","g")),Jo.setAttribute("transform",e),(e=Jo.transform.baseVal.consolidate())?nr((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):tr)}),", ",")",")");function ar(e){return((e=Math.exp(e))+1/e)/2}var sr,lr,cr=function e(t,n,o){function r(e,r){var i,a,s=e[0],l=e[1],c=e[2],u=r[0],d=r[1],h=r[2],f=u-s,g=d-l,p=f*f+g*g;if(p<1e-12)a=Math.log(h/c)/t,i=function(e){return[s+e*f,l+e*g,c*Math.exp(t*e*a)]};else{var m=Math.sqrt(p),y=(h*h-c*c+o*p)/(2*c*n*m),v=(h*h-c*c-o*p)/(2*h*n*m),b=Math.log(Math.sqrt(y*y+1)-y),S=Math.log(Math.sqrt(v*v+1)-v);a=(S-b)/t,i=function(e){var o,r=e*a,i=ar(b),u=c/(n*m)*(i*(o=t*r+b,((o=Math.exp(2*o))-1)/(o+1))-function(e){return((e=Math.exp(e))-1/e)/2}(b));return[s+u*f,l+u*g,c*i/ar(t*r+b)]}}return i.duration=1e3*a*t/Math.SQRT2,i}return r.rho=function(t){var n=Math.max(.001,+t),o=n*n;return e(n,o,o*o)},r}(Math.SQRT2,2,4),ur=0,dr=0,hr=0,fr=0,gr=0,pr=0,mr="object"==typeof performance&&performance.now?performance:Date,yr="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function vr(){return gr||(yr(br),gr=mr.now()+pr)}function br(){gr=0}function Sr(){this._call=this._time=this._next=null}function wr(e,t,n){var o=new Sr;return o.restart(e,t,n),o}function xr(){gr=(fr=mr.now())+pr,ur=dr=0;try{!function(){vr(),++ur;for(var e,t=sr;t;)(e=gr-t._time)>=0&&t._call.call(void 0,e),t=t._next;--ur}()}finally{ur=0,function(){var e,t,n=sr,o=1/0;for(;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:sr=t);lr=e,_r(o)}(),gr=0}}function Er(){var e=mr.now(),t=e-fr;t>1e3&&(pr-=t,fr=e)}function _r(e){ur||(dr&&(dr=clearTimeout(dr)),e-gr>24?(e<1/0&&(dr=setTimeout(xr,e-mr.now()-pr)),hr&&(hr=clearInterval(hr))):(hr||(fr=mr.now(),hr=setInterval(Er,1e3)),ur=1,yr(xr)))}function Cr(e,t,n){var o=new Sr;return t=null==t?0:+t,o.restart((n=>{o.stop(),e(n+t)}),t,n),o}Sr.prototype=wr.prototype={constructor:Sr,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?vr():+n)+(null==t?0:+t),this._next||lr===this||(lr?lr._next=this:sr=this,lr=this),this._call=e,this._time=n,_r()},stop:function(){this._call&&(this._call=null,this._time=1/0,_r())}};var Mr=kt("start","end","cancel","interrupt"),Nr=[];function Pr(e,t,n,o,r,i){var a=e.__transition;if(a){if(n in a)return}else e.__transition={};!function(e,t,n){var o,r=e.__transition;function i(e){n.state=1,n.timer.restart(a,n.delay,n.time),n.delay<=e&&a(e-n.delay)}function a(i){var c,u,d,h;if(1!==n.state)return l();for(c in r)if((h=r[c]).name===n.name){if(3===h.state)return Cr(a);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",e,e.__data__,h.index,h.group),delete r[c]):+c<t&&(h.state=6,h.timer.stop(),h.on.call("cancel",e,e.__data__,h.index,h.group),delete r[c])}if(Cr((function(){3===n.state&&(n.state=4,n.timer.restart(s,n.delay,n.time),s(i))})),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(n.state=3,o=new Array(d=n.tween.length),c=0,u=-1;c<d;++c)(h=n.tween[c].value.call(e,e.__data__,n.index,n.group))&&(o[++u]=h);o.length=u+1}}function s(t){for(var r=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(l),n.state=5,1),i=-1,a=o.length;++i<a;)o[i].call(e,r);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){for(var o in n.state=6,n.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=n,n.timer=wr(i,0,n.time)}(e,n,{name:t,index:o,group:r,on:Mr,tween:Nr,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:0})}function kr(e,t){var n=Or(e,t);if(n.state>0)throw new Error("too late; already scheduled");return n}function Ar(e,t){var n=Or(e,t);if(n.state>3)throw new Error("too late; already running");return n}function Or(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Ir(e,t){var n,o,r,i=e.__transition,a=!0;if(i){for(r in t=null==t?null:t+"",i)(n=i[r]).name===t?(o=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):a=!1;a&&delete e.__transition}}function Dr(e,t){var n,o;return function(){var r=Ar(this,e),i=r.tween;if(i!==n)for(var a=0,s=(o=n=i).length;a<s;++a)if(o[a].name===t){(o=o.slice()).splice(a,1);break}r.tween=o}}function Rr(e,t,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=Ar(this,e),a=i.tween;if(a!==o){r=(o=a).slice();for(var s={name:t,value:n},l=0,c=r.length;l<c;++l)if(r[l].name===t){r[l]=s;break}l===c&&r.push(s)}i.tween=r}}function zr(e,t,n){var o=e._id;return e.each((function(){var e=Ar(this,o);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return Or(e,o).value[t]}}function Tr(e,t){var n;return("number"==typeof t?qo:t instanceof Po?Wo:(n=Po(t))?(t=n,Wo):Qo)(e,t)}function $r(e){return function(){this.removeAttribute(e)}}function Br(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Lr(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttribute(e);return a===i?null:a===o?r:r=t(o=a,n)}}function Hr(e,t,n){var o,r,i=n+"";return function(){var a=this.getAttributeNS(e.space,e.local);return a===i?null:a===o?r:r=t(o=a,n)}}function Xr(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttribute(e))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttribute(e)}}function Yr(e,t,n){var o,r,i;return function(){var a,s,l=n(this);if(null!=l)return(a=this.getAttributeNS(e.space,e.local))===(s=l+"")?null:a===o&&s===r?i:(r=s,i=t(o=a,l));this.removeAttributeNS(e.space,e.local)}}function Vr(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function Fr(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function Kr(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&Fr(e,r)),n}return r._value=t,r}function Zr(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&Vr(e,r)),n}return r._value=t,r}function jr(e,t){return function(){kr(this,e).delay=+t.apply(this,arguments)}}function Wr(e,t){return t=+t,function(){kr(this,e).delay=t}}function qr(e,t){return function(){Ar(this,e).duration=+t.apply(this,arguments)}}function Ur(e,t){return t=+t,function(){Ar(this,e).duration=t}}function Gr(e,t){if("function"!=typeof t)throw new Error;return function(){Ar(this,e).ease=t}}function Qr(e,t,n){var o,r,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?kr:Ar;return function(){var a=i(this,e),s=a.on;s!==o&&(r=(o=s).copy()).on(t,n),a.on=r}}var Jr=Wn.prototype.constructor;function ei(e){return function(){this.style.removeProperty(e)}}function ti(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}function ni(e,t,n){var o,r;function i(){var i=t.apply(this,arguments);return i!==r&&(o=(r=i)&&ti(e,i,n)),o}return i._value=t,i}function oi(e){return function(t){this.textContent=e.call(this,t)}}function ri(e){var t,n;function o(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&oi(o)),t}return o._value=e,o}var ii=0;function ai(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function si(){return++ii}var li=Wn.prototype;ai.prototype={constructor:ai,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=Xt(e));for(var o=this._groups,r=o.length,i=new Array(r),a=0;a<r;++a)for(var s,l,c=o[a],u=c.length,d=i[a]=new Array(u),h=0;h<u;++h)(s=c[h])&&(l=e.call(s,s.__data__,h,c))&&("__data__"in s&&(l.__data__=s.__data__),d[h]=l,Pr(d[h],t,n,h,d,Or(s,n)));return new ai(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=Ft(e));for(var o=this._groups,r=o.length,i=[],a=[],s=0;s<r;++s)for(var l,c=o[s],u=c.length,d=0;d<u;++d)if(l=c[d]){for(var h,f=e.call(l,l.__data__,d,c),g=Or(l,n),p=0,m=f.length;p<m;++p)(h=f[p])&&Pr(h,t,n,p,f,g);i.push(f),a.push(l)}return new ai(i,a,t,n)},selectChild:li.selectChild,selectChildren:li.selectChildren,filter:function(e){"function"!=typeof e&&(e=Kt(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,a=t[r],s=a.length,l=o[r]=[],c=0;c<s;++c)(i=a[c])&&e.call(i,i.__data__,c,a)&&l.push(i);return new ai(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),a=new Array(o),s=0;s<i;++s)for(var l,c=t[s],u=n[s],d=c.length,h=a[s]=new Array(d),f=0;f<d;++f)(l=c[f]||u[f])&&(h[f]=l);for(;s<o;++s)a[s]=t[s];return new ai(a,this._parents,this._name,this._id)},selection:function(){return new Jr(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=si(),o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)if(a=s[c]){var u=Or(a,t);Pr(a,e,n,c,s,{time:u.time+u.delay+u.duration,delay:0,duration:u.duration,ease:u.ease})}return new ai(o,this._parents,e,n)},call:li.call,nodes:li.nodes,node:li.node,size:li.size,empty:li.empty,each:li.each,on:function(e,t){var n=this._id;return arguments.length<2?Or(this.node(),n).on.on(e):this.each(Qr(n,e,t))},attr:function(e,t){var n=Tt(e),o="transform"===n?ir:Tr;return this.attrTween(e,"function"==typeof t?(n.local?Yr:Xr)(n,o,zr(this,"attr."+e,t)):null==t?(n.local?Br:$r)(n):(n.local?Hr:Lr)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var o=Tt(e);return this.tween(n,(o.local?Kr:Zr)(o,t))},style:function(e,t,n){var o="transform"==(e+="")?rr:Tr;return null==t?this.styleTween(e,function(e,t){var n,o,r;return function(){var i=mn(this,e),a=(this.style.removeProperty(e),mn(this,e));return i===a?null:i===n&&a===o?r:r=t(n=i,o=a)}}(e,o)).on("end.style."+e,ei(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var o,r,i;return function(){var a=mn(this,e),s=n(this),l=s+"";return null==s&&(this.style.removeProperty(e),l=s=mn(this,e)),a===l?null:a===o&&l===r?i:(r=l,i=t(o=a,s))}}(e,o,zr(this,"style."+e,t))).each(function(e,t){var n,o,r,i,a="style."+t,s="end."+a;return function(){var l=Ar(this,e),c=l.on,u=null==l.value[a]?i||(i=ei(t)):void 0;c===n&&r===u||(o=(n=c).copy()).on(s,r=u),l.on=o}}(this._id,e)):this.styleTween(e,function(e,t,n){var o,r,i=n+"";return function(){var a=mn(this,e);return a===i?null:a===o?r:r=t(o=a,n)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==t)return this.tween(o,null);if("function"!=typeof t)throw new Error;return this.tween(o,ni(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(zr(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,ri(e))},remove:function(){return this.on("end.remove",function(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}(this._id))},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=Or(this.node(),n).tween,i=0,a=r.length;i<a;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((null==t?Dr:Rr)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?jr:Wr)(t,e)):Or(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?qr:Ur)(t,e)):Or(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(Gr(t,e)):Or(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;Ar(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise((function(i,a){var s={value:a},l={value:function(){0==--r&&i()}};n.each((function(){var n=Ar(this,o),r=n.on;r!==e&&((t=(e=r).copy())._.cancel.push(s),t._.interrupt.push(s),t._.end.push(l)),n.on=t})),0===r&&i()}))},[Symbol.iterator]:li[Symbol.iterator]};var ci={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function ui(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}Wn.prototype.interrupt=function(e){return this.each((function(){Ir(this,e)}))},Wn.prototype.transition=function(e){var t,n;e instanceof ai?(t=e._id,e=e._name):(t=si(),(n=ci).time=vr(),e=null==e?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var a,s=o[i],l=s.length,c=0;c<l;++c)(a=s[c])&&Pr(a,e,t,c,s,n||ui(a,t));return new ai(o,this._parents,e,t)};var di=e=>()=>e;function hi(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function fi(e,t,n){this.k=e,this.x=t,this.y=n}fi.prototype={constructor:fi,scale:function(e){return 1===e?this:new fi(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new fi(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var gi=new fi(1,0,0);function pi(e){e.stopImmediatePropagation()}function mi(e){e.preventDefault(),e.stopImmediatePropagation()}function yi(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function vi(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function bi(){return this.__zoom||gi}function Si(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function wi(){return navigator.maxTouchPoints||"ontouchstart"in this}function xi(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],a=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),a>i?(i+a)/2:Math.min(0,i)||Math.max(0,a))}function Ei(e,t,n,o){const r=e.parentNode||e.parentId;if(!r)return n;const i=t.get(r),a=$e(i,o);return Ei(i,t,{x:(n.x??0)+a.x,y:(n.y??0)+a.y,z:(i[W]?.z??0)>(n.z??0)?i[W]?.z??0:n.z??0},o)}function _i(e,t,n){e.forEach((o=>{const r=o.parentNode||o.parentId;if(r&&!e.has(r))throw new Error(`Parent node ${r} not found`);if(r||n?.[o.id]){const{x:r,y:i,z:a}=Ei(o,e,{...o.position,z:o[W]?.z??0},t);o.positionAbsolute={x:r,y:i},o[W].z=a,n?.[o.id]&&(o[W].isParent=!0)}}))}function Ci(e,t,n,o){const r=new Map,i={},a=o?1e3:0;return e.forEach((e=>{const n=(j(e.zIndex)?e.zIndex:0)+(e.selected?a:0),o=t.get(e.id),s={...e,positionAbsolute:{x:e.position.x,y:e.position.y}},l=e.parentNode||e.parentId;l&&(i[l]=!0);const c=o?.type&&o?.type!==e.type;Object.defineProperty(s,W,{enumerable:!1,value:{handleBounds:c?void 0:o?.[W]?.handleBounds,z:n}}),r.set(e.id,s)})),_i(r,n,i),r}function Mi(e,t={}){const{getNodes:n,width:o,height:r,minZoom:i,maxZoom:a,d3Zoom:s,d3Selection:l,fitViewOnInitDone:c,fitViewOnInit:u,nodeOrigin:d}=e(),h=t.initial&&!c&&u;if(s&&l&&(h||!t.initial)){const e=n().filter((e=>{const n=t.includeHiddenNodes?e.width&&e.height:!e.hidden;return t.nodes?.length?n&&t.nodes.some((t=>t.id===e.id)):n})),c=e.every((e=>e.width&&e.height));if(e.length>0&&c){const n=Be(e,d),{x:c,y:u,zoom:h}=Xe(n,o,r,t.minZoom??i,t.maxZoom??a,t.padding??.1),f=gi.translate(c,u).scale(h);return"number"==typeof t.duration&&t.duration>0?s.transform(Ye(l,t.duration),f):s.transform(l,f),!0}}return!1}function Ni(e,t){return e.forEach((e=>{const n=t.get(e.id);n&&t.set(n.id,{...n,[W]:n[W],selected:e.selected})})),new Map(t)}function Pi(e,t){return t.map((t=>{const n=e.find((e=>e.id===t.id));return n&&(t.selected=n.selected),t}))}function ki({changedNodes:e,changedEdges:t,get:n,set:o}){const{nodeInternals:r,edges:i,onNodesChange:a,onEdgesChange:s,hasDefaultNodes:l,hasDefaultEdges:c}=n();e?.length&&(l&&o({nodeInternals:Ni(e,r)}),a?.(e)),t?.length&&(c&&o({edges:Pi(t,i)}),s?.(t))}fi.prototype;const Ai=()=>{},Oi={zoomIn:Ai,zoomOut:Ai,zoomTo:Ai,getZoom:()=>1,setViewport:Ai,getViewport:()=>({x:0,y:0,zoom:1}),fitView:()=>!1,setCenter:Ai,fitBounds:Ai,project:e=>e,screenToFlowPosition:e=>e,flowToScreenPosition:e=>e,viewportInitialized:!1},Ii=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection});function Di(){const e=(()=>{const e=O(),{d3Zoom:n,d3Selection:o}=A(Ii,Ce),r=t.useMemo((()=>o&&n?{zoomIn:e=>n.scaleBy(Ye(o,e?.duration),1.2),zoomOut:e=>n.scaleBy(Ye(o,e?.duration),1/1.2),zoomTo:(e,t)=>n.scaleTo(Ye(o,t?.duration),e),getZoom:()=>e.getState().transform[2],setViewport:(t,r)=>{const[i,a,s]=e.getState().transform,l=gi.translate(t.x??i,t.y??a).scale(t.zoom??s);n.transform(Ye(o,r?.duration),l)},getViewport:()=>{const[t,n,o]=e.getState().transform;return{x:t,y:n,zoom:o}},fitView:t=>Mi(e.getState,t),setCenter:(t,r,i)=>{const{width:a,height:s,maxZoom:l}=e.getState(),c=void 0!==i?.zoom?i.zoom:l,u=a/2-t*c,d=s/2-r*c,h=gi.translate(u,d).scale(c);n.transform(Ye(o,i?.duration),h)},fitBounds:(t,r)=>{const{width:i,height:a,minZoom:s,maxZoom:l}=e.getState(),{x:c,y:u,zoom:d}=Xe(t,i,a,s,l,r?.padding??.1),h=gi.translate(c,u).scale(d);n.transform(Ye(o,r?.duration),h)},project:t=>{const{transform:n,snapToGrid:o,snapGrid:r}=e.getState();return console.warn("[DEPRECATED] `project` is deprecated. Instead use `screenToFlowPosition`. There is no need to subtract the react flow bounds anymore! https://reactflow.dev/api-reference/types/react-flow-instance#screen-to-flow-position"),ze(t,n,o,r)},screenToFlowPosition:t=>{const{transform:n,snapToGrid:o,snapGrid:r,domNode:i}=e.getState();if(!i)return t;const{x:a,y:s}=i.getBoundingClientRect(),l={x:t.x-a,y:t.y-s};return ze(l,n,o,r)},flowToScreenPosition:t=>{const{transform:n,domNode:o}=e.getState();if(!o)return t;const{x:r,y:i}=o.getBoundingClientRect(),a=Te(t,n);return{x:a.x+r,y:a.y+i}},viewportInitialized:!0}:Oi),[n,o]);return r})(),n=O(),o=t.useCallback((()=>n.getState().getNodes().map((e=>({...e})))),[]),r=t.useCallback((e=>n.getState().nodeInternals.get(e)),[]),i=t.useCallback((()=>{const{edges:e=[]}=n.getState();return e.map((e=>({...e})))}),[]),a=t.useCallback((e=>{const{edges:t=[]}=n.getState();return t.find((t=>t.id===e))}),[]),s=t.useCallback((e=>{const{getNodes:t,setNodes:o,hasDefaultNodes:r,onNodesChange:i}=n.getState(),a=t(),s="function"==typeof e?e(a):e;if(r)o(s);else if(i){i(0===s.length?a.map((e=>({type:"remove",id:e.id}))):s.map((e=>({item:e,type:"reset"}))))}}),[]),l=t.useCallback((e=>{const{edges:t=[],setEdges:o,hasDefaultEdges:r,onEdgesChange:i}=n.getState(),a="function"==typeof e?e(t):e;if(r)o(a);else if(i){i(0===a.length?t.map((e=>({type:"remove",id:e.id}))):a.map((e=>({item:e,type:"reset"}))))}}),[]),c=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{getNodes:o,setNodes:r,hasDefaultNodes:i,onNodesChange:a}=n.getState();if(i){r([...o(),...t])}else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),u=t.useCallback((e=>{const t=Array.isArray(e)?e:[e],{edges:o=[],setEdges:r,hasDefaultEdges:i,onEdgesChange:a}=n.getState();if(i)r([...o,...t]);else if(a){a(t.map((e=>({item:e,type:"add"}))))}}),[]),d=t.useCallback((()=>{const{getNodes:e,edges:t=[],transform:o}=n.getState(),[r,i,a]=o;return{nodes:e().map((e=>({...e}))),edges:t.map((e=>({...e}))),viewport:{x:r,y:i,zoom:a}}}),[]),h=t.useCallback((({nodes:e,edges:t})=>{const{nodeInternals:o,getNodes:r,edges:i,hasDefaultNodes:a,hasDefaultEdges:s,onNodesDelete:l,onEdgesDelete:c,onNodesChange:u,onEdgesChange:d}=n.getState(),h=(e||[]).map((e=>e.id)),f=(t||[]).map((e=>e.id)),g=r().reduce(((e,t)=>{const n=t.parentNode||t.parentId,o=!h.includes(t.id)&&n&&e.find((e=>e.id===n));return("boolean"!=typeof t.deletable||t.deletable)&&(h.includes(t.id)||o)&&e.push(t),e}),[]),p=i.filter((e=>"boolean"!=typeof e.deletable||e.deletable)),m=p.filter((e=>f.includes(e.id)));if(g||m){const e=He(g,p),t=[...m,...e],r=t.reduce(((e,t)=>(e.includes(t.id)||e.push(t.id),e)),[]);if((s||a)&&(s&&n.setState({edges:i.filter((e=>!r.includes(e.id)))}),a&&(g.forEach((e=>{o.delete(e.id)})),n.setState({nodeInternals:new Map(o)}))),r.length>0&&(c?.(t),d&&d(r.map((e=>({id:e,type:"remove"}))))),g.length>0&&(l?.(g),u)){u(g.map((e=>({id:e.id,type:"remove"}))))}}}),[]),f=t.useCallback((e=>{const t=j((o=e).width)&&j(o.height)&&j(o.x)&&j(o.y);var o;const r=t?null:n.getState().nodeInternals.get(e.id);if(!t&&!r)return[null,null,t];return[t?e:K(r),r,t]}),[]),g=t.useCallback(((e,t=!0,o)=>{const[r,i,a]=f(e);return r?(o||n.getState().getNodes()).filter((e=>{if(!(a||e.id!==i.id&&e.positionAbsolute))return!1;const n=K(e),o=Z(n,r);return t&&o>0||o>=r.width*r.height})):[]}),[]),p=t.useCallback(((e,t,n=!0)=>{const[o]=f(e);if(!o)return!1;const r=Z(o,t);return n&&r>0||r>=o.width*o.height}),[]);return t.useMemo((()=>({...e,getNodes:o,getNode:r,getEdges:i,getEdge:a,setNodes:s,setEdges:l,addNodes:c,addEdges:u,toObject:d,deleteElements:h,getIntersectingNodes:g,isNodeIntersecting:p})),[e,o,r,i,a,s,l,c,u,d,h,g,p])}const Ri={actInsideInputWithModifier:!1};const zi={position:"absolute",width:"100%",height:"100%",top:0,left:0},Ti=e=>({x:e.x,y:e.y,zoom:e.k}),$i=(e,t)=>e.target.closest(`.${t}`),Bi=(e,t)=>2===t&&Array.isArray(e)&&e.includes(2),Li=e=>{const t=e.ctrlKey&&J()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t},Hi=e=>({d3Zoom:e.d3Zoom,d3Selection:e.d3Selection,d3ZoomHandler:e.d3ZoomHandler,userSelectionActive:e.userSelectionActive}),Xi=({onMove:n,onMoveStart:o,onMoveEnd:r,onPaneContextMenu:i,zoomOnScroll:a=!0,zoomOnPinch:s=!0,panOnScroll:l=!1,panOnScrollSpeed:c=.5,panOnScrollMode:u=e.PanOnScrollMode.Free,zoomOnDoubleClick:d=!0,elementsSelectable:h,panOnDrag:f=!0,defaultViewport:g,translateExtent:p,minZoom:m,maxZoom:y,zoomActivationKeyCode:v,preventScrolling:b=!0,children:S,noWheelClassName:w,noPanClassName:E})=>{const _=t.useRef(),C=O(),M=t.useRef(!1),N=t.useRef(!1),P=t.useRef(null),k=t.useRef({x:0,y:0,zoom:0}),{d3Zoom:I,d3Selection:D,d3ZoomHandler:R,userSelectionActive:z}=A(Hi,Ce),B=Ct(v),L=t.useRef(0),H=t.useRef(!1),X=t.useRef();return function(e){const n=O();t.useEffect((()=>{let t;const o=()=>{if(!e.current)return;const t=T(e.current);0!==t.height&&0!==t.width||n.getState().onError?.("004",x()),n.setState({width:t.width||500,height:t.height||500})};return o(),window.addEventListener("resize",o),e.current&&(t=new ResizeObserver((()=>o())),t.observe(e.current)),()=>{window.removeEventListener("resize",o),t&&e.current&&t.unobserve(e.current)}}),[])}(P),t.useEffect((()=>{if(P.current){const e=P.current.getBoundingClientRect(),t=function(){var e,t,n,o=yi,r=vi,i=xi,a=Si,s=wi,l=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],u=250,d=cr,h=kt("start","zoom","end"),f=500,g=0,p=10;function m(e){e.property("__zoom",bi).on("wheel.zoom",E,{passive:!1}).on("mousedown.zoom",_).on("dblclick.zoom",C).filter(s).on("touchstart.zoom",M).on("touchmove.zoom",N).on("touchend.zoom touchcancel.zoom",P).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(e,t){return(t=Math.max(l[0],Math.min(l[1],t)))===e.k?e:new fi(t,e.x,e.y)}function v(e,t,n){var o=t[0]-n[0]*e.k,r=t[1]-n[1]*e.k;return o===e.x&&r===e.y?e:new fi(e.k,o,r)}function b(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function S(e,t,n,o){e.on("start.zoom",(function(){w(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){w(this,arguments).event(o).end()})).tween("zoom",(function(){var e=this,i=arguments,a=w(e,i).event(o),s=r.apply(e,i),l=null==n?b(s):"function"==typeof n?n.apply(e,i):n,c=Math.max(s[1][0]-s[0][0],s[1][1]-s[0][1]),u=e.__zoom,h="function"==typeof t?t.apply(e,i):t,f=d(u.invert(l).concat(c/u.k),h.invert(l).concat(c/h.k));return function(e){if(1===e)e=h;else{var t=f(e),n=c/t[2];e=new fi(n,l[0]-t[0]*n,l[1]-t[1]*n)}a.zoom(null,e)}}))}function w(e,t,n){return!n&&e.__zooming||new x(e,t)}function x(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=r.apply(e,t),this.taps=0}function E(e,...t){if(o.apply(this,arguments)){var n=w(this,t).event(e),r=this.__zoom,s=Math.max(l[0],Math.min(l[1],r.k*Math.pow(2,a.apply(this,arguments)))),u=Un(e);if(n.wheel)n.mouse[0][0]===u[0]&&n.mouse[0][1]===u[1]||(n.mouse[1]=r.invert(n.mouse[0]=u)),clearTimeout(n.wheel);else{if(r.k===s)return;n.mouse=[u,r.invert(u)],Ir(this),n.start()}mi(e),n.wheel=setTimeout(d,150),n.zoom("mouse",i(v(y(r,s),n.mouse[0],n.mouse[1]),n.extent,c))}function d(){n.wheel=null,n.end()}}function _(e,...t){if(!n&&o.apply(this,arguments)){var r=e.currentTarget,a=w(this,t,!0).event(e),s=qn(e.view).on("mousemove.zoom",h,!0).on("mouseup.zoom",f,!0),l=Un(e,r),u=e.clientX,d=e.clientY;to(e.view),pi(e),a.mouse=[l,this.__zoom.invert(l)],Ir(this),a.start()}function h(e){if(mi(e),!a.moved){var t=e.clientX-u,n=e.clientY-d;a.moved=t*t+n*n>g}a.event(e).zoom("mouse",i(v(a.that.__zoom,a.mouse[0]=Un(e,r),a.mouse[1]),a.extent,c))}function f(e){s.on("mousemove.zoom mouseup.zoom",null),no(e.view,a.moved),mi(e),a.event(e).end()}}function C(e,...t){if(o.apply(this,arguments)){var n=this.__zoom,a=Un(e.changedTouches?e.changedTouches[0]:e,this),s=n.invert(a),l=n.k*(e.shiftKey?.5:2),d=i(v(y(n,l),a,s),r.apply(this,t),c);mi(e),u>0?qn(this).transition().duration(u).call(S,d,a,e):qn(this).call(m.transform,d,a,e)}}function M(n,...r){if(o.apply(this,arguments)){var i,a,s,l,c=n.touches,u=c.length,d=w(this,r,n.changedTouches.length===u).event(n);for(pi(n),a=0;a<u;++a)l=[l=Un(s=c[a],this),this.__zoom.invert(l),s.identifier],d.touch0?d.touch1||d.touch0[2]===l[2]||(d.touch1=l,d.taps=0):(d.touch0=l,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=l[0],e=setTimeout((function(){e=null}),f)),Ir(this),d.start())}}function N(e,...t){if(this.__zooming){var n,o,r,a,s=w(this,t).event(e),l=e.changedTouches,u=l.length;for(mi(e),n=0;n<u;++n)r=Un(o=l[n],this),s.touch0&&s.touch0[2]===o.identifier?s.touch0[0]=r:s.touch1&&s.touch1[2]===o.identifier&&(s.touch1[0]=r);if(o=s.that.__zoom,s.touch1){var d=s.touch0[0],h=s.touch0[1],f=s.touch1[0],g=s.touch1[1],p=(p=f[0]-d[0])*p+(p=f[1]-d[1])*p,m=(m=g[0]-h[0])*m+(m=g[1]-h[1])*m;o=y(o,Math.sqrt(p/m)),r=[(d[0]+f[0])/2,(d[1]+f[1])/2],a=[(h[0]+g[0])/2,(h[1]+g[1])/2]}else{if(!s.touch0)return;r=s.touch0[0],a=s.touch0[1]}s.zoom("touch",i(v(o,r,a),s.extent,c))}}function P(e,...o){if(this.__zooming){var r,i,a=w(this,o).event(e),s=e.changedTouches,l=s.length;for(pi(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),f),r=0;r<l;++r)i=s[r],a.touch0&&a.touch0[2]===i.identifier?delete a.touch0:a.touch1&&a.touch1[2]===i.identifier&&delete a.touch1;if(a.touch1&&!a.touch0&&(a.touch0=a.touch1,delete a.touch1),a.touch0)a.touch0[1]=this.__zoom.invert(a.touch0[0]);else if(a.end(),2===a.taps&&(i=Un(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<p)){var c=qn(this).on("dblclick.zoom");c&&c.apply(this,arguments)}}}return m.transform=function(e,t,n,o){var r=e.selection?e.selection():e;r.property("__zoom",bi),e!==r?S(e,t,n,o):r.interrupt().each((function(){w(this,arguments).event(o).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},m.scaleBy=function(e,t,n,o){m.scaleTo(e,(function(){return this.__zoom.k*("function"==typeof t?t.apply(this,arguments):t)}),n,o)},m.scaleTo=function(e,t,n,o){m.transform(e,(function(){var e=r.apply(this,arguments),o=this.__zoom,a=null==n?b(e):"function"==typeof n?n.apply(this,arguments):n,s=o.invert(a),l="function"==typeof t?t.apply(this,arguments):t;return i(v(y(o,l),a,s),e,c)}),n,o)},m.translateBy=function(e,t,n,o){m.transform(e,(function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),c)}),null,o)},m.translateTo=function(e,t,n,o,a){m.transform(e,(function(){var e=r.apply(this,arguments),a=this.__zoom,s=null==o?b(e):"function"==typeof o?o.apply(this,arguments):o;return i(gi.translate(s[0],s[1]).scale(a.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,c)}),o,a)},x.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=qn(this.that).datum();h.call(e,this.that,new hi(e,{sourceEvent:this.sourceEvent,target:m,type:e,transform:this.that.__zoom,dispatch:h}),t)}},m.wheelDelta=function(e){return arguments.length?(a="function"==typeof e?e:di(+e),m):a},m.filter=function(e){return arguments.length?(o="function"==typeof e?e:di(!!e),m):o},m.touchable=function(e){return arguments.length?(s="function"==typeof e?e:di(!!e),m):s},m.extent=function(e){return arguments.length?(r="function"==typeof e?e:di([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),m):r},m.scaleExtent=function(e){return arguments.length?(l[0]=+e[0],l[1]=+e[1],m):[l[0],l[1]]},m.translateExtent=function(e){return arguments.length?(c[0][0]=+e[0][0],c[1][0]=+e[1][0],c[0][1]=+e[0][1],c[1][1]=+e[1][1],m):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},m.constrain=function(e){return arguments.length?(i=e,m):i},m.duration=function(e){return arguments.length?(u=+e,m):u},m.interpolate=function(e){return arguments.length?(d=e,m):d},m.on=function(){var e=h.on.apply(h,arguments);return e===h?m:e},m.clickDistance=function(e){return arguments.length?(g=(e=+e)*e,m):Math.sqrt(g)},m.tapDistance=function(e){return arguments.length?(p=+e,m):p},m}().scaleExtent([m,y]).translateExtent(p),n=qn(P.current).call(t),o=gi.translate(g.x,g.y).scale($(g.zoom,m,y)),r=[[0,0],[e.width,e.height]],i=t.constrain()(o,r,p);t.transform(n,i),t.wheelDelta(Li),C.setState({d3Zoom:t,d3Selection:n,d3ZoomHandler:n.on("wheel.zoom"),transform:[i.x,i.y,i.k],domNode:P.current.closest(".react-flow")})}}),[]),t.useEffect((()=>{D&&I&&(!l||B||z?void 0!==R&&D.on("wheel.zoom",(function(e,t){if(!b&&"wheel"===e.type&&!e.ctrlKey||$i(e,w))return null;e.preventDefault(),R.call(this,e,t)}),{passive:!1}):D.on("wheel.zoom",(t=>{if($i(t,w))return!1;t.preventDefault(),t.stopImmediatePropagation();const i=D.property("__zoom").k||1;if(t.ctrlKey&&s){const e=Un(t),n=Li(t),o=i*Math.pow(2,n);return void I.scaleTo(D,o,e,t)}const a=1===t.deltaMode?20:1;let l=u===e.PanOnScrollMode.Vertical?0:t.deltaX*a,d=u===e.PanOnScrollMode.Horizontal?0:t.deltaY*a;!J()&&t.shiftKey&&u!==e.PanOnScrollMode.Vertical&&(l=t.deltaY*a,d=0),I.translateBy(D,-l/i*c,-d/i*c,{internal:!0});const h=Ti(D.property("__zoom")),{onViewportChangeStart:f,onViewportChange:g,onViewportChangeEnd:p}=C.getState();clearTimeout(X.current),H.current||(H.current=!0,o?.(t,h),f?.(h)),H.current&&(n?.(t,h),g?.(h),X.current=setTimeout((()=>{r?.(t,h),p?.(h),H.current=!1}),150))}),{passive:!1}))}),[z,l,u,D,I,R,B,s,b,w,o,n,r]),t.useEffect((()=>{I&&I.on("start",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;L.current=e.sourceEvent?.button;const{onViewportChangeStart:t}=C.getState(),n=Ti(e.transform);M.current=!0,k.current=n,"mousedown"===e.sourceEvent?.type&&C.setState({paneDragging:!0}),t?.(n),o?.(e.sourceEvent,n)}))}),[I,o]),t.useEffect((()=>{I&&(z&&!M.current?I.on("zoom",null):z||I.on("zoom",(e=>{const{onViewportChange:t}=C.getState();if(C.setState({transform:[e.transform.x,e.transform.y,e.transform.k]}),N.current=!(!i||!Bi(f,L.current??0)),(n||t)&&!e.sourceEvent?.internal){const o=Ti(e.transform);t?.(o),n?.(e.sourceEvent,o)}})))}),[z,I,n,f,i]),t.useEffect((()=>{I&&I.on("end",(e=>{if(!e.sourceEvent||e.sourceEvent.internal)return null;const{onViewportChangeEnd:t}=C.getState();if(M.current=!1,C.setState({paneDragging:!1}),i&&Bi(f,L.current??0)&&!N.current&&i(e.sourceEvent),N.current=!1,(r||t)&&(n=k.current,o=e.transform,n.x!==o.x||n.y!==o.y||n.zoom!==o.k)){const n=Ti(e.transform);k.current=n,clearTimeout(_.current),_.current=setTimeout((()=>{t?.(n),r?.(e.sourceEvent,n)}),l?150:0)}var n,o}))}),[I,l,f,r,i]),t.useEffect((()=>{I&&I.filter((e=>{const t=B||a,n=s&&e.ctrlKey;if((!0===f||Array.isArray(f)&&f.includes(1))&&1===e.button&&"mousedown"===e.type&&($i(e,"react-flow__node")||$i(e,"react-flow__edge")))return!0;if(!(f||t||l||d||s))return!1;if(z)return!1;if(!d&&"dblclick"===e.type)return!1;if($i(e,w)&&"wheel"===e.type)return!1;if($i(e,E)&&("wheel"!==e.type||l&&"wheel"===e.type&&!B))return!1;if(!s&&e.ctrlKey&&"wheel"===e.type)return!1;if(!t&&!l&&!n&&"wheel"===e.type)return!1;if(!f&&("mousedown"===e.type||"touchstart"===e.type))return!1;if(Array.isArray(f)&&!f.includes(e.button)&&"mousedown"===e.type)return!1;const o=Array.isArray(f)&&f.includes(e.button)||!e.button||e.button<=1;return(!e.ctrlKey||"wheel"===e.type)&&o}))}),[z,I,a,s,l,d,f,h,B]),t.createElement("div",{className:"react-flow__renderer",ref:P,style:zi},S)},Yi=e=>({userSelectionActive:e.userSelectionActive,userSelectionRect:e.userSelectionRect});function Vi(){const{userSelectionActive:e,userSelectionRect:n}=A(Yi,Ce);return e&&n?t.createElement("div",{className:"react-flow__selection react-flow__container",style:{width:n.width,height:n.height,transform:`translate(${n.x}px, ${n.y}px)`}}):null}function Fi(e,t){const n=t.parentNode||t.parentId,o=e.find((e=>e.id===n));if(o){const e=t.position.x+t.width-o.width,n=t.position.y+t.height-o.height;if(e>0||n>0||t.position.x<0||t.position.y<0){if(o.style={...o.style}||{},o.style.width=o.style.width??o.width,o.style.height=o.style.height??o.height,e>0&&(o.style.width+=e),n>0&&(o.style.height+=n),t.position.x<0){const e=Math.abs(t.position.x);o.position.x=o.position.x-e,o.style.width+=e,t.position.x=0}if(t.position.y<0){const e=Math.abs(t.position.y);o.position.y=o.position.y-e,o.style.height+=e,t.position.y=0}o.width=o.style.width,o.height=o.style.height}}}function Ki(e,t){if(e.some((e=>"reset"===e.type)))return e.filter((e=>"reset"===e.type)).map((e=>e.item));const n=e.filter((e=>"add"===e.type)).map((e=>e.item));return t.reduce(((t,n)=>{const o=e.filter((e=>e.id===n.id));if(0===o.length)return t.push(n),t;const r={...n};for(const e of o)if(e)switch(e.type){case"select":r.selected=e.selected;break;case"position":void 0!==e.position&&(r.position=e.position),void 0!==e.positionAbsolute&&(r.positionAbsolute=e.positionAbsolute),void 0!==e.dragging&&(r.dragging=e.dragging),r.expandParent&&Fi(t,r);break;case"dimensions":void 0!==e.dimensions&&(r.width=e.dimensions.width,r.height=e.dimensions.height),void 0!==e.updateStyle&&(r.style={...r.style||{},...e.dimensions}),"boolean"==typeof e.resizing&&(r.resizing=e.resizing),r.expandParent&&Fi(t,r);break;case"remove":return t}return t.push(r),t}),n)}function Zi(e,t){return Ki(e,t)}function ji(e,t){return Ki(e,t)}const Wi=(e,t)=>({id:e,type:"select",selected:t});function qi(e,t){return e.reduce(((e,n)=>{const o=t.includes(n.id);return!n.selected&&o?(n.selected=!0,e.push(Wi(n.id,!0))):n.selected&&!o&&(n.selected=!1,e.push(Wi(n.id,!1))),e}),[])}const Ui=(e,t)=>n=>{n.target===t.current&&e?.(n)},Gi=e=>({userSelectionActive:e.userSelectionActive,elementsSelectable:e.elementsSelectable,dragging:e.paneDragging}),Qi=t.memo((({isSelecting:n,selectionMode:r=e.SelectionMode.Full,panOnDrag:i,onSelectionStart:a,onSelectionEnd:s,onPaneClick:l,onPaneContextMenu:c,onPaneScroll:u,onPaneMouseEnter:d,onPaneMouseMove:h,onPaneMouseLeave:f,children:g})=>{const p=t.useRef(null),m=O(),y=t.useRef(0),v=t.useRef(0),b=t.useRef(),{userSelectionActive:S,elementsSelectable:w,dragging:x}=A(Gi,Ce),E=()=>{m.setState({userSelectionActive:!1,userSelectionRect:null}),y.current=0,v.current=0},_=e=>{l?.(e),m.getState().resetSelectedElements(),m.setState({nodesSelectionActive:!1})},C=u?e=>u(e):void 0,M=w&&(n||S);return t.createElement("div",{className:o(["react-flow__pane",{dragging:x,selection:n}]),onClick:M?void 0:Ui(_,p),onContextMenu:Ui((e=>{Array.isArray(i)&&i?.includes(2)?e.preventDefault():c?.(e)}),p),onWheel:Ui(C,p),onMouseEnter:M?void 0:d,onMouseDown:M?e=>{const{resetSelectedElements:t,domNode:o}=m.getState();if(b.current=o?.getBoundingClientRect(),!w||!n||0!==e.button||e.target!==p.current||!b.current)return;const{x:r,y:i}=Q(e,b.current);t(),m.setState({userSelectionRect:{width:0,height:0,startX:r,startY:i,x:r,y:i}}),a?.(e)}:void 0,onMouseMove:M?t=>{const{userSelectionRect:o,nodeInternals:i,edges:a,transform:s,onNodesChange:l,onEdgesChange:c,nodeOrigin:u,getNodes:d}=m.getState();if(!n||!b.current||!o)return;m.setState({userSelectionActive:!0,nodesSelectionActive:!1});const h=Q(t,b.current),f=o.startX??0,g=o.startY??0,p={...o,x:h.x<f?h.x:f,y:h.y<g?h.y:g,width:Math.abs(h.x-f),height:Math.abs(h.y-g)},S=d(),w=Le(i,p,s,r===e.SelectionMode.Partial,!0,u),x=He(w,a).map((e=>e.id)),E=w.map((e=>e.id));if(y.current!==E.length){y.current=E.length;const e=qi(S,E);e.length&&l?.(e)}if(v.current!==x.length){v.current=x.length;const e=qi(a,x);e.length&&c?.(e)}m.setState({userSelectionRect:p})}:h,onMouseUp:M?e=>{if(0!==e.button)return;const{userSelectionRect:t}=m.getState();!S&&t&&e.target===p.current&&_?.(e),m.setState({nodesSelectionActive:y.current>0}),E(),s?.(e)}:void 0,onMouseLeave:M?e=>{S&&(m.setState({nodesSelectionActive:y.current>0}),s?.(e)),E()}:f,ref:p,style:zi},g,t.createElement(Vi,null))}));function Ji(e,t){const n=e.parentNode||e.parentId;if(!n)return!1;const o=t.get(n);return!!o&&(!!o.selected||Ji(o,t))}function ea(e,t,n){let o=e;do{if(o?.matches(t))return!0;if(o===n.current)return!1;o=o.parentElement}while(o);return!1}function ta(e,t,n,o){return Array.from(e.values()).filter((n=>(n.selected||n.id===o)&&(!n.parentNode||n.parentId||!Ji(n,e))&&(n.draggable||t&&void 0===n.draggable))).map((e=>({id:e.id,position:e.position||{x:0,y:0},positionAbsolute:e.positionAbsolute||{x:0,y:0},distance:{x:n.x-(e.positionAbsolute?.x??0),y:n.y-(e.positionAbsolute?.y??0)},delta:{x:0,y:0},extent:e.extent,parentNode:e.parentNode||e.parentId,parentId:e.parentNode||e.parentId,width:e.width,height:e.height,expandParent:e.expandParent})))}function na(e,t,n,o,r=[0,0],i){const a=function(e,t){return t&&"parent"!==t?[t[0],[t[1][0]-(e.width||0),t[1][1]-(e.height||0)]]:t}(e,e.extent||o);let s=a;const l=e.parentNode||e.parentId;if("parent"!==e.extent||e.expandParent){if(e.extent&&l&&"parent"!==e.extent){const t=n.get(l),{x:o,y:i}=$e(t,r).positionAbsolute;s=[[e.extent[0][0]+o,e.extent[0][1]+i],[e.extent[1][0]+o,e.extent[1][1]+i]]}}else if(l&&e.width&&e.height){const t=n.get(l),{x:o,y:i}=$e(t,r).positionAbsolute;s=t&&j(o)&&j(i)&&j(t.width)&&j(t.height)?[[o+e.width*r[0],i+e.height*r[1]],[o+t.width-e.width+e.width*r[0],i+t.height-e.height+e.height*r[1]]]:s}else i?.("005",E()),s=a;let c={x:0,y:0};if(l){const e=n.get(l);c=$e(e,r).positionAbsolute}const u=s&&"parent"!==s?B(t,s):t;return{position:{x:u.x-c.x,y:u.y-c.y},positionAbsolute:u}}function oa({nodeId:e,dragItems:t,nodeInternals:n}){const o=t.map((e=>({...n.get(e.id),position:e.position,positionAbsolute:e.positionAbsolute})));return[e?o.find((t=>t.id===e)):o[0],o]}Qi.displayName="Pane";const ra=(e,t,n,o)=>{const r=t.querySelectorAll(e);if(!r||!r.length)return null;const i=Array.from(r),a=t.getBoundingClientRect(),s=a.width*o[0],l=a.height*o[1];return i.map((e=>{const t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-a.left-s)/n,y:(t.top-a.top-l)/n,...T(e)}}))};function ia(e,t,n){return void 0===n?n:o=>{const r=t().nodeInternals.get(e);r&&n(o,{...r})}}function aa({id:e,store:t,unselect:n=!1,nodeRef:o}){const{addSelectedNodes:r,unselectNodesAndEdges:i,multiSelectionActive:a,nodeInternals:s,onError:l}=t.getState(),c=s.get(e);c?(t.setState({nodesSelectionActive:!1}),c.selected?(n||c.selected&&a)&&(i({nodes:[c],edges:[]}),requestAnimationFrame((()=>o?.current?.blur()))):r([e])):l?.("012",P(e))}function sa(){const e=O(),n=t.useCallback((({sourceEvent:t})=>{const{transform:n,snapGrid:o,snapToGrid:r}=e.getState(),i=t.touches?t.touches[0].clientX:t.clientX,a=t.touches?t.touches[0].clientY:t.clientY,s={x:(i-n[0])/n[2],y:(a-n[1])/n[2]};return{xSnapped:r?o[0]*Math.round(s.x/o[0]):s.x,ySnapped:r?o[1]*Math.round(s.y/o[1]):s.y,...s}}),[]);return n}function la(e){return(t,n,o)=>e?.(t,o)}function ca({nodeRef:e,disabled:n=!1,noDragClassName:o,handleSelector:r,nodeId:i,isSelectable:a,selectNodesOnDrag:s}){const l=O(),[c,u]=t.useState(!1),d=t.useRef([]),h=t.useRef({x:null,y:null}),f=t.useRef(0),g=t.useRef(null),p=t.useRef({x:0,y:0}),m=t.useRef(null),y=t.useRef(!1),v=t.useRef(!1),b=t.useRef(!1),S=sa();return t.useEffect((()=>{if(e?.current){const t=qn(e.current),c=({x:e,y:t})=>{const{nodeInternals:n,onNodeDrag:o,onSelectionDrag:r,updateNodePositions:a,nodeExtent:s,snapGrid:c,snapToGrid:f,nodeOrigin:g,onError:p}=l.getState();h.current={x:e,y:t};let y=!1,v={x:0,y:0,x2:0,y2:0};if(d.current.length>1&&s){const e=Be(d.current,g);v=V(e)}if(d.current=d.current.map((o=>{const r={x:e-o.distance.x,y:t-o.distance.y};f&&(r.x=c[0]*Math.round(r.x/c[0]),r.y=c[1]*Math.round(r.y/c[1]));const i=[[s[0][0],s[0][1]],[s[1][0],s[1][1]]];d.current.length>1&&s&&!o.extent&&(i[0][0]=o.positionAbsolute.x-v.x+s[0][0],i[1][0]=o.positionAbsolute.x+(o.width??0)-v.x2+s[1][0],i[0][1]=o.positionAbsolute.y-v.y+s[0][1],i[1][1]=o.positionAbsolute.y+(o.height??0)-v.y2+s[1][1]);const a=na(o,r,n,i,g,p);return y=y||o.position.x!==a.position.x||o.position.y!==a.position.y,o.position=a.position,o.positionAbsolute=a.positionAbsolute,o})),!y)return;a(d.current,!0,!0),u(!0);const b=i?o:la(r);if(b&&m.current){const[e,t]=oa({nodeId:i,dragItems:d.current,nodeInternals:n});b(m.current,e,t)}},w=()=>{if(!g.current)return;const[e,t]=H(p.current,g.current);if(0!==e||0!==t){const{transform:n,panBy:o}=l.getState();h.current.x=(h.current.x??0)-e/n[2],h.current.y=(h.current.y??0)-t/n[2],o({x:e,y:t})&&c(h.current)}f.current=requestAnimationFrame(w)},x=t=>{const{nodeInternals:n,multiSelectionActive:o,nodesDraggable:r,unselectNodesAndEdges:c,onNodeDragStart:u,onSelectionDragStart:f}=l.getState();v.current=!0;const g=i?u:la(f);s&&a||o||!i||n.get(i)?.selected||c(),i&&a&&s&&aa({id:i,store:l,nodeRef:e});const p=S(t);if(h.current=p,d.current=ta(n,r,p,i),g&&d.current){const[e,o]=oa({nodeId:i,dragItems:d.current,nodeInternals:n});g(t.sourceEvent,e,o)}};if(!n){const n=function(){var e,t,n,o,r=io,i=ao,a=so,s=lo,l={},c=kt("start","drag","end"),u=0,d=0;function h(e){e.on("mousedown.drag",f).filter(s).on("touchstart.drag",m).on("touchmove.drag",y,Gn).on("touchend.drag touchcancel.drag",v).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(a,s){if(!o&&r.call(this,a,s)){var l=b(this,i.call(this,a,s),a,s,"mouse");l&&(qn(a.view).on("mousemove.drag",g,Qn).on("mouseup.drag",p,Qn),to(a.view),Jn(a),n=!1,e=a.clientX,t=a.clientY,l("start",a))}}function g(o){if(eo(o),!n){var r=o.clientX-e,i=o.clientY-t;n=r*r+i*i>d}l.mouse("drag",o)}function p(e){qn(e.view).on("mousemove.drag mouseup.drag",null),no(e.view,n),eo(e),l.mouse("end",e)}function m(e,t){if(r.call(this,e,t)){var n,o,a=e.changedTouches,s=i.call(this,e,t),l=a.length;for(n=0;n<l;++n)(o=b(this,s,e,t,a[n].identifier,a[n]))&&(Jn(e),o("start",e,a[n]))}}function y(e){var t,n,o=e.changedTouches,r=o.length;for(t=0;t<r;++t)(n=l[o[t].identifier])&&(eo(e),n("drag",e,o[t]))}function v(e){var t,n,r=e.changedTouches,i=r.length;for(o&&clearTimeout(o),o=setTimeout((function(){o=null}),500),t=0;t<i;++t)(n=l[r[t].identifier])&&(Jn(e),n("end",e,r[t]))}function b(e,t,n,o,r,i){var s,d,f,g=c.copy(),p=Un(i||n,t);if(null!=(f=a.call(e,new ro("beforestart",{sourceEvent:n,target:h,identifier:r,active:u,x:p[0],y:p[1],dx:0,dy:0,dispatch:g}),o)))return s=f.x-p[0]||0,d=f.y-p[1]||0,function n(i,a,c){var m,y=p;switch(i){case"start":l[r]=n,m=u++;break;case"end":delete l[r],--u;case"drag":p=Un(c||a,t),m=u}g.call(i,e,new ro(i,{sourceEvent:a,subject:f,target:h,identifier:r,active:m,x:p[0]+s,y:p[1]+d,dx:p[0]-y[0],dy:p[1]-y[1],dispatch:g}),o)}}return h.filter=function(e){return arguments.length?(r="function"==typeof e?e:oo(!!e),h):r},h.container=function(e){return arguments.length?(i="function"==typeof e?e:oo(e),h):i},h.subject=function(e){return arguments.length?(a="function"==typeof e?e:oo(e),h):a},h.touchable=function(e){return arguments.length?(s="function"==typeof e?e:oo(!!e),h):s},h.on=function(){var e=c.on.apply(c,arguments);return e===c?h:e},h.clickDistance=function(e){return arguments.length?(d=(e=+e)*e,h):Math.sqrt(d)},h}().on("start",(e=>{const{domNode:t,nodeDragThreshold:n}=l.getState();0===n&&x(e),b.current=!1;const o=S(e);h.current=o,g.current=t?.getBoundingClientRect()||null,p.current=Q(e.sourceEvent,g.current)})).on("drag",(e=>{const t=S(e),{autoPanOnNodeDrag:n,nodeDragThreshold:o}=l.getState();if("touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1&&(b.current=!0),!b.current){if(!y.current&&v.current&&n&&(y.current=!0,w()),!v.current){const n=t.xSnapped-(h?.current?.x??0),r=t.ySnapped-(h?.current?.y??0);Math.sqrt(n*n+r*r)>o&&x(e)}(h.current.x!==t.xSnapped||h.current.y!==t.ySnapped)&&d.current&&v.current&&(m.current=e.sourceEvent,p.current=Q(e.sourceEvent,g.current),c(t))}})).on("end",(e=>{if(v.current&&!b.current&&(u(!1),y.current=!1,v.current=!1,cancelAnimationFrame(f.current),d.current)){const{updateNodePositions:t,nodeInternals:n,onNodeDragStop:o,onSelectionDragStop:r}=l.getState(),a=i?o:la(r);if(t(d.current,!1,!1),a){const[t,o]=oa({nodeId:i,dragItems:d.current,nodeInternals:n});a(e.sourceEvent,t,o)}}})).filter((t=>{const n=t.target;return!t.button&&(!o||!ea(n,`.${o}`,e))&&(!r||ea(n,r,e))}));return t.call(n),()=>{t.on(".drag",null)}}t.on(".drag",null)}}),[e,n,o,r,a,l,i,s,S]),c}function ua(){const e=O();return t.useCallback((t=>{const{nodeInternals:n,nodeExtent:o,updateNodePositions:r,getNodes:i,snapToGrid:a,snapGrid:s,onError:l,nodesDraggable:c}=e.getState(),u=i().filter((e=>e.selected&&(e.draggable||c&&void 0===e.draggable))),d=a?s[0]:5,h=a?s[1]:5,f=t.isShiftPressed?4:1,g=t.x*d*f,p=t.y*h*f;r(u.map((e=>{if(e.positionAbsolute){const t={x:e.positionAbsolute.x+g,y:e.positionAbsolute.y+p};a&&(t.x=s[0]*Math.round(t.x/s[0]),t.y=s[1]*Math.round(t.y/s[1]));const{positionAbsolute:r,position:i}=na(e,t,n,o,void 0,l);e.position=i,e.positionAbsolute=r}return e})),!0,!1)}),[])}const da={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var ha=e=>{const n=({id:n,type:r,data:i,xPos:a,yPos:s,xPosOrigin:l,yPosOrigin:c,selected:u,onClick:d,onMouseEnter:h,onMouseMove:f,onMouseLeave:g,onContextMenu:p,onDoubleClick:m,style:y,className:v,isDraggable:b,isSelectable:S,isConnectable:w,isFocusable:x,selectNodesOnDrag:E,sourcePosition:_,targetPosition:C,hidden:M,resizeObserver:N,dragHandle:P,zIndex:k,isParent:A,noDragClassName:I,noPanClassName:D,initialized:R,disableKeyboardA11y:z,ariaLabel:T,rfId:$,hasHandleBounds:B})=>{const L=O(),H=t.useRef(null),X=t.useRef(null),Y=t.useRef(_),V=t.useRef(C),F=t.useRef(r),K=S||b||d||h||f||g,Z=ua(),j=ia(n,L.getState,h),W=ia(n,L.getState,f),G=ia(n,L.getState,g),Q=ia(n,L.getState,p),J=ia(n,L.getState,m);t.useEffect((()=>()=>{X.current&&(N?.unobserve(X.current),X.current=null)}),[]),t.useEffect((()=>{if(H.current&&!M){const e=H.current;R&&B&&X.current===e||(X.current&&N?.unobserve(X.current),N?.observe(e),X.current=e)}}),[M,R,B]),t.useEffect((()=>{const e=F.current!==r,t=Y.current!==_,o=V.current!==C;H.current&&(e||t||o)&&(e&&(F.current=r),t&&(Y.current=_),o&&(V.current=C),L.getState().updateNodeDimensions([{id:n,nodeElement:H.current,forceUpdate:!0}]))}),[n,r,_,C]);const ee=ca({nodeRef:H,disabled:M||!b,noDragClassName:I,handleSelector:P,nodeId:n,isSelectable:S,selectNodesOnDrag:E});return M?null:t.createElement("div",{className:o(["react-flow__node",`react-flow__node-${r}`,{[D]:b},v,{selected:u,selectable:S,parent:A,dragging:ee}]),ref:H,style:{zIndex:k,transform:`translate(${l}px,${c}px)`,pointerEvents:K?"all":"none",visibility:R?"visible":"hidden",...y},"data-id":n,"data-testid":`rf__node-${n}`,onMouseEnter:j,onMouseMove:W,onMouseLeave:G,onContextMenu:Q,onClick:e=>{const{nodeDragThreshold:t}=L.getState();if(S&&(!E||!b||t>0)&&aa({id:n,store:L,nodeRef:H}),d){const t=L.getState().nodeInternals.get(n);t&&d(e,{...t})}},onDoubleClick:J,onKeyDown:x?e=>{if(!U(e)&&!z)if(q.includes(e.key)&&S){const t="Escape"===e.key;aa({id:n,store:L,unselect:t,nodeRef:H})}else b&&u&&Object.prototype.hasOwnProperty.call(da,e.key)&&(L.setState({ariaLiveMessage:`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~a}, y: ${~~s}`}),Z({x:da[e.key].x,y:da[e.key].y,isShiftPressed:e.shiftKey}))}:void 0,tabIndex:x?0:void 0,role:x?"button":void 0,"aria-describedby":z?void 0:`${St}-${$}`,"aria-label":T},t.createElement(Ne,{value:n},t.createElement(e,{id:n,data:i,type:r,xPos:a,yPos:s,selected:u,isConnectable:w,sourcePosition:_,targetPosition:C,dragging:ee,dragHandle:P,zIndex:k})))};return n.displayName="NodeWrapper",t.memo(n)};const fa=e=>{const t=e.getNodes().filter((e=>e.selected));return{...Be(t,e.nodeOrigin),transformString:`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`,userSelectionActive:e.userSelectionActive}};var ga=t.memo((function({onSelectionContextMenu:e,noPanClassName:n,disableKeyboardA11y:r}){const i=O(),{width:a,height:s,x:l,y:c,transformString:u,userSelectionActive:d}=A(fa,Ce),h=ua(),f=t.useRef(null);if(t.useEffect((()=>{r||f.current?.focus({preventScroll:!0})}),[r]),ca({nodeRef:f}),d||!a||!s)return null;const g=e?t=>{const n=i.getState().getNodes().filter((e=>e.selected));e(t,n)}:void 0;return t.createElement("div",{className:o(["react-flow__nodesselection","react-flow__container",n]),style:{transform:u}},t.createElement("div",{ref:f,className:"react-flow__nodesselection-rect",onContextMenu:g,tabIndex:r?void 0:-1,onKeyDown:r?void 0:e=>{Object.prototype.hasOwnProperty.call(da,e.key)&&h({x:da[e.key].x,y:da[e.key].y,isShiftPressed:e.shiftKey})},style:{width:a,height:s,top:c,left:l}}))}));const pa=e=>e.nodesSelectionActive,ma=({children:e,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,deleteKeyCode:l,onMove:c,onMoveStart:u,onMoveEnd:d,selectionKeyCode:h,selectionOnDrag:f,selectionMode:g,onSelectionStart:p,onSelectionEnd:m,multiSelectionKeyCode:y,panActivationKeyCode:v,zoomActivationKeyCode:b,elementsSelectable:S,zoomOnScroll:w,zoomOnPinch:x,panOnScroll:E,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:M,panOnDrag:N,defaultViewport:P,translateExtent:k,minZoom:I,maxZoom:D,preventScrolling:R,onSelectionContextMenu:z,noWheelClassName:T,noPanClassName:$,disableKeyboardA11y:B})=>{const L=A(pa),H=Ct(h),X=Ct(v),Y=X||N,V=X||E,F=H||f&&!0!==Y;return(({deleteKeyCode:e,multiSelectionKeyCode:n})=>{const o=O(),{deleteElements:r}=Di(),i=Ct(e,Ri),a=Ct(n);t.useEffect((()=>{if(i){const{edges:e,getNodes:t}=o.getState(),n=t().filter((e=>e.selected)),i=e.filter((e=>e.selected));r({nodes:n,edges:i}),o.setState({nodesSelectionActive:!1})}}),[i]),t.useEffect((()=>{o.setState({multiSelectionActive:a})}),[a])})({deleteKeyCode:l,multiSelectionKeyCode:y}),t.createElement(Xi,{onMove:c,onMoveStart:u,onMoveEnd:d,onPaneContextMenu:a,elementsSelectable:S,zoomOnScroll:w,zoomOnPinch:x,panOnScroll:V,panOnScrollSpeed:_,panOnScrollMode:C,zoomOnDoubleClick:M,panOnDrag:!H&&Y,defaultViewport:P,translateExtent:k,minZoom:I,maxZoom:D,zoomActivationKeyCode:b,preventScrolling:R,noWheelClassName:T,noPanClassName:$},t.createElement(Qi,{onSelectionStart:p,onSelectionEnd:m,onPaneClick:n,onPaneMouseEnter:o,onPaneMouseMove:r,onPaneMouseLeave:i,onPaneContextMenu:a,onPaneScroll:s,panOnDrag:Y,isSelecting:!!F,selectionMode:g},e,L&&t.createElement(ga,{onSelectionContextMenu:z,noPanClassName:$,disableKeyboardA11y:B})))};ma.displayName="FlowRenderer";var ya=t.memo(ma);function va(e){return{...{input:ha(e.input||rt),default:ha(e.default||nt),output:ha(e.output||at),group:ha(e.group||st)},...Object.keys(e).filter((e=>!["input","default","output","group"].includes(e))).reduce(((t,n)=>(t[n]=ha(e[n]||nt),t)),{})}}const ba=e=>({nodesDraggable:e.nodesDraggable,nodesConnectable:e.nodesConnectable,nodesFocusable:e.nodesFocusable,elementsSelectable:e.elementsSelectable,updateNodeDimensions:e.updateNodeDimensions,onError:e.onError}),Sa=n=>{const{nodesDraggable:o,nodesConnectable:r,nodesFocusable:i,elementsSelectable:a,updateNodeDimensions:s,onError:l}=A(ba,Ce),c=(u=n.onlyRenderVisibleElements,A(t.useCallback((e=>u?Le(e.nodeInternals,{x:0,y:0,width:e.width,height:e.height},e.transform,!0):e.getNodes()),[u])));var u;const d=t.useRef(),h=t.useMemo((()=>{if("undefined"==typeof ResizeObserver)return null;const e=new ResizeObserver((e=>{const t=e.map((e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})));s(t)}));return d.current=e,e}),[]);return t.useEffect((()=>()=>{d?.current?.disconnect()}),[]),t.createElement("div",{className:"react-flow__nodes",style:zi},c.map((s=>{let c=s.type||"default";n.nodeTypes[c]||(l?.("003",w(c)),c="default");const u=n.nodeTypes[c]||n.nodeTypes.default,d=!!(s.draggable||o&&void 0===s.draggable),f=!!(s.selectable||a&&void 0===s.selectable),g=!!(s.connectable||r&&void 0===s.connectable),p=!!(s.focusable||i&&void 0===s.focusable),m=n.nodeExtent?B(s.positionAbsolute,n.nodeExtent):s.positionAbsolute,y=m?.x??0,v=m?.y??0,b=(({x:e,y:t,width:n,height:o,origin:r})=>n&&o?r[0]<0||r[1]<0||r[0]>1||r[1]>1?{x:e,y:t}:{x:e-n*r[0],y:t-o*r[1]}:{x:e,y:t})({x:y,y:v,width:s.width??0,height:s.height??0,origin:n.nodeOrigin});return t.createElement(u,{key:s.id,id:s.id,className:s.className,style:s.style,type:c,data:s.data,sourcePosition:s.sourcePosition||e.Position.Bottom,targetPosition:s.targetPosition||e.Position.Top,hidden:s.hidden,xPos:y,yPos:v,xPosOrigin:b.x,yPosOrigin:b.y,selectNodesOnDrag:n.selectNodesOnDrag,onClick:n.onNodeClick,onMouseEnter:n.onNodeMouseEnter,onMouseMove:n.onNodeMouseMove,onMouseLeave:n.onNodeMouseLeave,onContextMenu:n.onNodeContextMenu,onDoubleClick:n.onNodeDoubleClick,selected:!!s.selected,isDraggable:d,isSelectable:f,isConnectable:g,isFocusable:p,resizeObserver:h,dragHandle:s.dragHandle,zIndex:s[W]?.z??0,isParent:!!s[W]?.isParent,noDragClassName:n.noDragClassName,noPanClassName:n.noPanClassName,initialized:!!s.width&&!!s.height,rfId:n.rfId,disableKeyboardA11y:n.disableKeyboardA11y,ariaLabel:s.ariaLabel,hasHandleBounds:!!s[W]?.handleBounds})})))};Sa.displayName="NodeRenderer";var wa=t.memo(Sa);const xa=(t,n,o)=>o===e.Position.Left?t-n:o===e.Position.Right?t+n:t,Ea=(t,n,o)=>o===e.Position.Top?t-n:o===e.Position.Bottom?t+n:t,_a="react-flow__edgeupdater",Ca=({position:e,centerX:n,centerY:r,radius:i=10,onMouseDown:a,onMouseEnter:s,onMouseOut:l,type:c})=>t.createElement("circle",{onMouseDown:a,onMouseEnter:s,onMouseOut:l,className:o([_a,`${_a}-${c}`]),cx:xa(n,i,e),cy:Ea(r,i,e),r:i,stroke:"transparent",fill:"transparent"}),Ma=()=>!0;var Na=e=>{const n=({id:n,className:r,type:i,data:a,onClick:s,onEdgeDoubleClick:l,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,style:y,source:v,target:b,sourceX:S,sourceY:w,targetX:x,targetY:E,sourcePosition:_,targetPosition:C,elementsSelectable:M,hidden:N,sourceHandleId:P,targetHandleId:k,onContextMenu:A,onMouseEnter:I,onMouseMove:D,onMouseLeave:R,reconnectRadius:z,onReconnect:T,onReconnectStart:$,onReconnectEnd:B,markerEnd:L,markerStart:H,rfId:X,ariaLabel:Y,isFocusable:V,isReconnectable:F,pathOptions:K,interactionWidth:Z,disableKeyboardA11y:j})=>{const W=t.useRef(null),[U,G]=t.useState(!1),[Q,J]=t.useState(!1),ee=O(),ne=t.useMemo((()=>`url('#${Ie(H,X)}')`),[H,X]),oe=t.useMemo((()=>`url('#${Ie(L,X)}')`),[L,X]);if(N)return null;const re=te(n,ee.getState,l),ie=te(n,ee.getState,A),ae=te(n,ee.getState,I),se=te(n,ee.getState,D),le=te(n,ee.getState,R),ce=(e,t)=>{if(0!==e.button)return;const{edges:o,isValidConnection:r}=ee.getState(),i=t?b:v,a=(t?k:P)||null,s=t?"target":"source",l=r||Ma,c=t,u=o.find((e=>e.id===n));J(!0),$?.(e,u,s);Ue({event:e,handleId:a,nodeId:i,onConnect:e=>T?.(u,e),isTarget:c,getState:ee.getState,setState:ee.setState,isValidConnection:l,edgeUpdaterType:s,onReconnectEnd:e=>{J(!1),B?.(e,u,s)}})},ue=()=>G(!0),de=()=>G(!1),he=!M&&!s;return t.createElement("g",{className:o(["react-flow__edge",`react-flow__edge-${i}`,r,{selected:c,animated:u,inactive:he,updating:U}]),onClick:e=>{const{edges:t,addSelectedEdges:o,unselectNodesAndEdges:r,multiSelectionActive:i}=ee.getState(),a=t.find((e=>e.id===n));a&&(M&&(ee.setState({nodesSelectionActive:!1}),a.selected&&i?(r({nodes:[],edges:[a]}),W.current?.blur()):o([n])),s&&s(e,a))},onDoubleClick:re,onContextMenu:ie,onMouseEnter:ae,onMouseMove:se,onMouseLeave:le,onKeyDown:V?e=>{if(!j&&q.includes(e.key)&&M){const{unselectNodesAndEdges:t,addSelectedEdges:o,edges:r}=ee.getState();"Escape"===e.key?(W.current?.blur(),t({edges:[r.find((e=>e.id===n))]})):o([n])}}:void 0,tabIndex:V?0:void 0,role:V?"button":"img","data-testid":`rf__edge-${n}`,"aria-label":null===Y?void 0:Y||`Edge from ${v} to ${b}`,"aria-describedby":V?`${wt}-${X}`:void 0,ref:W},!Q&&t.createElement(e,{id:n,source:v,target:b,selected:c,animated:u,label:d,labelStyle:h,labelShowBg:f,labelBgStyle:g,labelBgPadding:p,labelBgBorderRadius:m,data:a,style:y,sourceX:S,sourceY:w,targetX:x,targetY:E,sourcePosition:_,targetPosition:C,sourceHandleId:P,targetHandleId:k,markerStart:ne,markerEnd:oe,pathOptions:K,interactionWidth:Z}),F&&t.createElement(t.Fragment,null,("source"===F||!0===F)&&t.createElement(Ca,{position:_,centerX:S,centerY:w,radius:z,onMouseDown:e=>ce(e,!0),onMouseEnter:ue,onMouseOut:de,type:"source"}),("target"===F||!0===F)&&t.createElement(Ca,{position:C,centerX:x,centerY:E,radius:z,onMouseDown:e=>ce(e,!1),onMouseEnter:ue,onMouseOut:de,type:"target"})))};return n.displayName="EdgeWrapper",t.memo(n)};function Pa(e){return{...{default:Na(e.default||_e),straight:Na(e.bezier||Se),step:Na(e.step||ve),smoothstep:Na(e.step||ye),simplebezier:Na(e.simplebezier||he)},...Object.keys(e).filter((e=>!["default","bezier"].includes(e))).reduce(((t,n)=>(t[n]=Na(e[n]||_e),t)),{})}}function ka(t,n,o=null){const r=(o?.x||0)+n.x,i=(o?.y||0)+n.y,a=o?.width||n.width,s=o?.height||n.height;switch(t){case e.Position.Top:return{x:r+a/2,y:i};case e.Position.Right:return{x:r+a,y:i+s/2};case e.Position.Bottom:return{x:r+a/2,y:i+s};case e.Position.Left:return{x:r,y:i+s/2}}}function Aa(e,t){return e?1!==e.length&&t?t&&e.find((e=>e.id===t))||null:e[0]:null}function Oa(e){const t=e?.[W]?.handleBounds||null,n=t&&e?.width&&e?.height&&void 0!==e?.positionAbsolute?.x&&void 0!==e?.positionAbsolute?.y;return[{x:e?.positionAbsolute?.x||0,y:e?.positionAbsolute?.y||0,width:e?.width||0,height:e?.height||0},t,!!n]}const Ia=[{level:0,isMaxLevel:!0,edges:[]}];function Da(e,n,o){return function(e,t,n=!1){let o=-1;const r=e.reduce(((e,r)=>{const i=j(r.zIndex);let a=i?r.zIndex:0;if(n){const e=t.get(r.target),n=t.get(r.source),o=r.selected||e?.selected||n?.selected,s=Math.max(n?.[W]?.z||0,e?.[W]?.z||0,1e3);a=(i?r.zIndex:0)+(o?s:0)}return e[a]?e[a].push(r):e[a]=[r],o=a>o?a:o,e}),{}),i=Object.entries(r).map((([e,t])=>{const n=+e;return{edges:t,level:n,isMaxLevel:n===o}}));return 0===i.length?Ia:i}(A(t.useCallback((t=>e?t.edges.filter((e=>{const o=n.get(e.source),r=n.get(e.target);return o?.width&&o?.height&&r?.width&&r?.height&&function({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:i,width:a,height:s,transform:l}){const c={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+i)};c.x===c.x2&&(c.x2+=1),c.y===c.y2&&(c.y2+=1);const u=V({x:(0-l[0])/l[2],y:(0-l[1])/l[2],width:a/l[2],height:s/l[2]}),d=Math.max(0,Math.min(u.x2,c.x2)-Math.max(u.x,c.x)),h=Math.max(0,Math.min(u.y2,c.y2)-Math.max(u.y,c.y));return Math.ceil(d*h)>0}({sourcePos:o.positionAbsolute||{x:0,y:0},targetPos:r.positionAbsolute||{x:0,y:0},sourceWidth:o.width,sourceHeight:o.height,targetWidth:r.width,targetHeight:r.height,width:t.width,height:t.height,transform:t.transform})})):t.edges),[e,n])),n,o)}const Ra={[e.MarkerType.Arrow]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",fill:"none",points:"-5,-4 0,0 -5,4"}),[e.MarkerType.ArrowClosed]:({color:e="none",strokeWidth:n=1})=>t.createElement("polyline",{style:{stroke:e,fill:e,strokeWidth:n},strokeLinecap:"round",strokeLinejoin:"round",points:"-5,-4 0,0 -5,4 -5,-4"})};const za=({id:e,type:n,color:o,width:r=12.5,height:i=12.5,markerUnits:a="strokeWidth",strokeWidth:s,orient:l="auto-start-reverse"})=>{const c=function(e){const n=O();return t.useMemo((()=>Object.prototype.hasOwnProperty.call(Ra,e)?Ra[e]:(n.getState().onError?.("009",_(e)),null)),[e])}(n);return c?t.createElement("marker",{className:"react-flow__arrowhead",id:e,markerWidth:`${r}`,markerHeight:`${i}`,viewBox:"-10 -10 20 20",markerUnits:a,orient:l,refX:"0",refY:"0"},t.createElement(c,{color:o,strokeWidth:s})):null},Ta=({defaultColor:e,rfId:n})=>{const o=A(t.useCallback((({defaultColor:e,rfId:t})=>n=>{const o=[];return n.edges.reduce(((n,r)=>([r.markerStart,r.markerEnd].forEach((r=>{if(r&&"object"==typeof r){const i=Ie(r,t);o.includes(i)||(n.push({id:i,color:r.color||e,...r}),o.push(i))}})),n)),[]).sort(((e,t)=>e.id.localeCompare(t.id)))})({defaultColor:e,rfId:n}),[e,n]),((e,t)=>!(e.length!==t.length||e.some(((e,n)=>e.id!==t[n].id)))));return t.createElement("defs",null,o.map((e=>t.createElement(za,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,strokeWidth:e.strokeWidth,orient:e.orient}))))};Ta.displayName="MarkerDefinitions";var $a=t.memo(Ta);const Ba=e=>({nodesConnectable:e.nodesConnectable,edgesFocusable:e.edgesFocusable,edgesUpdatable:e.edgesUpdatable,elementsSelectable:e.elementsSelectable,width:e.width,height:e.height,connectionMode:e.connectionMode,nodeInternals:e.nodeInternals,onError:e.onError}),La=({defaultMarkerColor:n,onlyRenderVisibleElements:r,elevateEdgesOnSelect:i,rfId:a,edgeTypes:s,noPanClassName:l,onEdgeContextMenu:c,onEdgeMouseEnter:u,onEdgeMouseMove:d,onEdgeMouseLeave:h,onEdgeClick:f,onEdgeDoubleClick:g,onReconnect:p,onReconnectStart:m,onReconnectEnd:y,reconnectRadius:v,children:b,disableKeyboardA11y:S})=>{const{edgesFocusable:w,edgesUpdatable:x,elementsSelectable:E,width:_,height:M,connectionMode:P,nodeInternals:k,onError:O}=A(Ba,Ce),I=Da(r,k,i);return _?t.createElement(t.Fragment,null,I.map((({level:r,edges:i,isMaxLevel:b})=>t.createElement("svg",{key:r,style:{zIndex:r},width:_,height:M,className:"react-flow__edges react-flow__container"},b&&t.createElement($a,{defaultColor:n,rfId:a}),t.createElement("g",null,i.map((n=>{const[r,i,b]=Oa(k.get(n.source)),[_,M,A]=Oa(k.get(n.target));if(!b||!A)return null;let I=n.type||"default";s[I]||(O?.("011",N(I)),I="default");const D=s[I]||s.default,R=P===e.ConnectionMode.Strict?M.target:(M.target??[]).concat(M.source??[]),z=Aa(i.source,n.sourceHandle),T=Aa(R,n.targetHandle),$=z?.position||e.Position.Bottom,B=T?.position||e.Position.Top,L=!!(n.focusable||w&&void 0===n.focusable),H=n.reconnectable||n.updatable,X=void 0!==p&&(H||x&&void 0===H);if(!z||!T)return O?.("008",C(z,n)),null;const{sourceX:Y,sourceY:V,targetX:F,targetY:K}=((e,t,n,o,r,i)=>{const a=ka(n,e,t),s=ka(i,o,r);return{sourceX:a.x,sourceY:a.y,targetX:s.x,targetY:s.y}})(r,z,$,_,T,B);return t.createElement(D,{key:n.id,id:n.id,className:o([n.className,l]),type:I,data:n.data,selected:!!n.selected,animated:!!n.animated,hidden:!!n.hidden,label:n.label,labelStyle:n.labelStyle,labelShowBg:n.labelShowBg,labelBgStyle:n.labelBgStyle,labelBgPadding:n.labelBgPadding,labelBgBorderRadius:n.labelBgBorderRadius,style:n.style,source:n.source,target:n.target,sourceHandleId:n.sourceHandle,targetHandleId:n.targetHandle,markerEnd:n.markerEnd,markerStart:n.markerStart,sourceX:Y,sourceY:V,targetX:F,targetY:K,sourcePosition:$,targetPosition:B,elementsSelectable:E,onContextMenu:c,onMouseEnter:u,onMouseMove:d,onMouseLeave:h,onClick:f,onEdgeDoubleClick:g,onReconnect:p,onReconnectStart:m,onReconnectEnd:y,reconnectRadius:v,rfId:a,ariaLabel:n.ariaLabel,isFocusable:L,isReconnectable:X,pathOptions:"pathOptions"in n?n.pathOptions:void 0,interactionWidth:n.interactionWidth,disableKeyboardA11y:S})})))))),b):null};La.displayName="EdgeRenderer";var Ha=t.memo(La);const Xa=e=>`translate(${e.transform[0]}px,${e.transform[1]}px) scale(${e.transform[2]})`;function Ya({children:e}){const n=A(Xa);return t.createElement("div",{className:"react-flow__viewport react-flow__container",style:{transform:n}},e)}const Va={[e.Position.Left]:e.Position.Right,[e.Position.Right]:e.Position.Left,[e.Position.Top]:e.Position.Bottom,[e.Position.Bottom]:e.Position.Top},Fa=({nodeId:n,handleType:o,style:r,type:i=e.ConnectionLineType.Bezier,CustomComponent:a,connectionStatus:s})=>{const{fromNode:l,handleId:c,toX:u,toY:d,connectionMode:h}=A(t.useCallback((e=>({fromNode:e.nodeInternals.get(n),handleId:e.connectionHandleId,toX:(e.connectionPosition.x-e.transform[0])/e.transform[2],toY:(e.connectionPosition.y-e.transform[1])/e.transform[2],connectionMode:e.connectionMode})),[n]),Ce),f=l?.[W]?.handleBounds;let g=f?.[o];if(h===e.ConnectionMode.Loose&&(g=g||f?.["source"===o?"target":"source"]),!l||!g)return null;const p=c?g.find((e=>e.id===c)):g[0],m=p?p.x+p.width/2:(l.width??0)/2,y=p?p.y+p.height/2:l.height??0,v=(l.positionAbsolute?.x??0)+m,b=(l.positionAbsolute?.y??0)+y,S=p?.position,w=S?Va[S]:null;if(!S||!w)return null;if(a)return t.createElement(a,{connectionLineType:i,connectionLineStyle:r,fromNode:l,fromHandle:p,fromX:v,fromY:b,toX:u,toY:d,fromPosition:S,toPosition:w,connectionStatus:s});let x="";const E={sourceX:v,sourceY:b,sourcePosition:S,targetX:u,targetY:d,targetPosition:w};return i===e.ConnectionLineType.Bezier?[x]=Ee(E):i===e.ConnectionLineType.Step?[x]=me({...E,borderRadius:0}):i===e.ConnectionLineType.SmoothStep?[x]=me(E):i===e.ConnectionLineType.SimpleBezier?[x]=de(E):x=`M${v},${b} ${u},${d}`,t.createElement("path",{d:x,fill:"none",className:"react-flow__connection-path",style:r})};Fa.displayName="ConnectionLine";const Ka=e=>({nodeId:e.connectionNodeId,handleType:e.connectionHandleType,nodesConnectable:e.nodesConnectable,connectionStatus:e.connectionStatus,width:e.width,height:e.height});function Za({containerStyle:e,style:n,type:r,component:i}){const{nodeId:a,handleType:s,nodesConnectable:l,width:c,height:u,connectionStatus:d}=A(Ka,Ce);return!!(a&&s&&c&&l)?t.createElement("svg",{style:e,width:c,height:u,className:"react-flow__edges react-flow__connectionline react-flow__container"},t.createElement("g",{className:o(["react-flow__connection",d])},t.createElement(Fa,{nodeId:a,handleType:s,style:n,type:r,CustomComponent:i,connectionStatus:d}))):null}function ja(e,n){t.useRef(null),O();return t.useMemo((()=>n(e)),[e])}const Wa=({nodeTypes:e,edgeTypes:n,onMove:o,onMoveStart:r,onMoveEnd:i,onInit:a,onNodeClick:s,onEdgeClick:l,onNodeDoubleClick:c,onEdgeDoubleClick:u,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,onSelectionContextMenu:p,onSelectionStart:m,onSelectionEnd:y,connectionLineType:v,connectionLineStyle:b,connectionLineComponent:S,connectionLineContainerStyle:w,selectionKeyCode:x,selectionOnDrag:E,selectionMode:_,multiSelectionKeyCode:C,panActivationKeyCode:M,zoomActivationKeyCode:N,deleteKeyCode:P,onlyRenderVisibleElements:k,elementsSelectable:A,selectNodesOnDrag:O,defaultViewport:I,translateExtent:D,minZoom:R,maxZoom:z,preventScrolling:T,defaultMarkerColor:$,zoomOnScroll:B,zoomOnPinch:L,panOnScroll:H,panOnScrollSpeed:X,panOnScrollMode:Y,zoomOnDoubleClick:V,panOnDrag:F,onPaneClick:K,onPaneMouseEnter:Z,onPaneMouseMove:j,onPaneMouseLeave:W,onPaneScroll:q,onPaneContextMenu:U,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,elevateEdgesOnSelect:le,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})=>{const fe=ja(e,va),ge=ja(n,Pa);return function(e){const n=Di(),o=t.useRef(!1);t.useEffect((()=>{!o.current&&n.viewportInitialized&&e&&(setTimeout((()=>e(n)),1),o.current=!0)}),[e,n.viewportInitialized])}(a),t.createElement(ya,{onPaneClick:K,onPaneMouseEnter:Z,onPaneMouseMove:j,onPaneMouseLeave:W,onPaneContextMenu:U,onPaneScroll:q,deleteKeyCode:P,selectionKeyCode:x,selectionOnDrag:E,selectionMode:_,onSelectionStart:m,onSelectionEnd:y,multiSelectionKeyCode:C,panActivationKeyCode:M,zoomActivationKeyCode:N,elementsSelectable:A,onMove:o,onMoveStart:r,onMoveEnd:i,zoomOnScroll:B,zoomOnPinch:L,zoomOnDoubleClick:V,panOnScroll:H,panOnScrollSpeed:X,panOnScrollMode:Y,panOnDrag:F,defaultViewport:I,translateExtent:D,minZoom:R,maxZoom:z,onSelectionContextMenu:p,preventScrolling:T,noDragClassName:ie,noWheelClassName:ae,noPanClassName:se,disableKeyboardA11y:ce},t.createElement(Ya,null,t.createElement(Ha,{edgeTypes:ge,onEdgeClick:l,onEdgeDoubleClick:u,onlyRenderVisibleElements:k,onEdgeContextMenu:G,onEdgeMouseEnter:Q,onEdgeMouseMove:J,onEdgeMouseLeave:ee,onReconnect:te,onReconnectStart:ne,onReconnectEnd:oe,reconnectRadius:re,defaultMarkerColor:$,noPanClassName:se,elevateEdgesOnSelect:!!le,disableKeyboardA11y:ce,rfId:he},t.createElement(Za,{style:b,type:v,component:S,containerStyle:w})),t.createElement("div",{className:"react-flow__edgelabel-renderer"}),t.createElement(wa,{nodeTypes:fe,onNodeClick:s,onNodeDoubleClick:c,onNodeMouseEnter:d,onNodeMouseMove:h,onNodeMouseLeave:f,onNodeContextMenu:g,selectNodesOnDrag:O,onlyRenderVisibleElements:k,noPanClassName:se,noDragClassName:ie,disableKeyboardA11y:ce,nodeOrigin:ue,nodeExtent:de,rfId:he})))};Wa.displayName="GraphView";var qa=t.memo(Wa);const Ua=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],Ga={rfId:"1",width:0,height:0,transform:[0,0,1],nodeInternals:new Map,edges:[],onNodesChange:null,onEdgesChange:null,hasDefaultNodes:!1,hasDefaultEdges:!1,d3Zoom:null,d3Selection:null,d3ZoomHandler:void 0,minZoom:.5,maxZoom:2,translateExtent:Ua,nodeExtent:Ua,nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,connectionNodeId:null,connectionHandleId:null,connectionHandleType:"source",connectionPosition:{x:0,y:0},connectionStatus:null,connectionMode:e.ConnectionMode.Strict,domNode:null,paneDragging:!1,noPanClassName:"nopan",nodeOrigin:[0,0],nodeDragThreshold:0,snapGrid:[15,15],snapToGrid:!1,nodesDraggable:!0,nodesConnectable:!0,nodesFocusable:!0,edgesFocusable:!0,edgesUpdatable:!0,elementsSelectable:!0,elevateNodesOnSelect:!0,fitViewOnInit:!1,fitViewOnInitDone:!1,fitViewOnInitOptions:void 0,onSelectionChange:[],multiSelectionActive:!1,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectOnClick:!0,ariaLiveMessage:"",autoPanOnConnect:!0,autoPanOnNodeDrag:!0,connectionRadius:20,onError:(e,t)=>{},isValidConnection:void 0},Qa=()=>{return e=(e,t)=>({...Ga,setNodes:n=>{const{nodeInternals:o,nodeOrigin:r,elevateNodesOnSelect:i}=t();e({nodeInternals:Ci(n,o,r,i)})},getNodes:()=>Array.from(t().nodeInternals.values()),setEdges:n=>{const{defaultEdgeOptions:o={}}=t();e({edges:n.map((e=>({...o,...e})))})},setDefaultNodesAndEdges:(n,o)=>{const r=void 0!==n,i=void 0!==o,a=r?Ci(n,new Map,t().nodeOrigin,t().elevateNodesOnSelect):new Map;e({nodeInternals:a,edges:i?o:[],hasDefaultNodes:r,hasDefaultEdges:i})},updateNodeDimensions:n=>{const{onNodesChange:o,nodeInternals:r,fitViewOnInit:i,fitViewOnInitDone:a,fitViewOnInitOptions:s,domNode:l,nodeOrigin:c}=t(),u=l?.querySelector(".react-flow__viewport");if(!u)return;const d=window.getComputedStyle(u),{m22:h}=new window.DOMMatrixReadOnly(d.transform),f=n.reduce(((e,t)=>{const n=r.get(t.id);if(n?.hidden)r.set(n.id,{...n,[W]:{...n[W],handleBounds:void 0}});else if(n){const o=T(t.nodeElement);o.width&&o.height&&(n.width!==o.width||n.height!==o.height||t.forceUpdate)&&(r.set(n.id,{...n,[W]:{...n[W],handleBounds:{source:ra(".source",t.nodeElement,h,c),target:ra(".target",t.nodeElement,h,c)}},...o}),e.push({id:n.id,type:"dimensions",dimensions:o}))}return e}),[]);_i(r,c);const g=a||i&&!a&&Mi(t,{initial:!0,...s});e({nodeInternals:new Map(r),fitViewOnInitDone:g}),f?.length>0&&o?.(f)},updateNodePositions:(e,n=!0,o=!1)=>{const{triggerNodeChanges:r}=t();r(e.map((e=>{const t={id:e.id,type:"position",dragging:o};return n&&(t.positionAbsolute=e.positionAbsolute,t.position=e.position),t})))},triggerNodeChanges:n=>{const{onNodesChange:o,nodeInternals:r,hasDefaultNodes:i,nodeOrigin:a,getNodes:s,elevateNodesOnSelect:l}=t();if(n?.length){if(i){const t=Ci(Zi(n,s()),r,a,l);e({nodeInternals:t})}o?.(n)}},addSelectedNodes:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Wi(e,!0))):(a=qi(i(),n),s=qi(r,[])),ki({changedNodes:a,changedEdges:s,get:t,set:e})},addSelectedEdges:n=>{const{multiSelectionActive:o,edges:r,getNodes:i}=t();let a,s=null;o?a=n.map((e=>Wi(e,!0))):(a=qi(r,n),s=qi(i(),[])),ki({changedNodes:s,changedEdges:a,get:t,set:e})},unselectNodesAndEdges:({nodes:n,edges:o}={})=>{const{edges:r,getNodes:i}=t(),a=o||r;ki({changedNodes:(n||i()).map((e=>(e.selected=!1,Wi(e.id,!1)))),changedEdges:a.map((e=>Wi(e.id,!1))),get:t,set:e})},setMinZoom:n=>{const{d3Zoom:o,maxZoom:r}=t();o?.scaleExtent([n,r]),e({minZoom:n})},setMaxZoom:n=>{const{d3Zoom:o,minZoom:r}=t();o?.scaleExtent([r,n]),e({maxZoom:n})},setTranslateExtent:n=>{t().d3Zoom?.translateExtent(n),e({translateExtent:n})},resetSelectedElements:()=>{const{edges:n,getNodes:o}=t();ki({changedNodes:o().filter((e=>e.selected)).map((e=>Wi(e.id,!1))),changedEdges:n.filter((e=>e.selected)).map((e=>Wi(e.id,!1))),get:t,set:e})},setNodeExtent:n=>{const{nodeInternals:o}=t();o.forEach((e=>{e.positionAbsolute=B(e.position,n)})),e({nodeExtent:n,nodeInternals:new Map(o)})},panBy:e=>{const{transform:n,width:o,height:r,d3Zoom:i,d3Selection:a,translateExtent:s}=t();if(!i||!a||!e.x&&!e.y)return!1;const l=gi.translate(n[0]+e.x,n[1]+e.y).scale(n[2]),c=[[0,0],[o,r]],u=i?.constrain()(l,c,s);return i.transform(a,u),n[0]!==u.x||n[1]!==u.y||n[2]!==u.k},cancelConnection:()=>e({connectionNodeId:Ga.connectionNodeId,connectionHandleId:Ga.connectionHandleId,connectionHandleType:Ga.connectionHandleType,connectionStatus:Ga.connectionStatus,connectionStartHandle:Ga.connectionStartHandle,connectionEndHandle:Ga.connectionEndHandle}),reset:()=>e({...Ga})}),t=Object.is,e?v(e,t):v;var e,t},Ja=({children:e})=>{const n=t.useRef(null);return n.current||(n.current=Qa()),t.createElement(S,{value:n.current},e)};Ja.displayName="ReactFlowProvider";const es=({children:e})=>t.useContext(b)?t.createElement(t.Fragment,null,e):t.createElement(Ja,null,e);es.displayName="ReactFlowWrapper";const ts={input:rt,default:nt,output:at,group:st},ns={default:_e,straight:Se,step:ve,smoothstep:ye,simplebezier:he},os=[0,0],rs=[15,15],is={x:0,y:0,zoom:1},as={width:"100%",height:"100%",overflow:"hidden",position:"relative",zIndex:0},ss=t.forwardRef((({nodes:n,edges:r,defaultNodes:i,defaultEdges:a,className:s,nodeTypes:l=ts,edgeTypes:c=ns,onNodeClick:u,onEdgeClick:d,onInit:h,onMove:f,onMoveStart:g,onMoveEnd:p,onConnect:m,onConnectStart:y,onConnectEnd:v,onClickConnectStart:b,onClickConnectEnd:S,onNodeMouseEnter:w,onNodeMouseMove:x,onNodeMouseLeave:E,onNodeContextMenu:_,onNodeDoubleClick:C,onNodeDragStart:M,onNodeDrag:N,onNodeDragStop:P,onNodesDelete:k,onEdgesDelete:A,onSelectionChange:O,onSelectionDragStart:I,onSelectionDrag:D,onSelectionDragStop:z,onSelectionContextMenu:T,onSelectionStart:$,onSelectionEnd:B,connectionMode:L=e.ConnectionMode.Strict,connectionLineType:H=e.ConnectionLineType.Bezier,connectionLineStyle:X,connectionLineComponent:Y,connectionLineContainerStyle:V,deleteKeyCode:F="Backspace",selectionKeyCode:K="Shift",selectionOnDrag:Z=!1,selectionMode:j=e.SelectionMode.Full,panActivationKeyCode:W="Space",multiSelectionKeyCode:q=(J()?"Meta":"Control"),zoomActivationKeyCode:U=(J()?"Meta":"Control"),snapToGrid:G=!1,snapGrid:Q=rs,onlyRenderVisibleElements:ee=!1,selectNodesOnDrag:te=!0,nodesDraggable:ne,nodesConnectable:oe,nodesFocusable:re,nodeOrigin:ie=os,edgesFocusable:ae,edgesUpdatable:se,elementsSelectable:le,defaultViewport:ce=is,minZoom:ue=.5,maxZoom:de=2,translateExtent:he=Ua,preventScrolling:fe=!0,nodeExtent:ge,defaultMarkerColor:pe="#b1b1b7",zoomOnScroll:me=!0,zoomOnPinch:ye=!0,panOnScroll:ve=!1,panOnScrollSpeed:be=.5,panOnScrollMode:Se=e.PanOnScrollMode.Free,zoomOnDoubleClick:we=!0,panOnDrag:xe=!0,onPaneClick:Ee,onPaneMouseEnter:_e,onPaneMouseMove:Ce,onPaneMouseLeave:Me,onPaneScroll:Ne,onPaneContextMenu:Pe,children:ke,onEdgeContextMenu:Ae,onEdgeDoubleClick:Oe,onEdgeMouseEnter:Ie,onEdgeMouseMove:De,onEdgeMouseLeave:Re,onEdgeUpdate:ze,onEdgeUpdateStart:Te,onEdgeUpdateEnd:$e,onReconnect:Be,onReconnectStart:Le,onReconnectEnd:He,reconnectRadius:Xe=10,edgeUpdaterRadius:Ye=10,onNodesChange:Ve,onEdgesChange:Fe,noDragClassName:Ke="nodrag",noWheelClassName:Ze="nowheel",noPanClassName:je="nopan",fitView:We=!1,fitViewOptions:qe,connectOnClick:Ue=!0,attributionPosition:Ge,proOptions:Qe,defaultEdgeOptions:Je,elevateNodesOnSelect:et=!0,elevateEdgesOnSelect:tt=!1,disableKeyboardA11y:nt=!1,autoPanOnConnect:ot=!0,autoPanOnNodeDrag:rt=!0,connectionRadius:it=20,isValidConnection:at,onError:st,style:lt,id:ct,nodeDragThreshold:ut,...dt},ht)=>{const gt=ct||"1";return t.createElement("div",{...dt,style:{...lt,...as},ref:ht,className:o(["react-flow",s]),"data-testid":"rf__wrapper",id:ct},t.createElement(es,null,t.createElement(qa,{onInit:h,onMove:f,onMoveStart:g,onMoveEnd:p,onNodeClick:u,onEdgeClick:d,onNodeMouseEnter:w,onNodeMouseMove:x,onNodeMouseLeave:E,onNodeContextMenu:_,onNodeDoubleClick:C,nodeTypes:l,edgeTypes:c,connectionLineType:H,connectionLineStyle:X,connectionLineComponent:Y,connectionLineContainerStyle:V,selectionKeyCode:K,selectionOnDrag:Z,selectionMode:j,deleteKeyCode:F,multiSelectionKeyCode:q,panActivationKeyCode:W,zoomActivationKeyCode:U,onlyRenderVisibleElements:ee,selectNodesOnDrag:te,defaultViewport:ce,translateExtent:he,minZoom:ue,maxZoom:de,preventScrolling:fe,zoomOnScroll:me,zoomOnPinch:ye,zoomOnDoubleClick:we,panOnScroll:ve,panOnScrollSpeed:be,panOnScrollMode:Se,panOnDrag:xe,onPaneClick:Ee,onPaneMouseEnter:_e,onPaneMouseMove:Ce,onPaneMouseLeave:Me,onPaneScroll:Ne,onPaneContextMenu:Pe,onSelectionContextMenu:T,onSelectionStart:$,onSelectionEnd:B,onEdgeContextMenu:Ae,onEdgeDoubleClick:Oe,onEdgeMouseEnter:Ie,onEdgeMouseMove:De,onEdgeMouseLeave:Re,onReconnect:Be??ze,onReconnectStart:Le??Te,onReconnectEnd:He??$e,reconnectRadius:Xe??Ye,defaultMarkerColor:pe,noDragClassName:Ke,noWheelClassName:Ze,noPanClassName:je,elevateEdgesOnSelect:tt,rfId:gt,disableKeyboardA11y:nt,nodeOrigin:ie,nodeExtent:ge}),t.createElement(yt,{nodes:n,edges:r,defaultNodes:i,defaultEdges:a,onConnect:m,onConnectStart:y,onConnectEnd:v,onClickConnectStart:b,onClickConnectEnd:S,nodesDraggable:ne,nodesConnectable:oe,nodesFocusable:re,edgesFocusable:ae,edgesUpdatable:se,elementsSelectable:le,elevateNodesOnSelect:et,minZoom:ue,maxZoom:de,nodeExtent:ge,onNodesChange:Ve,onEdgesChange:Fe,snapToGrid:G,snapGrid:Q,connectionMode:L,translateExtent:he,connectOnClick:Ue,defaultEdgeOptions:Je,fitView:We,fitViewOptions:qe,onNodesDelete:k,onEdgesDelete:A,onNodeDragStart:M,onNodeDrag:N,onNodeDragStop:P,onSelectionDrag:D,onSelectionDragStart:I,onSelectionDragStop:z,noPanClassName:je,nodeOrigin:ie,rfId:gt,autoPanOnConnect:ot,autoPanOnNodeDrag:rt,onError:st,connectionRadius:it,isValidConnection:at,nodeDragThreshold:ut}),t.createElement(ft,{onSelectionChange:O}),ke,t.createElement(R,{proOptions:Qe,position:Ge}),t.createElement(_t,{rfId:gt,disableKeyboardA11y:nt})))}));ss.displayName="ReactFlow";const ls=e=>e.domNode?.querySelector(".react-flow__edgelabel-renderer");const cs=e=>e.getNodes();const us=e=>e.edges;const ds=e=>({x:e.transform[0],y:e.transform[1],zoom:e.transform[2]});function hs(e){return n=>{const[o,r]=t.useState(n),i=t.useCallback((t=>r((n=>e(t,n)))),[]);return[o,r,i]}}const fs=hs(Zi),gs=hs(ji);const ps={includeHiddenNodes:!1};e.BaseEdge=ee,e.BezierEdge=_e,e.EdgeLabelRenderer=function({children:e}){const t=A(ls);return t?n.createPortal(e,t):null},e.EdgeText=z,e.Handle=et,e.Panel=D,e.ReactFlow=ss,e.ReactFlowProvider=Ja,e.SimpleBezierEdge=he,e.SmoothStepEdge=ye,e.StepEdge=ve,e.StraightEdge=Se,e.addEdge=De,e.applyEdgeChanges=ji,e.applyNodeChanges=Zi,e.boxToRect=F,e.clamp=$,e.getBezierPath=Ee,e.getBoundsOfRects=(e,t)=>F(Y(V(e),V(t))),e.getConnectedEdges=He,e.getIncomers=(e,t,n)=>{if(!Ae(e))return[];const o=n.filter((t=>t.target===e.id)).map((e=>e.source));return t.filter((e=>o.includes(e.id)))},e.getMarkerEnd=(e,t)=>void 0!==t&&t?`url(#${t})`:void 0!==e?`url(#react-flow__${e})`:"none",e.getNodePositionWithOrigin=$e,e.getNodesBounds=Be,e.getOutgoers=(e,t,n)=>{if(!Ae(e))return[];const o=n.filter((t=>t.source===e.id)).map((e=>e.target));return t.filter((e=>o.includes(e.id)))},e.getRectOfNodes=(e,t=[0,0])=>(console.warn("[DEPRECATED] `getRectOfNodes` is deprecated. Instead use `getNodesBounds` https://reactflow.dev/api-reference/utils/get-nodes-bounds."),Be(e,t)),e.getSimpleBezierPath=de,e.getSmoothStepPath=me,e.getStraightPath=be,e.getTransformForBounds=(e,t,n,o,r,i=.1)=>{const{x:a,y:s,zoom:l}=Xe(e,t,n,o,r,i);return console.warn("[DEPRECATED] `getTransformForBounds` is deprecated. Instead use `getViewportForBounds`. Beware that the return value is type Viewport (`{ x: number, y: number, zoom: number }`) instead of Transform (`[number, number, number]`). https://reactflow.dev/api-reference/utils/get-viewport-for-bounds"),[a,s,l]},e.getViewportForBounds=Xe,e.handleParentExpand=Fi,e.internalsSymbol=W,e.isEdge=ke,e.isNode=Ae,e.reconnectEdge=Re,e.rectToBox=V,e.updateEdge=(e,t,n,o={shouldReplaceId:!0})=>(console.warn("[DEPRECATED] `updateEdge` is deprecated. Instead use `reconnectEdge` https://reactflow.dev/api-reference/utils/reconnect-edge"),Re(e,t,n,o)),e.useEdges=function(){return A(us,Ce)},e.useEdgesState=gs,e.useGetPointerPosition=sa,e.useKeyPress=Ct,e.useNodeId=Pe,e.useNodes=function(){return A(cs,Ce)},e.useNodesInitialized=function(e=ps){return A((e=>t=>0!==t.nodeInternals.size&&t.getNodes().filter((t=>!!e.includeHiddenNodes||!t.hidden)).every((e=>void 0!==e[W]?.handleBounds)))(e))},e.useNodesState=fs,e.useOnSelectionChange=function({onChange:e}){const n=O();t.useEffect((()=>{const t=[...n.getState().onSelectionChange,e];return n.setState({onSelectionChange:t}),()=>{const t=n.getState().onSelectionChange.filter((t=>t!==e));n.setState({onSelectionChange:t})}}),[e])},e.useOnViewportChange=function({onStart:e,onChange:n,onEnd:o}){const r=O();t.useEffect((()=>{r.setState({onViewportChangeStart:e})}),[e]),t.useEffect((()=>{r.setState({onViewportChange:n})}),[n]),t.useEffect((()=>{r.setState({onViewportChangeEnd:o})}),[o])},e.useReactFlow=Di,e.useStore=A,e.useStoreApi=O,e.useUpdateNodeInternals=function(){const e=O();return t.useCallback((t=>{const{domNode:n,updateNodeDimensions:o}=e.getState(),r=(Array.isArray(t)?t:[t]).reduce(((e,t)=>{const o=n?.querySelector(`.react-flow__node[data-id="${t}"]`);return o&&e.push({id:t,nodeElement:o,forceUpdate:!0}),e}),[]);requestAnimationFrame((()=>o(r)))}),[])},e.useViewport=function(){return A(ds,Ce)}}));
