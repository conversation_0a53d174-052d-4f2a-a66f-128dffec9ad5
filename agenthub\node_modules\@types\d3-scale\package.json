{"name": "@types/d3-scale", "version": "4.0.9", "description": "TypeScript definitions for d3-scale", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-scale", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "githubUsername": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "r<PERSON><PERSON>", "githubUsername": "r<PERSON><PERSON>", "url": "https://github.com/rulonder"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-scale"}, "scripts": {}, "dependencies": {"@types/d3-time": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "c7eb4d63d0dfb659f0f8e359a4de33ed1f690bdea8051641bffa941f06a92f4d", "typeScriptVersion": "5.0"}