{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/store/useStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { Language } from '@/lib/i18n'\n\nexport interface WorkflowNode {\n  id: string\n  type: 'ai-agent' | 'input' | 'output' | 'condition' | 'action'\n  position: { x: number; y: number }\n  data: {\n    label: string\n    description?: string\n    config?: Record<string, any>\n  }\n}\n\nexport interface WorkflowEdge {\n  id: string\n  source: string\n  target: string\n  type?: string\n}\n\nexport interface Workflow {\n  id: string\n  name: string\n  description: string\n  nodes: WorkflowNode[]\n  edges: WorkflowEdge[]\n  createdAt: Date\n  updatedAt: Date\n}\n\nexport interface Agent {\n  id: string\n  name: string\n  description: string\n  type: 'autogen' | 'custom'\n  config: Record<string, any>\n  createdAt: Date\n}\n\nexport interface AIModel {\n  id: string\n  name: string\n  provider: string\n  apiKey: string\n  endpoint?: string\n  maxTokens: number\n  temperature: number\n  createdAt: Date\n}\n\ninterface AppState {\n  // Navigation\n  currentView: 'chat' | 'workflows' | 'marketplace' | 'agents' | 'settings'\n  setCurrentView: (view: AppState['currentView']) => void\n  \n  // Workflows\n  workflows: Workflow[]\n  currentWorkflow: Workflow | null\n  setCurrentWorkflow: (workflow: Workflow | null) => void\n  addWorkflow: (workflow: Workflow) => void\n  updateWorkflow: (id: string, updates: Partial<Workflow>) => void\n  deleteWorkflow: (id: string) => void\n  \n  // Agents\n  agents: Agent[]\n  addAgent: (agent: Agent) => void\n  updateAgent: (id: string, updates: Partial<Agent>) => void\n  deleteAgent: (id: string) => void\n\n  // AI Models\n  aiModels: AIModel[]\n  addAIModel: (model: AIModel) => void\n  updateAIModel: (id: string, updates: Partial<AIModel>) => void\n  deleteAIModel: (id: string) => void\n  \n  // UI State\n  sidebarCollapsed: boolean\n  setSidebarCollapsed: (collapsed: boolean) => void\n\n  // Language\n  language: Language\n  setLanguage: (language: Language) => void\n}\n\nexport const useStore = create<AppState>()(\n  persist(\n    (set, get) => ({\n  // Navigation\n  currentView: 'workflows',\n  setCurrentView: (view) => set({ currentView: view }),\n  \n  // Workflows\n  workflows: [],\n  currentWorkflow: null,\n  setCurrentWorkflow: (workflow) => set({ currentWorkflow: workflow }),\n  addWorkflow: (workflow) => set((state) => ({ \n    workflows: [...state.workflows, workflow] \n  })),\n  updateWorkflow: (id, updates) => set((state) => ({\n    workflows: state.workflows.map(w => \n      w.id === id ? { ...w, ...updates, updatedAt: new Date() } : w\n    ),\n    currentWorkflow: state.currentWorkflow?.id === id \n      ? { ...state.currentWorkflow, ...updates, updatedAt: new Date() }\n      : state.currentWorkflow\n  })),\n  deleteWorkflow: (id) => set((state) => ({\n    workflows: state.workflows.filter(w => w.id !== id),\n    currentWorkflow: state.currentWorkflow?.id === id ? null : state.currentWorkflow\n  })),\n  \n  // Agents\n  agents: [],\n  addAgent: (agent) => set((state) => ({\n    agents: [...state.agents, agent]\n  })),\n  updateAgent: (id, updates) => set((state) => ({\n    agents: state.agents.map(a =>\n      a.id === id ? { ...a, ...updates } : a\n    )\n  })),\n  deleteAgent: (id) => set((state) => ({\n    agents: state.agents.filter(a => a.id !== id)\n  })),\n\n  // AI Models\n  aiModels: [],\n  addAIModel: (model) => set((state) => ({\n    aiModels: [...state.aiModels, model]\n  })),\n  updateAIModel: (id, updates) => set((state) => ({\n    aiModels: state.aiModels.map(m =>\n      m.id === id ? { ...m, ...updates } : m\n    )\n  })),\n  deleteAIModel: (id) => set((state) => ({\n    aiModels: state.aiModels.filter(m => m.id !== id)\n  })),\n  \n  // UI State\n  sidebarCollapsed: false,\n  setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),\n\n  // Language\n  language: 'zh' as Language,\n  setLanguage: (language) => set({ language }),\n    }),\n    {\n      name: 'agenthub-storage',\n      partialize: (state) => ({\n        language: state.language,\n        sidebarCollapsed: state.sidebarCollapsed,\n        workflows: state.workflows,\n        agents: state.agents,\n        aiModels: state.aiModels,\n      }),\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAqFO,MAAM,WAAW,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC3B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACjB,aAAa;QACb,aAAa;QACb,gBAAgB,CAAC,OAAS,IAAI;gBAAE,aAAa;YAAK;QAElD,YAAY;QACZ,WAAW,EAAE;QACb,iBAAiB;QACjB,oBAAoB,CAAC,WAAa,IAAI;gBAAE,iBAAiB;YAAS;QAClE,aAAa,CAAC,WAAa,IAAI,CAAC,QAAU,CAAC;oBACzC,WAAW;2BAAI,MAAM,SAAS;wBAAE;qBAAS;gBAC3C,CAAC;QACD,gBAAgB,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC/C,WAAW,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,IAC7B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;4BAAE,WAAW,IAAI;wBAAO,IAAI;oBAE9D,iBAAiB,MAAM,eAAe,EAAE,OAAO,KAC3C;wBAAE,GAAG,MAAM,eAAe;wBAAE,GAAG,OAAO;wBAAE,WAAW,IAAI;oBAAO,IAC9D,MAAM,eAAe;gBAC3B,CAAC;QACD,gBAAgB,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACtC,WAAW,MAAM,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBAChD,iBAAiB,MAAM,eAAe,EAAE,OAAO,KAAK,OAAO,MAAM,eAAe;gBAClF,CAAC;QAED,SAAS;QACT,QAAQ,EAAE;QACV,UAAU,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACnC,QAAQ;2BAAI,MAAM,MAAM;wBAAE;qBAAM;gBAClC,CAAC;QACD,aAAa,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC5C,QAAQ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,IACvB,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QACD,aAAa,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACnC,QAAQ,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC5C,CAAC;QAED,YAAY;QACZ,UAAU,EAAE;QACZ,YAAY,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBACrC,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAM;gBACtC,CAAC;QACD,eAAe,CAAC,IAAI,UAAY,IAAI,CAAC,QAAU,CAAC;oBAC9C,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAA,IAC3B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEzC,CAAC;QACD,eAAe,CAAC,KAAO,IAAI,CAAC,QAAU,CAAC;oBACrC,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAChD,CAAC;QAED,WAAW;QACX,kBAAkB;QAClB,qBAAqB,CAAC,YAAc,IAAI;gBAAE,kBAAkB;YAAU;QAEtE,WAAW;QACX,UAAU;QACV,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;IACxC,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,UAAU,MAAM,QAAQ;YACxB,kBAAkB,MAAM,gBAAgB;YACxC,WAAW,MAAM,SAAS;YAC1B,QAAQ,MAAM,MAAM;YACpB,UAAU,MAAM,QAAQ;QAC1B,CAAC;AACH", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Layout/TopMenuBar.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Search, Bell, User, Settings } from 'lucide-react'\nimport * as Avatar from '@radix-ui/react-avatar'\nimport * as DropdownMenu from '@radix-ui/react-dropdown-menu'\n\nexport function TopMenuBar() {\n  return (\n    <div className=\"h-14 bg-white border-b border-gray-200 flex items-center justify-between px-6\">\n      {/* Logo and Title */}\n      <div className=\"flex items-center space-x-3\">\n        <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n          <span className=\"text-white font-bold text-sm\">AH</span>\n        </div>\n        <h1 className=\"text-xl font-semibold text-gray-900\">AgentHub</h1>\n      </div>\n      \n      {/* Search Bar */}\n      <div className=\"flex-1 max-w-md mx-8\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search workflows, agents...\"\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n        </div>\n      </div>\n      \n      {/* Right Actions */}\n      <div className=\"flex items-center space-x-4\">\n        {/* Notifications */}\n        <button className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\">\n          <Bell className=\"w-5 h-5\" />\n        </button>\n        \n        {/* User Menu */}\n        <DropdownMenu.Root>\n          <DropdownMenu.Trigger asChild>\n            <button className=\"flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\">\n              <Avatar.Root className=\"w-8 h-8\">\n                <Avatar.Image\n                  className=\"w-full h-full rounded-full object-cover\"\n                  src=\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80\"\n                  alt=\"User\"\n                />\n                <Avatar.Fallback className=\"w-full h-full rounded-full bg-gray-100 flex items-center justify-center text-gray-600 text-sm font-medium\">\n                  U\n                </Avatar.Fallback>\n              </Avatar.Root>\n              <span className=\"text-sm font-medium text-gray-700\">User</span>\n            </button>\n          </DropdownMenu.Trigger>\n          \n          <DropdownMenu.Portal>\n            <DropdownMenu.Content\n              className=\"min-w-[200px] bg-white rounded-lg shadow-lg border border-gray-200 p-1\"\n              sideOffset={5}\n            >\n              <DropdownMenu.Item className=\"flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md cursor-pointer\">\n                <User className=\"w-4 h-4 mr-2\" />\n                Profile\n              </DropdownMenu.Item>\n              <DropdownMenu.Item className=\"flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md cursor-pointer\">\n                <Settings className=\"w-4 h-4 mr-2\" />\n                Settings\n              </DropdownMenu.Item>\n              <DropdownMenu.Separator className=\"h-px bg-gray-200 my-1\" />\n              <DropdownMenu.Item className=\"flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md cursor-pointer\">\n                Sign out\n              </DropdownMenu.Item>\n            </DropdownMenu.Content>\n          </DropdownMenu.Portal>\n        </DropdownMenu.Root>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;AAOO,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAA+B;;;;;;;;;;;kCAEjD,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;0BAItD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAIlB,6LAAC,+KAAA,CAAA,OAAiB;;0CAChB,6LAAC,+KAAA,CAAA,UAAoB;gCAAC,OAAO;0CAC3B,cAAA,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,qKAAA,CAAA,OAAW;4CAAC,WAAU;;8DACrB,6LAAC,qKAAA,CAAA,QAAY;oDACX,WAAU;oDACV,KAAI;oDACJ,KAAI;;;;;;8DAEN,6LAAC,qKAAA,CAAA,WAAe;oDAAC,WAAU;8DAA4G;;;;;;;;;;;;sDAIzI,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAIxD,6LAAC,+KAAA,CAAA,SAAmB;0CAClB,cAAA,6LAAC,+KAAA,CAAA,UAAoB;oCACnB,WAAU;oCACV,YAAY;;sDAEZ,6LAAC,+KAAA,CAAA,OAAiB;4CAAC,WAAU;;8DAC3B,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,6LAAC,+KAAA,CAAA,OAAiB;4CAAC,WAAU;;8DAC3B,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGvC,6LAAC,+KAAA,CAAA,YAAsB;4CAAC,WAAU;;;;;;sDAClC,6LAAC,+KAAA,CAAA,OAAiB;4CAAC,WAAU;sDAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxI;KAvEgB", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/lib/i18n.ts"], "sourcesContent": ["// 多语言支持配置\nexport type Language = 'en' | 'zh'\n\nexport interface Translations {\n  // 导航菜单\n  nav: {\n    chat: string\n    workflows: string\n    marketplace: string\n    agents: string\n    settings: string\n  }\n  \n  // 聊天页面\n  chat: {\n    title: string\n    newChat: string\n    searchChats: string\n    noChats: string\n    noChatsDescription: string\n    startFirstChat: string\n    typeMessage: string\n    send: string\n    thinking: string\n  }\n  \n  // 工作流页面\n  workflows: {\n    title: string\n    description: string\n    newWorkflow: string\n    searchWorkflows: string\n    noWorkflows: string\n    noWorkflowsDescription: string\n    createFirstWorkflow: string\n    editor: string\n    list: string\n    save: string\n    run: string\n  }\n  \n  // 市场页面\n  marketplace: {\n    title: string\n    description: string\n    searchWorkflows: string\n    featured: string\n    popular: string\n    recent: string\n    saveToMyWorkflows: string\n    viewDetails: string\n    author: string\n    downloads: string\n    rating: string\n    tags: string\n    description_label: string\n    close: string\n  }\n  \n  // 智能体页面\n  agents: {\n    title: string\n    description: string\n    newAgent: string\n    searchAgents: string\n    noAgents: string\n    noAgentsDescription: string\n    createFirstAgent: string\n    demo: string\n    agents: string\n    type: string\n    model: string\n    temperature: string\n    context7: string\n    enabled: string\n    disabled: string\n    startDemo: string\n    stopDemo: string\n    enableContext7: string\n    libraries: string\n    status: string\n    messages: string\n    agentsCollaborating: string\n    readyToStart: string\n    readyDescription: string\n  }\n  \n  // 设置页面\n  settings: {\n    title: string\n    description: string\n    language: string\n    languageDescription: string\n    english: string\n    chinese: string\n    general: string\n    appearance: string\n    advanced: string\n    profile: string\n    notifications: string\n    security: string\n    integrations: string\n  }\n  \n  // 通用\n  common: {\n    save: string\n    cancel: string\n    delete: string\n    edit: string\n    create: string\n    search: string\n    loading: string\n    error: string\n    success: string\n    confirm: string\n    back: string\n    next: string\n    previous: string\n    close: string\n    open: string\n    view: string\n    download: string\n    upload: string\n    copy: string\n    paste: string\n    cut: string\n    undo: string\n    redo: string\n  }\n  \n  // 工作流编辑器\n  workflowEditor: {\n    title: string\n    addNode: string\n    deleteNode: string\n    connectNodes: string\n    nodeTypes: {\n      input: string\n      output: string\n      aiAgent: string\n      condition: string\n      action: string\n    }\n    properties: string\n    configuration: string\n  }\n}\n\n// 英文翻译\nexport const enTranslations: Translations = {\n  nav: {\n    chat: 'Chat',\n    workflows: 'Workflows',\n    marketplace: 'Marketplace',\n    agents: 'Agents',\n    settings: 'Settings'\n  },\n  \n  chat: {\n    title: 'Chat',\n    newChat: 'New Chat',\n    searchChats: 'Search chats...',\n    noChats: 'No conversations yet',\n    noChatsDescription: 'Start your first conversation with an AI agent to begin collaborating on your projects.',\n    startFirstChat: 'Start Your First Chat',\n    typeMessage: 'Type your message...',\n    send: 'Send',\n    thinking: 'AI is thinking...'\n  },\n  \n  workflows: {\n    title: 'My Workflows',\n    description: 'Create and manage your custom workflows',\n    newWorkflow: 'New Workflow',\n    searchWorkflows: 'Search workflows...',\n    noWorkflows: 'No workflows created',\n    noWorkflowsDescription: 'Create your first workflow to automate tasks with AI agents.',\n    createFirstWorkflow: 'Create Your First Workflow',\n    editor: 'Editor',\n    list: 'List',\n    save: 'Save',\n    run: 'Run'\n  },\n  \n  marketplace: {\n    title: 'Workflow Marketplace',\n    description: 'Discover and use workflows created by the community',\n    searchWorkflows: 'Search workflows...',\n    featured: 'Featured',\n    popular: 'Popular',\n    recent: 'Recent',\n    saveToMyWorkflows: 'Save to My Workflows',\n    viewDetails: 'View Details',\n    author: 'Author',\n    downloads: 'Downloads',\n    rating: 'Rating',\n    tags: 'Tags',\n    description_label: 'Description',\n    close: 'Close'\n  },\n  \n  agents: {\n    title: 'AI Agents',\n    description: 'Manage and configure your AI agents',\n    newAgent: 'New Agent',\n    searchAgents: 'Search agents...',\n    noAgents: 'No agents configured',\n    noAgentsDescription: 'Create your first AI agent to start automating tasks. Choose from AutoGen templates or build custom agents.',\n    createFirstAgent: 'Create Your First Agent',\n    demo: 'Demo',\n    agents: 'Agents',\n    type: 'Type',\n    model: 'Model',\n    temperature: 'Temperature',\n    context7: 'Context7',\n    enabled: 'Enabled',\n    disabled: 'Disabled',\n    startDemo: 'Start Demo',\n    stopDemo: 'Stop Demo',\n    enableContext7: 'Enable Context7 Enhancement',\n    libraries: 'Context7 Libraries',\n    status: 'Status',\n    messages: 'Messages',\n    agentsCollaborating: 'Agents are collaborating...',\n    readyToStart: 'Ready to Start Demo',\n    readyDescription: 'Click \"Start Demo\" to see AutoGen agents collaborate on creating AI workflow documentation, enhanced with Context7\\'s up-to-date libraries.'\n  },\n  \n  settings: {\n    title: 'Settings',\n    description: 'Configure your preferences and application settings',\n    language: 'Language',\n    languageDescription: 'Choose your preferred language for the interface',\n    english: 'English',\n    chinese: '中文',\n    general: 'General',\n    appearance: 'Appearance',\n    advanced: 'Advanced',\n    profile: 'Profile',\n    notifications: 'Notifications',\n    security: 'Security',\n    integrations: 'Integrations'\n  },\n  \n  common: {\n    save: 'Save',\n    cancel: 'Cancel',\n    delete: 'Delete',\n    edit: 'Edit',\n    create: 'Create',\n    search: 'Search',\n    loading: 'Loading...',\n    error: 'Error',\n    success: 'Success',\n    confirm: 'Confirm',\n    back: 'Back',\n    next: 'Next',\n    previous: 'Previous',\n    close: 'Close',\n    open: 'Open',\n    view: 'View',\n    download: 'Download',\n    upload: 'Upload',\n    copy: 'Copy',\n    paste: 'Paste',\n    cut: 'Cut',\n    undo: 'Undo',\n    redo: 'Redo'\n  },\n  \n  workflowEditor: {\n    title: 'Workflow Editor',\n    addNode: 'Add Node',\n    deleteNode: 'Delete Node',\n    connectNodes: 'Connect Nodes',\n    nodeTypes: {\n      input: 'Input',\n      output: 'Output',\n      aiAgent: 'AI Agent',\n      condition: 'Condition',\n      action: 'Action'\n    },\n    properties: 'Properties',\n    configuration: 'Configuration'\n  }\n}\n\n// 中文翻译\nexport const zhTranslations: Translations = {\n  nav: {\n    chat: '聊天',\n    workflows: '我的工作流',\n    marketplace: '工作流市场',\n    agents: '我的AI-Agent',\n    settings: '设置'\n  },\n\n  chat: {\n    title: '聊天',\n    newChat: '新建聊天',\n    searchChats: '搜索聊天记录...',\n    noChats: '暂无对话',\n    noChatsDescription: '开始与AI智能体的第一次对话，开始协作处理您的项目。',\n    startFirstChat: '开始第一次聊天',\n    typeMessage: '输入您的消息...',\n    send: '发送',\n    thinking: 'AI正在思考中...'\n  },\n\n  workflows: {\n    title: '我的工作流',\n    description: '创建和管理您的自定义工作流',\n    newWorkflow: '新建工作流',\n    searchWorkflows: '搜索工作流...',\n    noWorkflows: '暂无工作流',\n    noWorkflowsDescription: '创建您的第一个工作流，使用AI智能体自动化任务。',\n    createFirstWorkflow: '创建第一个工作流',\n    editor: '编辑器',\n    list: '列表',\n    save: '保存',\n    run: '运行'\n  },\n\n  marketplace: {\n    title: '工作流市场',\n    description: '发现并使用社区创建的工作流',\n    searchWorkflows: '搜索工作流...',\n    featured: '精选',\n    popular: '热门',\n    recent: '最新',\n    saveToMyWorkflows: '保存到我的工作流',\n    viewDetails: '查看详情',\n    author: '作者',\n    downloads: '下载量',\n    rating: '评分',\n    tags: '标签',\n    description_label: '描述',\n    close: '关闭'\n  },\n\n  agents: {\n    title: '我的AI-Agent',\n    description: '管理和配置您的AI智能体',\n    newAgent: '新建智能体',\n    searchAgents: '搜索智能体...',\n    noAgents: '暂无智能体',\n    noAgentsDescription: '创建您的第一个AI智能体来开始自动化任务。从AutoGen模板中选择或构建自定义智能体。',\n    createFirstAgent: '创建第一个智能体',\n    demo: '演示',\n    agents: '智能体',\n    type: '类型',\n    model: '模型',\n    temperature: '温度',\n    context7: 'Context7',\n    enabled: '已启用',\n    disabled: '已禁用',\n    startDemo: '开始演示',\n    stopDemo: '停止演示',\n    enableContext7: '启用Context7增强',\n    libraries: 'Context7库',\n    status: '状态',\n    messages: '消息',\n    agentsCollaborating: '智能体正在协作中...',\n    readyToStart: '准备开始演示',\n    readyDescription: '点击\"开始演示\"查看AutoGen智能体协作创建AI工作流文档，并使用Context7的最新库进行增强。'\n  },\n\n  settings: {\n    title: '设置',\n    description: '配置您的偏好和应用程序设置',\n    language: '语言',\n    languageDescription: '选择您偏好的界面语言',\n    english: 'English',\n    chinese: '中文',\n    general: '通用',\n    appearance: '外观',\n    advanced: '高级',\n    profile: '个人资料',\n    notifications: '通知',\n    security: '安全',\n    integrations: '集成'\n  },\n\n  common: {\n    save: '保存',\n    cancel: '取消',\n    delete: '删除',\n    edit: '编辑',\n    create: '创建',\n    search: '搜索',\n    loading: '加载中...',\n    error: '错误',\n    success: '成功',\n    confirm: '确认',\n    back: '返回',\n    next: '下一步',\n    previous: '上一步',\n    close: '关闭',\n    open: '打开',\n    view: '查看',\n    download: '下载',\n    upload: '上传',\n    copy: '复制',\n    paste: '粘贴',\n    cut: '剪切',\n    undo: '撤销',\n    redo: '重做'\n  },\n\n  workflowEditor: {\n    title: '工作流编辑器',\n    addNode: '添加节点',\n    deleteNode: '删除节点',\n    connectNodes: '连接节点',\n    nodeTypes: {\n      input: '输入',\n      output: '输出',\n      aiAgent: 'AI智能体',\n      condition: '条件',\n      action: '动作'\n    },\n    properties: '属性',\n    configuration: '配置'\n  }\n}\n\n// 语言配置\nexport const translations = {\n  en: enTranslations,\n  zh: zhTranslations\n}\n\n// 获取翻译文本的辅助函数\nexport function getTranslation(language: Language, key: string): string {\n  const keys = key.split('.')\n  let value: any = translations[language]\n\n  for (const k of keys) {\n    value = value?.[k]\n  }\n\n  return value || key\n}\n\n// 创建翻译钩子\nexport function createTranslationHook(language: Language) {\n  return function t(key: string): string {\n    return getTranslation(language, key)\n  }\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;;AAsJH,MAAM,iBAA+B;IAC1C,KAAK;QACH,MAAM;QACN,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;IACZ;IAEA,MAAM;QACJ,OAAO;QACP,SAAS;QACT,aAAa;QACb,SAAS;QACT,oBAAoB;QACpB,gBAAgB;QAChB,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IAEA,WAAW;QACT,OAAO;QACP,aAAa;QACb,aAAa;QACb,iBAAiB;QACjB,aAAa;QACb,wBAAwB;QACxB,qBAAqB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,KAAK;IACP;IAEA,aAAa;QACX,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,SAAS;QACT,QAAQ;QACR,mBAAmB;QACnB,aAAa;QACb,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,MAAM;QACN,mBAAmB;QACnB,OAAO;IACT;IAEA,QAAQ;QACN,OAAO;QACP,aAAa;QACb,UAAU;QACV,cAAc;QACd,UAAU;QACV,qBAAqB;QACrB,kBAAkB;QAClB,MAAM;QACN,QAAQ;QACR,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;QACV,SAAS;QACT,UAAU;QACV,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,WAAW;QACX,QAAQ;QACR,UAAU;QACV,qBAAqB;QACrB,cAAc;QACd,kBAAkB;IACpB;IAEA,UAAU;QACR,OAAO;QACP,aAAa;QACb,UAAU;QACV,qBAAqB;QACrB,SAAS;QACT,SAAS;QACT,SAAS;QACT,YAAY;QACZ,UAAU;QACV,SAAS;QACT,eAAe;QACf,UAAU;QACV,cAAc;IAChB;IAEA,QAAQ;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,MAAM;IACR;IAEA,gBAAgB;QACd,OAAO;QACP,SAAS;QACT,YAAY;QACZ,cAAc;QACd,WAAW;YACT,OAAO;YACP,QAAQ;YACR,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA,YAAY;QACZ,eAAe;IACjB;AACF;AAGO,MAAM,iBAA+B;IAC1C,KAAK;QACH,MAAM;QACN,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;IACZ;IAEA,MAAM;QACJ,OAAO;QACP,SAAS;QACT,aAAa;QACb,SAAS;QACT,oBAAoB;QACpB,gBAAgB;QAChB,aAAa;QACb,MAAM;QACN,UAAU;IACZ;IAEA,WAAW;QACT,OAAO;QACP,aAAa;QACb,aAAa;QACb,iBAAiB;QACjB,aAAa;QACb,wBAAwB;QACxB,qBAAqB;QACrB,QAAQ;QACR,MAAM;QACN,MAAM;QACN,KAAK;IACP;IAEA,aAAa;QACX,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,UAAU;QACV,SAAS;QACT,QAAQ;QACR,mBAAmB;QACnB,aAAa;QACb,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,MAAM;QACN,mBAAmB;QACnB,OAAO;IACT;IAEA,QAAQ;QACN,OAAO;QACP,aAAa;QACb,UAAU;QACV,cAAc;QACd,UAAU;QACV,qBAAqB;QACrB,kBAAkB;QAClB,MAAM;QACN,QAAQ;QACR,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;QACV,SAAS;QACT,UAAU;QACV,WAAW;QACX,UAAU;QACV,gBAAgB;QAChB,WAAW;QACX,QAAQ;QACR,UAAU;QACV,qBAAqB;QACrB,cAAc;QACd,kBAAkB;IACpB;IAEA,UAAU;QACR,OAAO;QACP,aAAa;QACb,UAAU;QACV,qBAAqB;QACrB,SAAS;QACT,SAAS;QACT,SAAS;QACT,YAAY;QACZ,UAAU;QACV,SAAS;QACT,eAAe;QACf,UAAU;QACV,cAAc;IAChB;IAEA,QAAQ;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,MAAM;IACR;IAEA,gBAAgB;QACd,OAAO;QACP,SAAS;QACT,YAAY;QACZ,cAAc;QACd,WAAW;YACT,OAAO;YACP,QAAQ;YACR,SAAS;YACT,WAAW;YACX,QAAQ;QACV;QACA,YAAY;QACZ,eAAe;IACjB;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,eAAe,QAAkB,EAAE,GAAW;IAC5D,MAAM,OAAO,IAAI,KAAK,CAAC;IACvB,IAAI,QAAa,YAAY,CAAC,SAAS;IAEvC,KAAK,MAAM,KAAK,KAAM;QACpB,QAAQ,OAAO,CAAC,EAAE;IACpB;IAEA,OAAO,SAAS;AAClB;AAGO,SAAS,sBAAsB,QAAkB;IACtD,OAAO,SAAS,EAAE,GAAW;QAC3B,OAAO,eAAe,UAAU;IAClC;AACF", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/hooks/useTranslation.ts"], "sourcesContent": ["import { useStore } from '@/store/useStore'\nimport { createTranslationHook } from '@/lib/i18n'\n\nexport function useTranslation() {\n  const language = useStore((state) => state.language)\n  return createTranslationHook(language)\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAEO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;6CAAE,CAAC,QAAU,MAAM,QAAQ;;IACnD,OAAO,CAAA,GAAA,qHAAA,CAAA,wBAAqB,AAAD,EAAE;AAC/B;GAHgB;;QACG,2HAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Layout/LeftNavigation.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useStore } from '@/store/useStore'\nimport {\n  MessageSquare,\n  Workflow,\n  Store,\n  Bot,\n  Settings,\n  ChevronLeft,\n  ChevronRight\n} from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { useTranslation } from '@/hooks/useTranslation'\n\nconst navigationItems = [\n  { id: 'chat', labelKey: 'nav.chat', icon: MessageSquare },\n  { id: 'workflows', labelKey: 'nav.workflows', icon: Workflow },\n  { id: 'marketplace', labelKey: 'nav.marketplace', icon: Store },\n  { id: 'agents', labelKey: 'nav.agents', icon: Bot },\n  { id: 'settings', labelKey: 'nav.settings', icon: Settings },\n] as const\n\nexport function LeftNavigation() {\n  const { currentView, setCurrentView, sidebarCollapsed, setSidebarCollapsed } = useStore()\n  const t = useTranslation()\n\n  return (\n    <div className={cn(\n      \"fixed left-0 top-14 h-[calc(100vh-3.5rem)] bg-white border-r border-gray-200 transition-all duration-300 z-10\",\n      sidebarCollapsed ? \"w-16\" : \"w-64\"\n    )}>\n      {/* Collapse Toggle */}\n      <div className=\"flex justify-end p-2 border-b border-gray-100\">\n        <button\n          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}\n          className=\"p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors\"\n        >\n          {sidebarCollapsed ? (\n            <ChevronRight className=\"w-4 h-4\" />\n          ) : (\n            <ChevronLeft className=\"w-4 h-4\" />\n          )}\n        </button>\n      </div>\n      \n      {/* Navigation Items */}\n      <nav className=\"p-2 space-y-1\">\n        {navigationItems.map((item) => {\n          const Icon = item.icon\n          const isActive = currentView === item.id\n          \n          return (\n            <button\n              key={item.id}\n              onClick={() => setCurrentView(item.id)}\n              className={cn(\n                \"w-full flex items-center text-left transition-colors rounded-lg\",\n                sidebarCollapsed ? \"p-3 justify-center\" : \"p-3 space-x-3\",\n                isActive\n                  ? \"bg-blue-50 text-blue-700 border border-blue-200\"\n                  : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"\n              )}\n              title={sidebarCollapsed ? t(item.labelKey) : undefined}\n            >\n              <Icon className={cn(\n                \"flex-shrink-0\",\n                sidebarCollapsed ? \"w-5 h-5\" : \"w-5 h-5\"\n              )} />\n              {!sidebarCollapsed && (\n                <span className=\"font-medium\">{t(item.labelKey)}</span>\n              )}\n            </button>\n          )\n        })}\n      </nav>\n      \n      {/* Quick Actions */}\n      {!sidebarCollapsed && (\n        <div className=\"absolute bottom-4 left-2 right-2\">\n          <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white\">\n            <h3 className=\"font-semibold text-sm mb-1\">Create New Workflow</h3>\n            <p className=\"text-xs text-blue-100 mb-3\">\n              Build powerful AI-driven workflows\n            </p>\n            <button className=\"w-full bg-white/20 hover:bg-white/30 text-white text-xs font-medium py-2 px-3 rounded-md transition-colors\">\n              Get Started\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAdA;;;;;AAgBA,MAAM,kBAAkB;IACtB;QAAE,IAAI;QAAQ,UAAU;QAAY,MAAM,2NAAA,CAAA,gBAAa;IAAC;IACxD;QAAE,IAAI;QAAa,UAAU;QAAiB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAC7D;QAAE,IAAI;QAAe,UAAU;QAAmB,MAAM,uMAAA,CAAA,QAAK;IAAC;IAC9D;QAAE,IAAI;QAAU,UAAU;QAAc,MAAM,mMAAA,CAAA,MAAG;IAAC;IAClD;QAAE,IAAI;QAAY,UAAU;QAAgB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAC5D;AAEM,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IACtF,MAAM,IAAI,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iHACA,mBAAmB,SAAS;;0BAG5B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS,IAAM,oBAAoB,CAAC;oBACpC,WAAU;8BAET,iCACC,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;6CAExB,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAM7B,6LAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC;oBACpB,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,WAAW,gBAAgB,KAAK,EAAE;oBAExC,qBACE,6LAAC;wBAEC,SAAS,IAAM,eAAe,KAAK,EAAE;wBACrC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mEACA,mBAAmB,uBAAuB,iBAC1C,WACI,oDACA;wBAEN,OAAO,mBAAmB,EAAE,KAAK,QAAQ,IAAI;;0CAE7C,6LAAC;gCAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,iBACA,mBAAmB,YAAY;;;;;;4BAEhC,CAAC,kCACA,6LAAC;gCAAK,WAAU;0CAAe,EAAE,KAAK,QAAQ;;;;;;;uBAhB3C,KAAK,EAAE;;;;;gBAoBlB;;;;;;YAID,CAAC,kCACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,6LAAC;4BAAO,WAAU;sCAA6G;;;;;;;;;;;;;;;;;;;;;;;AAQ3I;GAtEgB;;QACiE,2HAAA,CAAA,WAAQ;QAC7E,iIAAA,CAAA,iBAAc;;;KAFV", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Views/ChatView.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Send, Bot, User } from 'lucide-react'\nimport { useTranslation } from '@/hooks/useTranslation'\n\ninterface Message {\n  id: string\n  type: 'user' | 'assistant'\n  content: string\n  timestamp: Date\n}\n\nexport function ChatView() {\n  const t = useTranslation()\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: '1',\n      type: 'assistant',\n      content: 'Hello! I\\'m your AI assistant. I can help you create workflows, manage agents, and answer questions about your automation platform. How can I assist you today?',\n      timestamp: new Date(),\n    }\n  ])\n  const [inputValue, setInputValue] = useState('')\n\n  const handleSendMessage = () => {\n    if (!inputValue.trim()) return\n\n    const newMessage: Message = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputValue,\n      timestamp: new Date(),\n    }\n\n    setMessages(prev => [...prev, newMessage])\n    setInputValue('')\n\n    // Simulate AI response\n    setTimeout(() => {\n      const aiResponse: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'assistant',\n        content: 'I understand you want to work with workflows. Let me help you with that. You can create new workflows from the Workflows section, or I can guide you through the process step by step.',\n        timestamp: new Date(),\n      }\n      setMessages(prev => [...prev, aiResponse])\n    }, 1000)\n  }\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault()\n      handleSendMessage()\n    }\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 p-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">{t('chat.title')}</h1>\n        <p className=\"text-gray-600 mt-1\">\n          Chat with your AI assistant to get help with workflows and automation\n        </p>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-6 space-y-4\">\n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={`flex items-start space-x-3 ${\n              message.type === 'user' ? 'justify-end' : 'justify-start'\n            }`}\n          >\n            {message.type === 'assistant' && (\n              <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                <Bot className=\"w-4 h-4 text-blue-600\" />\n              </div>\n            )}\n            \n            <div\n              className={`max-w-2xl rounded-lg p-4 ${\n                message.type === 'user'\n                  ? 'bg-blue-600 text-white'\n                  : 'bg-white border border-gray-200'\n              }`}\n            >\n              <p className={`text-sm ${message.type === 'user' ? 'text-white' : 'text-black'}`}>{message.content}</p>\n              <p\n                className={`text-xs mt-2 ${\n                  message.type === 'user' ? 'text-blue-100' : 'text-gray-500'\n                }`}\n              >\n                {message.timestamp.toLocaleTimeString()}\n              </p>\n            </div>\n            \n            {message.type === 'user' && (\n              <div className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                <User className=\"w-4 h-4 text-gray-600\" />\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Input */}\n      <div className=\"bg-white border-t border-gray-200 p-6\">\n        <div className=\"flex items-end space-x-4\">\n          <div className=\"flex-1\">\n            <textarea\n              value={inputValue}\n              onChange={(e) => setInputValue(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder={t('chat.typeMessage')}\n              className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black\"\n              rows={3}\n            />\n          </div>\n          <button\n            onClick={handleSendMessage}\n            disabled={!inputValue.trim()}\n            className=\"p-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white rounded-lg transition-colors\"\n          >\n            <Send className=\"w-5 h-5\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAaO,SAAS;;IACd,MAAM,IAAI,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,oBAAoB;QACxB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;QACzC,cAAc;QAEd,uBAAuB;QACvB,WAAW;YACT,MAAM,aAAsB;gBAC1B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;QAC3C,GAAG;IACL;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC,EAAE;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAMpC,6LAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;wBAEC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAC1C;;4BAED,QAAQ,IAAI,KAAK,6BAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAInB,6LAAC;gCACC,WAAW,CAAC,yBAAyB,EACnC,QAAQ,IAAI,KAAK,SACb,2BACA,mCACJ;;kDAEF,6LAAC;wCAAE,WAAW,CAAC,QAAQ,EAAE,QAAQ,IAAI,KAAK,SAAS,eAAe,cAAc;kDAAG,QAAQ,OAAO;;;;;;kDAClG,6LAAC;wCACC,WAAW,CAAC,aAAa,EACvB,QAAQ,IAAI,KAAK,SAAS,kBAAkB,iBAC5C;kDAED,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;4BAIxC,QAAQ,IAAI,KAAK,wBAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;uBA9Bf,QAAQ,EAAE;;;;;;;;;;0BAsCrB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,YAAY;gCACZ,aAAa,EAAE;gCACf,WAAU;gCACV,MAAM;;;;;;;;;;;sCAGV,6LAAC;4BACC,SAAS;4BACT,UAAU,CAAC,WAAW,IAAI;4BAC1B,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B;GAvHgB;;QACJ,iIAAA,CAAA,iBAAc;;;KADV", "debugId": null}}, {"offset": {"line": 1118, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/WorkflowEditor/nodes/AIAgentNode.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'\nimport { <PERSON><PERSON>, Settings } from 'lucide-react'\n\ninterface AIAgentNodeData {\n  label: string\n  description?: string\n  agentType: 'autogen' | 'custom'\n  config?: {\n    model?: string\n    temperature?: number\n    maxTokens?: number\n    context7Libraries?: string[]\n    systemMessage?: string\n  }\n}\n\nexport function AIAgentNode({ data }: NodeProps<AIAgentNodeData>) {\n  return (\n    <div className=\"bg-white border-2 border-blue-300 rounded-lg shadow-md min-w-[200px]\">\n      <Handle type=\"target\" position={Position.Left} className=\"w-3 h-3\" />\n      \n      <div className=\"p-4\">\n        <div className=\"flex items-center space-x-2 mb-2\">\n          <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n            <Bot className=\"w-4 h-4 text-blue-600\" />\n          </div>\n          <div className=\"flex-1\">\n            <h3 className=\"font-semibold text-gray-900 text-sm\">{data.label}</h3>\n            <span className=\"text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded\">\n              AI Agent\n            </span>\n          </div>\n          <button className=\"p-1 text-gray-400 hover:text-gray-600\">\n            <Settings className=\"w-3 h-3\" />\n          </button>\n        </div>\n        \n        {data.description && (\n          <p className=\"text-xs text-gray-600 mb-2\">{data.description}</p>\n        )}\n        \n        <div className=\"space-y-1\">\n          <div className=\"flex justify-between text-xs\">\n            <span className=\"text-gray-500\">Type:</span>\n            <span className=\"font-medium capitalize\">{data.agentType}</span>\n          </div>\n          {data.config?.model && (\n            <div className=\"flex justify-between text-xs\">\n              <span className=\"text-gray-500\">Model:</span>\n              <span className=\"font-medium\">{data.config.model}</span>\n            </div>\n          )}\n          {data.config?.temperature !== undefined && (\n            <div className=\"flex justify-between text-xs\">\n              <span className=\"text-gray-500\">Temperature:</span>\n              <span className=\"font-medium\">{data.config.temperature}</span>\n            </div>\n          )}\n          {data.config?.context7Libraries && data.config.context7Libraries.length > 0 && (\n            <div className=\"flex justify-between text-xs\">\n              <span className=\"text-gray-500\">Context7:</span>\n              <span className=\"font-medium text-green-600\">Enabled</span>\n            </div>\n          )}\n        </div>\n      </div>\n      \n      <Handle type=\"source\" position={Position.Right} className=\"w-3 h-3\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAJA;;;;AAmBO,SAAS,YAAY,EAAE,IAAI,EAA8B;IAC9D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,IAAI;gBAAE,WAAU;;;;;;0BAEzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAuC,KAAK,KAAK;;;;;;kDAC/D,6LAAC;wCAAK,WAAU;kDAAuD;;;;;;;;;;;;0CAIzE,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAIvB,KAAK,WAAW,kBACf,6LAAC;wBAAE,WAAU;kCAA8B,KAAK,WAAW;;;;;;kCAG7D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAA0B,KAAK,SAAS;;;;;;;;;;;;4BAEzD,KAAK,MAAM,EAAE,uBACZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAe,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;4BAGnD,KAAK,MAAM,EAAE,gBAAgB,2BAC5B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAAe,KAAK,MAAM,CAAC,WAAW;;;;;;;;;;;;4BAGzD,KAAK,MAAM,EAAE,qBAAqB,KAAK,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,mBACxE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,6LAAC;wCAAK,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;0BAMrD,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,KAAK;gBAAE,WAAU;;;;;;;;;;;;AAGhE;KAtDgB", "debugId": null}}, {"offset": {"line": 1358, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/WorkflowEditor/nodes/InputNode.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Handle, Position, NodeProps } from 'reactflow'\nimport { Play } from 'lucide-react'\n\ninterface InputNodeData {\n  label: string\n}\n\nexport function InputNode({ data }: NodeProps<InputNodeData>) {\n  return (\n    <div className=\"bg-white border-2 border-green-300 rounded-lg shadow-md min-w-[120px]\">\n      <div className=\"p-3\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center\">\n            <Play className=\"w-3 h-3 text-green-600\" />\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900 text-sm\">{data.label}</h3>\n            <span className=\"text-xs text-green-600 bg-green-50 px-2 py-0.5 rounded\">\n              Input\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <Handle type=\"source\" position={Position.Right} className=\"w-3 h-3\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUO,SAAS,UAAU,EAAE,IAAI,EAA4B;IAC1D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAuC,KAAK,KAAK;;;;;;8CAC/D,6LAAC;oCAAK,WAAU;8CAAyD;;;;;;;;;;;;;;;;;;;;;;;0BAO/E,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,KAAK;gBAAE,WAAU;;;;;;;;;;;;AAGhE;KApBgB", "debugId": null}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/WorkflowEditor/nodes/OutputNode.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Handle, Position, NodeProps } from 'reactflow'\nimport { Flag } from 'lucide-react'\n\ninterface OutputNodeData {\n  label: string\n}\n\nexport function OutputNode({ data }: NodeProps<OutputNodeData>) {\n  return (\n    <div className=\"bg-white border-2 border-red-300 rounded-lg shadow-md min-w-[120px]\">\n      <Handle type=\"target\" position={Position.Left} className=\"w-3 h-3\" />\n      \n      <div className=\"p-3\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-6 h-6 bg-red-100 rounded-full flex items-center justify-center\">\n            <Flag className=\"w-3 h-3 text-red-600\" />\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900 text-sm\">{data.label}</h3>\n            <span className=\"text-xs text-red-600 bg-red-50 px-2 py-0.5 rounded\">\n              Output\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUO,SAAS,WAAW,EAAE,IAAI,EAA6B;IAC5D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,IAAI;gBAAE,WAAU;;;;;;0BAEzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAuC,KAAK,KAAK;;;;;;8CAC/D,6LAAC;oCAAK,WAAU;8CAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;KApBgB", "debugId": null}}, {"offset": {"line": 1550, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/WorkflowEditor/nodes/ConditionNode.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Handle, Position, NodeProps } from 'reactflow'\nimport { GitBranch } from 'lucide-react'\n\ninterface ConditionNodeData {\n  label: string\n}\n\nexport function ConditionNode({ data }: NodeProps<ConditionNodeData>) {\n  return (\n    <div className=\"bg-white border-2 border-yellow-300 rounded-lg shadow-md min-w-[140px]\">\n      <Handle type=\"target\" position={Position.Left} className=\"w-3 h-3\" />\n      \n      <div className=\"p-3\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center\">\n            <GitBranch className=\"w-3 h-3 text-yellow-600\" />\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900 text-sm\">{data.label}</h3>\n            <span className=\"text-xs text-yellow-600 bg-yellow-50 px-2 py-0.5 rounded\">\n              Condition\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <Handle type=\"source\" position={Position.Right} id=\"true\" className=\"w-3 h-3\" style={{ top: '30%' }} />\n      <Handle type=\"source\" position={Position.Right} id=\"false\" className=\"w-3 h-3\" style={{ top: '70%' }} />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUO,SAAS,cAAc,EAAE,IAAI,EAAgC;IAClE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,IAAI;gBAAE,WAAU;;;;;;0BAEzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAuC,KAAK,KAAK;;;;;;8CAC/D,6LAAC;oCAAK,WAAU;8CAA2D;;;;;;;;;;;;;;;;;;;;;;;0BAOjF,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,KAAK;gBAAE,IAAG;gBAAO,WAAU;gBAAU,OAAO;oBAAE,KAAK;gBAAM;;;;;;0BAClG,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,KAAK;gBAAE,IAAG;gBAAQ,WAAU;gBAAU,OAAO;oBAAE,KAAK;gBAAM;;;;;;;;;;;;AAGzG;KAvBgB", "debugId": null}}, {"offset": {"line": 1672, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/WorkflowEditor/nodes/ActionNode.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Handle, Position, NodeProps } from 'reactflow'\nimport { Zap } from 'lucide-react'\n\ninterface ActionNodeData {\n  label: string\n}\n\nexport function ActionNode({ data }: NodeProps<ActionNodeData>) {\n  return (\n    <div className=\"bg-white border-2 border-purple-300 rounded-lg shadow-md min-w-[140px]\">\n      <Handle type=\"target\" position={Position.Left} className=\"w-3 h-3\" />\n      \n      <div className=\"p-3\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center\">\n            <Zap className=\"w-3 h-3 text-purple-600\" />\n          </div>\n          <div>\n            <h3 className=\"font-semibold text-gray-900 text-sm\">{data.label}</h3>\n            <span className=\"text-xs text-purple-600 bg-purple-50 px-2 py-0.5 rounded\">\n              Action\n            </span>\n          </div>\n        </div>\n      </div>\n      \n      <Handle type=\"source\" position={Position.Right} className=\"w-3 h-3\" />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUO,SAAS,WAAW,EAAE,IAAI,EAA6B;IAC5D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,IAAI;gBAAE,WAAU;;;;;;0BAEzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAuC,KAAK,KAAK;;;;;;8CAC/D,6LAAC;oCAAK,WAAU;8CAA2D;;;;;;;;;;;;;;;;;;;;;;;0BAOjF,6LAAC,+JAAA,CAAA,SAAM;gBAAC,MAAK;gBAAS,UAAU,+JAAA,CAAA,WAAQ,CAAC,KAAK;gBAAE,WAAU;;;;;;;;;;;;AAGhE;KAtBgB", "debugId": null}}, {"offset": {"line": 1777, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/WorkflowEditor/WorkflowEditor.tsx"], "sourcesContent": ["'use client'\n\nimport React, { use<PERSON><PERSON>back, useState } from 'react'\nimport <PERSON>act<PERSON><PERSON>, {\n  Node,\n  Edge,\n  addEdge,\n  Connection,\n  useNodesState,\n  useEdgesState,\n  Controls,\n  Background,\n  BackgroundVariant,\n} from 'reactflow'\nimport 'reactflow/dist/style.css'\n\nimport { AIAgentNode } from './nodes/AIAgentNode'\nimport { InputNode } from './nodes/InputNode'\nimport { OutputNode } from './nodes/OutputNode'\nimport { ConditionNode } from './nodes/ConditionNode'\nimport { ActionNode } from './nodes/ActionNode'\n\nconst nodeTypes = {\n  'ai-agent': AIAgentNode,\n  'input': InputNode,\n  'output': OutputNode,\n  'condition': ConditionNode,\n  'action': ActionNode,\n}\n\nconst initialNodes: Node[] = [\n  {\n    id: '1',\n    type: 'input',\n    position: { x: 100, y: 100 },\n    data: { label: 'Start' },\n  },\n  {\n    id: '2',\n    type: 'ai-agent',\n    position: { x: 300, y: 100 },\n    data: {\n      label: 'Content Analyzer',\n      description: 'AI agent that analyzes content using AutoGen with Context7',\n      agentType: 'autogen',\n      config: {\n        model: 'gpt-4',\n        temperature: 0.7,\n        maxTokens: 1000,\n        context7Libraries: ['/microsoft/autogen', '/openai/openai-node'],\n        systemMessage: 'You are a content analysis expert with access to up-to-date documentation.'\n      }\n    },\n  },\n  {\n    id: '3',\n    type: 'condition',\n    position: { x: 500, y: 100 },\n    data: { label: 'Quality Check' },\n  },\n  {\n    id: '4',\n    type: 'action',\n    position: { x: 700, y: 50 },\n    data: { label: 'Approve Content' },\n  },\n  {\n    id: '5',\n    type: 'ai-agent',\n    position: { x: 700, y: 150 },\n    data: {\n      label: 'Content Improver',\n      description: 'AI agent that improves content quality with Context7 docs',\n      agentType: 'autogen',\n      config: {\n        model: 'gpt-4',\n        temperature: 0.5,\n        maxTokens: 1500,\n        context7Libraries: ['/vercel/next.js', '/tailwindlabs/tailwindcss'],\n        systemMessage: 'You are a content improvement specialist with access to latest web development practices.'\n      }\n    },\n  },\n  {\n    id: '6',\n    type: 'output',\n    position: { x: 900, y: 100 },\n    data: { label: 'Final Output' },\n  },\n]\n\nconst initialEdges: Edge[] = [\n  { id: 'e1-2', source: '1', target: '2' },\n  { id: 'e2-3', source: '2', target: '3' },\n  { id: 'e3-4', source: '3', target: '4', label: 'High Quality' },\n  { id: 'e3-5', source: '3', target: '5', label: 'Needs Improvement' },\n  { id: 'e4-6', source: '4', target: '6' },\n  { id: 'e5-6', source: '5', target: '6' },\n]\n\nexport function WorkflowEditor() {\n  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)\n  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)\n\n  const onConnect = useCallback(\n    (params: Connection) => setEdges((eds) => addEdge(params, eds)),\n    [setEdges]\n  )\n\n  return (\n    <div className=\"h-full w-full\">\n      <ReactFlow\n        nodes={nodes}\n        edges={edges}\n        onNodesChange={onNodesChange}\n        onEdgesChange={onEdgesChange}\n        onConnect={onConnect}\n        nodeTypes={nodeTypes}\n        fitView\n        className=\"bg-gray-50\"\n      >\n        <Controls />\n        <Background variant={BackgroundVariant.Dots} gap={12} size={1} />\n      </ReactFlow>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;AAsBA,MAAM,YAAY;IAChB,YAAY,+JAAA,CAAA,cAAW;IACvB,SAAS,6JAAA,CAAA,YAAS;IAClB,UAAU,8JAAA,CAAA,aAAU;IACpB,aAAa,iKAAA,CAAA,gBAAa;IAC1B,UAAU,8JAAA,CAAA,aAAU;AACtB;AAEA,MAAM,eAAuB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG;YAAK,GAAG;QAAI;QAC3B,MAAM;YAAE,OAAO;QAAQ;IACzB;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG;YAAK,GAAG;QAAI;QAC3B,MAAM;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;gBACN,OAAO;gBACP,aAAa;gBACb,WAAW;gBACX,mBAAmB;oBAAC;oBAAsB;iBAAsB;gBAChE,eAAe;YACjB;QACF;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG;YAAK,GAAG;QAAI;QAC3B,MAAM;YAAE,OAAO;QAAgB;IACjC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG;YAAK,GAAG;QAAG;QAC1B,MAAM;YAAE,OAAO;QAAkB;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG;YAAK,GAAG;QAAI;QAC3B,MAAM;YACJ,OAAO;YACP,aAAa;YACb,WAAW;YACX,QAAQ;gBACN,OAAO;gBACP,aAAa;gBACb,WAAW;gBACX,mBAAmB;oBAAC;oBAAmB;iBAA4B;gBACnE,eAAe;YACjB;QACF;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;YAAE,GAAG;YAAK,GAAG;QAAI;QAC3B,MAAM;YAAE,OAAO;QAAe;IAChC;CACD;AAED,MAAM,eAAuB;IAC3B;QAAE,IAAI;QAAQ,QAAQ;QAAK,QAAQ;IAAI;IACvC;QAAE,IAAI;QAAQ,QAAQ;QAAK,QAAQ;IAAI;IACvC;QAAE,IAAI;QAAQ,QAAQ;QAAK,QAAQ;QAAK,OAAO;IAAe;IAC9D;QAAE,IAAI;QAAQ,QAAQ;QAAK,QAAQ;QAAK,OAAO;IAAoB;IACnE;QAAE,IAAI;QAAQ,QAAQ;QAAK,QAAQ;IAAI;IACvC;QAAE,IAAI;QAAQ,QAAQ;QAAK,QAAQ;IAAI;CACxC;AAEM,SAAS;;IACd,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE;IACvD,MAAM,CAAC,OAAO,UAAU,cAAc,GAAG,CAAA,GAAA,+JAAA,CAAA,gBAAa,AAAD,EAAE;IAEvD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAC1B,CAAC,SAAuB;yDAAS,CAAC,MAAQ,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD,EAAE,QAAQ;;gDAC1D;QAAC;KAAS;IAGZ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,uMAAA,CAAA,UAAS;YACR,OAAO;YACP,OAAO;YACP,eAAe;YACf,eAAe;YACf,WAAW;YACX,WAAW;YACX,OAAO;YACP,WAAU;;8BAEV,6LAAC,mKAAA,CAAA,WAAQ;;;;;8BACT,6LAAC,qKAAA,CAAA,aAAU;oBAAC,SAAS,qKAAA,CAAA,oBAAiB,CAAC,IAAI;oBAAE,KAAK;oBAAI,MAAM;;;;;;;;;;;;;;;;;AAIpE;GA1BgB;;QAC2B,+JAAA,CAAA,gBAAa;QACb,+JAAA,CAAA,gBAAa;;;KAFxC", "debugId": null}}, {"offset": {"line": 2002, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Views/WorkflowsView.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useStore } from '@/store/useStore'\nimport { Plus, Search, Filter, MoreVertical, Play, Edit, Trash2, ArrowLeft } from 'lucide-react'\nimport * as DropdownMenu from '@radix-ui/react-dropdown-menu'\nimport { WorkflowEditor } from '@/components/WorkflowEditor/WorkflowEditor'\nimport { useTranslation } from '@/hooks/useTranslation'\n\nexport function WorkflowsView() {\n  const { workflows, setCurrentWorkflow } = useStore()\n  const t = useTranslation()\n  const [isEditing, setIsEditing] = useState(false)\n  const [editingWorkflow, setEditingWorkflow] = useState<any>(null)\n\n  const handleCreateWorkflow = () => {\n    const newWorkflow = {\n      id: `workflow-${Date.now()}`,\n      name: t('workflows.newWorkflow'),\n      description: 'A new workflow for automation',\n      nodes: [],\n      edges: [],\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    }\n    setEditingWorkflow(newWorkflow)\n    setIsEditing(true)\n  }\n\n  const handleEditWorkflow = (workflow: any) => {\n    setEditingWorkflow(workflow)\n    setIsEditing(true)\n  }\n\n  const handleBackToList = () => {\n    setIsEditing(false)\n    setEditingWorkflow(null)\n  }\n\n  if (isEditing) {\n    return (\n      <div className=\"h-full flex flex-col\">\n        {/* Editor Header */}\n        <div className=\"bg-white border-b border-gray-200 p-4\">\n          <div className=\"flex items-center space-x-4\">\n            <button\n              onClick={handleBackToList}\n              className=\"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors\"\n            >\n              <ArrowLeft className=\"w-4 h-4\" />\n              <span>Back to Workflows</span>\n            </button>\n            <div className=\"flex-1\">\n              <h1 className=\"text-xl font-bold text-gray-900\">\n                {editingWorkflow?.name || 'New Workflow'}\n              </h1>\n              <p className=\"text-sm text-gray-600\">\n                Design your AI-powered workflow with drag-and-drop nodes\n              </p>\n            </div>\n            <button className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\">\n              Save Workflow\n            </button>\n          </div>\n        </div>\n\n        {/* Workflow Editor */}\n        <div className=\"flex-1\">\n          <WorkflowEditor />\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">{t('workflows.title')}</h1>\n            <p className=\"text-gray-600 mt-1\">\n              {t('workflows.description')}\n            </p>\n          </div>\n          <button\n            onClick={handleCreateWorkflow}\n            className=\"flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\"\n          >\n            <Plus className=\"w-4 h-4\" />\n            <span>{t('workflows.newWorkflow')}</span>\n          </button>\n        </div>\n        \n        {/* Search and Filters */}\n        <div className=\"flex items-center space-x-4 mt-6\">\n          <div className=\"flex-1 relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search workflows...\"\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n          <button className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\">\n            <Filter className=\"w-4 h-4\" />\n            <span>Filter</span>\n          </button>\n        </div>\n      </div>\n      \n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-6\">\n        {workflows.length === 0 ? (\n          /* Empty State */\n          <div className=\"flex flex-col items-center justify-center h-full text-center\">\n            <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n              <Play className=\"w-12 h-12 text-gray-400\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              {t('workflows.noWorkflows')}\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-md\">\n              {t('workflows.noWorkflowsDescription')}\n            </p>\n            <button\n              onClick={handleCreateWorkflow}\n              className=\"flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors\"\n            >\n              <Plus className=\"w-5 h-5\" />\n              <span>{t('workflows.createFirstWorkflow')}</span>\n            </button>\n          </div>\n        ) : (\n          /* Workflows Grid */\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {workflows.map((workflow) => (\n              <div\n                key={workflow.id}\n                className=\"bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow cursor-pointer\"\n                onClick={() => setCurrentWorkflow(workflow)}\n              >\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-gray-900 mb-1\">\n                        {workflow.name}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 line-clamp-2\">\n                        {workflow.description}\n                      </p>\n                    </div>\n                    <DropdownMenu.Root>\n                      <DropdownMenu.Trigger asChild>\n                        <button\n                          className=\"p-1 text-gray-400 hover:text-gray-600 rounded-md\"\n                          onClick={(e) => e.stopPropagation()}\n                        >\n                          <MoreVertical className=\"w-4 h-4\" />\n                        </button>\n                      </DropdownMenu.Trigger>\n                      <DropdownMenu.Portal>\n                        <DropdownMenu.Content\n                          className=\"min-w-[160px] bg-white rounded-lg shadow-lg border border-gray-200 p-1\"\n                          sideOffset={5}\n                        >\n                          <DropdownMenu.Item className=\"flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md cursor-pointer\">\n                            <Play className=\"w-4 h-4 mr-2\" />\n                            Run\n                          </DropdownMenu.Item>\n                          <DropdownMenu.Item\n                            className=\"flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md cursor-pointer\"\n                            onClick={() => handleEditWorkflow(workflow)}\n                          >\n                            <Edit className=\"w-4 h-4 mr-2\" />\n                            Edit\n                          </DropdownMenu.Item>\n                          <DropdownMenu.Separator className=\"h-px bg-gray-200 my-1\" />\n                          <DropdownMenu.Item className=\"flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md cursor-pointer\">\n                            <Trash2 className=\"w-4 h-4 mr-2\" />\n                            Delete\n                          </DropdownMenu.Item>\n                        </DropdownMenu.Content>\n                      </DropdownMenu.Portal>\n                    </DropdownMenu.Root>\n                  </div>\n                  \n                  <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                    <span>{workflow.nodes.length} nodes</span>\n                    <span>\n                      Updated {workflow.updatedAt.toLocaleDateString()}\n                    </span>\n                  </div>\n                </div>\n                \n                <div className=\"px-6 py-3 bg-gray-50 border-t border-gray-100 rounded-b-lg\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-xs text-gray-600\">Ready</span>\n                    </div>\n                    <button\n                      className=\"text-xs text-blue-600 hover:text-blue-700 font-medium\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        console.log('Running workflow:', workflow.id)\n                      }}\n                    >\n                      {t('workflows.run')}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IACjD,MAAM,IAAI,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAE5D,MAAM,uBAAuB;QAC3B,MAAM,cAAc;YAClB,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;YAC5B,MAAM,EAAE;YACR,aAAa;YACb,OAAO,EAAE;YACT,OAAO,EAAE;YACT,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QACA,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,aAAa;IACf;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,mBAAmB;IACrB;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,iBAAiB,QAAQ;;;;;;kDAE5B,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,6LAAC;gCAAO,WAAU;0CAAkF;;;;;;;;;;;;;;;;;8BAOxG,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yJAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,EAAE;;;;;;kDACpD,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;0CAGP,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAM,EAAE;;;;;;;;;;;;;;;;;;kCAKb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAGd,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACZ,UAAU,MAAM,KAAK,IACpB,eAAe,iBACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAG,WAAU;sCACX,EAAE;;;;;;sCAEL,6LAAC;4BAAE,WAAU;sCACV,EAAE;;;;;;sCAEL,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAM,EAAE;;;;;;;;;;;;;;;;;2BAIb,kBAAkB,iBAClB,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;4BAEC,WAAU;4BACV,SAAS,IAAM,mBAAmB;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,SAAS,IAAI;;;;;;sEAEhB,6LAAC;4DAAE,WAAU;sEACV,SAAS,WAAW;;;;;;;;;;;;8DAGzB,6LAAC,+KAAA,CAAA,OAAiB;;sEAChB,6LAAC,+KAAA,CAAA,UAAoB;4DAAC,OAAO;sEAC3B,cAAA,6LAAC;gEACC,WAAU;gEACV,SAAS,CAAC,IAAM,EAAE,eAAe;0EAEjC,cAAA,6LAAC,6NAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG5B,6LAAC,+KAAA,CAAA,SAAmB;sEAClB,cAAA,6LAAC,+KAAA,CAAA,UAAoB;gEACnB,WAAU;gEACV,YAAY;;kFAEZ,6LAAC,+KAAA,CAAA,OAAiB;wEAAC,WAAU;;0FAC3B,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGnC,6LAAC,+KAAA,CAAA,OAAiB;wEAChB,WAAU;wEACV,SAAS,IAAM,mBAAmB;;0FAElC,6LAAC,8MAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGnC,6LAAC,+KAAA,CAAA,YAAsB;wEAAC,WAAU;;;;;;kFAClC,6LAAC,+KAAA,CAAA,OAAiB;wEAAC,WAAU;;0FAC3B,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQ7C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAM,SAAS,KAAK,CAAC,MAAM;wDAAC;;;;;;;8DAC7B,6LAAC;;wDAAK;wDACK,SAAS,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;8CAKpD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEAAwB;;;;;;;;;;;;0DAE1C,6LAAC;gDACC,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,QAAQ,GAAG,CAAC,qBAAqB,SAAS,EAAE;gDAC9C;0DAEC,EAAE;;;;;;;;;;;;;;;;;;2BAtEJ,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;AAiFhC;GAlNgB;;QAC4B,2HAAA,CAAA,WAAQ;QACxC,iIAAA,CAAA,iBAAc;;;KAFV", "debugId": null}}, {"offset": {"line": 2617, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Views/MarketplaceView.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { Search, Star, Download, ExternalLink, X, Eye, User, Calendar, Tag } from 'lucide-react'\nimport * as Dialog from '@radix-ui/react-dialog'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { useStore } from '@/store/useStore'\nimport { Workflow } from '@/store/useStore'\n\nconst marketplaceItems = [\n  {\n    id: '1',\n    name: 'Email Marketing Agent',\n    description: 'Automated email campaign management with AI-powered content generation',\n    category: 'Marketing',\n    rating: 4.8,\n    downloads: 1250,\n    price: 'Free',\n    image: '📧',\n    author: 'MarketingPro',\n    createdAt: '2024-01-15',\n    tags: ['Email', 'Marketing', 'Automation', 'AI'],\n    detailedDescription: 'This comprehensive email marketing workflow automates your entire email campaign process. It includes AI-powered content generation, audience segmentation, A/B testing, and performance analytics. The workflow can automatically create personalized email content based on user behavior, schedule optimal send times, and track engagement metrics.',\n    features: [\n      'AI-powered email content generation',\n      'Automatic audience segmentation',\n      'A/B testing capabilities',\n      'Real-time analytics dashboard',\n      'Integration with major email platforms',\n      'Personalization based on user behavior'\n    ],\n    requirements: [\n      'Email service provider API key',\n      'Customer database access',\n      'Basic marketing knowledge'\n    ]\n  },\n  {\n    id: '2',\n    name: 'Data Analysis Workflow',\n    description: 'Complete data processing pipeline with visualization and reporting',\n    category: 'Analytics',\n    rating: 4.9,\n    downloads: 890,\n    price: '$29',\n    image: '📊',\n    author: 'DataScience Team',\n    createdAt: '2024-02-20',\n    tags: ['Data', 'Analytics', 'Visualization', 'Reports'],\n    detailedDescription: 'A powerful data analysis workflow that transforms raw data into actionable insights. This workflow includes data cleaning, statistical analysis, machine learning models, and automated report generation. Perfect for businesses looking to make data-driven decisions.',\n    features: [\n      'Automated data cleaning and preprocessing',\n      'Statistical analysis and modeling',\n      'Interactive data visualizations',\n      'Automated report generation',\n      'Machine learning integration',\n      'Export to multiple formats'\n    ],\n    requirements: [\n      'Data source connection',\n      'Basic understanding of data analysis',\n      'Storage for processed data'\n    ]\n  },\n  {\n    id: '3',\n    name: 'Customer Support Bot',\n    description: 'Intelligent customer service automation with natural language processing',\n    category: 'Support',\n    rating: 4.7,\n    downloads: 2100,\n    price: 'Free',\n    image: '🤖',\n    author: 'SupportAI',\n    createdAt: '2024-03-10',\n    tags: ['Support', 'Chatbot', 'NLP', 'Customer Service'],\n    detailedDescription: 'An advanced customer support bot that handles customer inquiries 24/7. Uses natural language processing to understand customer questions and provides accurate responses. Can escalate complex issues to human agents and learn from interactions.',\n    features: [\n      '24/7 automated customer support',\n      'Natural language understanding',\n      'Multi-language support',\n      'Escalation to human agents',\n      'Learning from interactions',\n      'Integration with help desk systems'\n    ],\n    requirements: [\n      'Customer support knowledge base',\n      'Chat platform integration',\n      'Basic customer service setup'\n    ]\n  },\n]\n\nexport function MarketplaceView() {\n  const [selectedItem, setSelectedItem] = useState<typeof marketplaceItems[0] | null>(null)\n  const t = useTranslation()\n  const { addWorkflow } = useStore()\n\n  const handleSaveWorkflow = (item: typeof marketplaceItems[0]) => {\n    // Convert marketplace item to workflow format\n    const newWorkflow: Workflow = {\n      id: `workflow-${Date.now()}`,\n      name: item.title,\n      description: item.description,\n      nodes: [\n        {\n          id: '1',\n          type: 'input',\n          position: { x: 100, y: 100 },\n          data: { label: 'Start' }\n        },\n        {\n          id: '2',\n          type: 'ai-agent',\n          position: { x: 300, y: 100 },\n          data: {\n            label: item.title,\n            description: item.description,\n            config: {\n              model: 'gpt-4',\n              temperature: 0.7,\n              maxTokens: 1000,\n              systemMessage: `You are a ${item.title.toLowerCase()} specialist.`\n            }\n          }\n        },\n        {\n          id: '3',\n          type: 'output',\n          position: { x: 500, y: 100 },\n          data: { label: 'Output' }\n        }\n      ],\n      edges: [\n        {\n          id: 'e1-2',\n          source: '1',\n          target: '2'\n        },\n        {\n          id: 'e2-3',\n          source: '2',\n          target: '3'\n        }\n      ],\n      createdAt: new Date(),\n      updatedAt: new Date()\n    }\n\n    addWorkflow(newWorkflow)\n    setSelectedItem(null)\n\n    // Show success message (you could add a toast notification here)\n    alert(`工作流 \"${item.title}\" 已成功保存到我的工作流！`)\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 p-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">{t('marketplace.title')}</h1>\n        <p className=\"text-gray-600 mt-1\">\n          {t('marketplace.description')}\n        </p>\n        \n        {/* Search */}\n        <div className=\"mt-6\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder={t('marketplace.searchWorkflows')}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-6\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {marketplaceItems.map((item) => (\n            <div\n              key={item.id}\n              className=\"bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow cursor-pointer\"\n              onClick={() => setSelectedItem(item)}\n            >\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div className=\"text-3xl\">{item.image}</div>\n                  <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">\n                    {item.category}\n                  </span>\n                </div>\n                \n                <h3 className=\"font-semibold text-gray-900 mb-2\">{item.name}</h3>\n                <p className=\"text-sm text-gray-600 mb-4\">{item.description}</p>\n                \n                <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                  <div className=\"flex items-center space-x-1\">\n                    <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                    <span>{item.rating}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <Download className=\"w-4 h-4\" />\n                    <span>{item.downloads}</span>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center justify-between\">\n                  <span className=\"font-semibold text-gray-900\">{item.price}</span>\n                  <div className=\"flex items-center space-x-2\">\n                    <button\n                      className=\"flex items-center space-x-1 text-gray-600 hover:text-gray-700 text-sm font-medium\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        setSelectedItem(item)\n                      }}\n                    >\n                      <Eye className=\"w-3 h-3\" />\n                      <span>{t('marketplace.viewDetails')}</span>\n                    </button>\n                    <button\n                      className=\"flex items-center space-x-1 text-blue-600 hover:text-blue-700 text-sm font-medium\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        handleSaveWorkflow(item)\n                      }}\n                    >\n                      <span>{t('marketplace.saveToMyWorkflows')}</span>\n                      <ExternalLink className=\"w-3 h-3\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Workflow Details Modal */}\n      <Dialog.Root open={!!selectedItem} onOpenChange={() => setSelectedItem(null)}>\n        <Dialog.Portal>\n          <Dialog.Overlay className=\"fixed inset-0 bg-black/50 z-50\" />\n          <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto z-50\">\n            {selectedItem && (\n              <div className=\"p-6\">\n                {/* Header */}\n                <div className=\"flex items-start justify-between mb-6\">\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"text-4xl\">{selectedItem.image}</div>\n                    <div>\n                      <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">{selectedItem.name}</h2>\n                      <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                        <div className=\"flex items-center space-x-1\">\n                          <User className=\"w-4 h-4\" />\n                          <span>{t('marketplace.author')}: {selectedItem.author}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-1\">\n                          <Calendar className=\"w-4 h-4\" />\n                          <span>{selectedItem.createdAt}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-1\">\n                          <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                          <span>{selectedItem.rating}</span>\n                        </div>\n                        <div className=\"flex items-center space-x-1\">\n                          <Download className=\"w-4 h-4\" />\n                          <span>{selectedItem.downloads} {t('marketplace.downloads')}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <Dialog.Close asChild>\n                    <button className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\">\n                      <X className=\"w-5 h-5\" />\n                    </button>\n                  </Dialog.Close>\n                </div>\n\n                {/* Tags */}\n                <div className=\"mb-6\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <Tag className=\"w-4 h-4 text-gray-600\" />\n                    <span className=\"text-sm font-medium text-gray-700\">{t('marketplace.tags')}:</span>\n                  </div>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {selectedItem.tags.map((tag, index) => (\n                      <span\n                        key={index}\n                        className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\"\n                      >\n                        {tag}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Description */}\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">{t('marketplace.description_label')}</h3>\n                  <p className=\"text-gray-700 leading-relaxed\">{selectedItem.detailedDescription}</p>\n                </div>\n\n                {/* Features */}\n                <div className=\"mb-6\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Features</h3>\n                  <ul className=\"space-y-2\">\n                    {selectedItem.features.map((feature, index) => (\n                      <li key={index} className=\"flex items-start space-x-2\">\n                        <div className=\"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 flex-shrink-0\"></div>\n                        <span className=\"text-gray-700\">{feature}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Requirements */}\n                <div className=\"mb-8\">\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Requirements</h3>\n                  <ul className=\"space-y-2\">\n                    {selectedItem.requirements.map((requirement, index) => (\n                      <li key={index} className=\"flex items-start space-x-2\">\n                        <div className=\"w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0\"></div>\n                        <span className=\"text-gray-700\">{requirement}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Actions */}\n                <div className=\"flex items-center justify-between pt-6 border-t border-gray-200\">\n                  <div className=\"flex items-center space-x-4\">\n                    <span className=\"text-2xl font-bold text-gray-900\">{selectedItem.price}</span>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full\">\n                      {selectedItem.category}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center space-x-3\">\n                    <Dialog.Close asChild>\n                      <button className=\"px-4 py-2 text-gray-600 hover:text-gray-700 border border-gray-300 rounded-lg transition-colors\">\n                        {t('marketplace.close')}\n                      </button>\n                    </Dialog.Close>\n                    <button\n                      className=\"px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors\"\n                      onClick={() => {\n                        if (selectedItem) {\n                          handleSaveWorkflow(selectedItem)\n                        }\n                      }}\n                    >\n                      {t('marketplace.saveToMyWorkflows')}\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </Dialog.Content>\n        </Dialog.Portal>\n      </Dialog.Root>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AASA,MAAM,mBAAmB;IACvB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,OAAO;QACP,OAAO;QACP,QAAQ;QACR,WAAW;QACX,MAAM;YAAC;YAAS;YAAa;YAAc;SAAK;QAChD,qBAAqB;QACrB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,OAAO;QACP,OAAO;QACP,QAAQ;QACR,WAAW;QACX,MAAM;YAAC;YAAQ;YAAa;YAAiB;SAAU;QACvD,qBAAqB;QACrB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,QAAQ;QACR,WAAW;QACX,OAAO;QACP,OAAO;QACP,QAAQ;QACR,WAAW;QACX,MAAM;YAAC;YAAW;YAAW;YAAO;SAAmB;QACvD,qBAAqB;QACrB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;SACD;IACH;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IACpF,MAAM,IAAI,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAE/B,MAAM,qBAAqB,CAAC;QAC1B,8CAA8C;QAC9C,MAAM,cAAwB;YAC5B,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;YAC5B,MAAM,KAAK,KAAK;YAChB,aAAa,KAAK,WAAW;YAC7B,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAC3B,MAAM;wBAAE,OAAO;oBAAQ;gBACzB;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAC3B,MAAM;wBACJ,OAAO,KAAK,KAAK;wBACjB,aAAa,KAAK,WAAW;wBAC7B,QAAQ;4BACN,OAAO;4BACP,aAAa;4BACb,WAAW;4BACX,eAAe,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,WAAW,GAAG,YAAY,CAAC;wBACpE;oBACF;gBACF;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAC3B,MAAM;wBAAE,OAAO;oBAAS;gBAC1B;aACD;YACD,OAAO;gBACL;oBACE,IAAI;oBACJ,QAAQ;oBACR,QAAQ;gBACV;gBACA;oBACE,IAAI;oBACJ,QAAQ;oBACR,QAAQ;gBACV;aACD;YACD,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QAEA,YAAY;QACZ,gBAAgB;QAEhB,iEAAiE;QACjE,MAAM,CAAC,KAAK,EAAE,KAAK,KAAK,CAAC,cAAc,CAAC;IAC1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC,EAAE;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;kCAIL,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAa,EAAE;oCACf,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,6LAAC;4BAEC,WAAU;4BACV,SAAS,IAAM,gBAAgB;sCAE/B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAY,KAAK,KAAK;;;;;;0DACrC,6LAAC;gDAAK,WAAU;0DACb,KAAK,QAAQ;;;;;;;;;;;;kDAIlB,6LAAC;wCAAG,WAAU;kDAAoC,KAAK,IAAI;;;;;;kDAC3D,6LAAC;wCAAE,WAAU;kDAA8B,KAAK,WAAW;;;;;;kDAE3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;kEAAM,KAAK,MAAM;;;;;;;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAM,KAAK,SAAS;;;;;;;;;;;;;;;;;;kDAIzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA+B,KAAK,KAAK;;;;;;0DACzD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,gBAAgB;wDAClB;;0EAEA,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;0EACf,6LAAC;0EAAM,EAAE;;;;;;;;;;;;kEAEX,6LAAC;wDACC,WAAU;wDACV,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,mBAAmB;wDACrB;;0EAEA,6LAAC;0EAAM,EAAE;;;;;;0EACT,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BA/C3B,KAAK,EAAE;;;;;;;;;;;;;;;0BA0DpB,6LAAC,qKAAA,CAAA,OAAW;gBAAC,MAAM,CAAC,CAAC;gBAAc,cAAc,IAAM,gBAAgB;0BACrE,cAAA,6LAAC,qKAAA,CAAA,SAAa;;sCACZ,6LAAC,qKAAA,CAAA,UAAc;4BAAC,WAAU;;;;;;sCAC1B,6LAAC,qKAAA,CAAA,UAAc;4BAAC,WAAU;sCACvB,8BACC,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAY,aAAa,KAAK;;;;;;kEAC7C,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAyC,aAAa,IAAI;;;;;;0EACxE,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;;oFAAM,EAAE;oFAAsB;oFAAG,aAAa,MAAM;;;;;;;;;;;;;kFAEvD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;0FAAM,aAAa,SAAS;;;;;;;;;;;;kFAE/B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAM,aAAa,MAAM;;;;;;;;;;;;kFAE5B,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;;oFAAM,aAAa,SAAS;oFAAC;oFAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAK1C,6LAAC,qKAAA,CAAA,QAAY;gDAAC,OAAO;0DACnB,cAAA,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,+LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kDAMnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;;4DAAqC,EAAE;4DAAoB;;;;;;;;;;;;;0DAE7E,6LAAC;gDAAI,WAAU;0DACZ,aAAa,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC3B,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;kDAUb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA4C,EAAE;;;;;;0DAC5D,6LAAC;gDAAE,WAAU;0DAAiC,aAAa,mBAAmB;;;;;;;;;;;;kDAIhF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAG,WAAU;0DACX,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;;;;;;;kDASf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAG,WAAU;0DACX,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAC3C,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;;;;;;;kDASf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAoC,aAAa,KAAK;;;;;;kEACtE,6LAAC;wDAAK,WAAU;kEACb,aAAa,QAAQ;;;;;;;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qKAAA,CAAA,QAAY;wDAAC,OAAO;kEACnB,cAAA,6LAAC;4DAAO,WAAU;sEACf,EAAE;;;;;;;;;;;kEAGP,6LAAC;wDACC,WAAU;wDACV,SAAS;4DACP,IAAI,cAAc;gEAChB,mBAAmB;4DACrB;wDACF;kEAEC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWzB;GA9QgB;;QAEJ,iIAAA,CAAA,iBAAc;QACA,2HAAA,CAAA,WAAQ;;;KAHlB", "debugId": null}}, {"offset": {"line": 3559, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/lib/autogen.ts"], "sourcesContent": ["// AutoGen Integration Module\n// This module provides a TypeScript interface for AutoGen functionality\n\nexport interface AutoGenAgent {\n  id: string\n  name: string\n  role: 'assistant' | 'user' | 'system'\n  systemMessage?: string\n  model?: string\n  temperature?: number\n  maxTokens?: number\n}\n\nexport interface AutoGenConversation {\n  id: string\n  agents: AutoGenAgent[]\n  messages: AutoGenMessage[]\n  status: 'active' | 'completed' | 'error'\n}\n\nexport interface AutoGenMessage {\n  id: string\n  agentId: string\n  content: string\n  timestamp: Date\n  type: 'text' | 'function_call' | 'function_result'\n}\n\nexport class AutoGenManager {\n  private conversations: Map<string, AutoGenConversation> = new Map()\n\n  // Create a new AutoGen agent\n  createAgent(config: Omit<AutoGenAgent, 'id'>): AutoGenAgent {\n    return {\n      id: `agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n      ...config,\n    }\n  }\n\n  // Start a new conversation between agents\n  startConversation(agents: AutoGenAgent[]): AutoGenConversation {\n    const conversation: AutoGenConversation = {\n      id: `conv-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n      agents,\n      messages: [],\n      status: 'active',\n    }\n\n    this.conversations.set(conversation.id, conversation)\n    return conversation\n  }\n\n  // Send a message in a conversation\n  async sendMessage(\n    conversationId: string,\n    agentId: string,\n    content: string\n  ): Promise<AutoGenMessage> {\n    const conversation = this.conversations.get(conversationId)\n    if (!conversation) {\n      throw new Error(`Conversation ${conversationId} not found`)\n    }\n\n    const message: AutoGenMessage = {\n      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n      agentId,\n      content,\n      timestamp: new Date(),\n      type: 'text',\n    }\n\n    conversation.messages.push(message)\n\n    // Simulate AI response (in real implementation, this would call AutoGen)\n    if (agentId !== 'user') {\n      await this.simulateAgentResponse(conversation, agentId)\n    }\n\n    return message\n  }\n\n  // Simulate an agent response (placeholder for actual AutoGen integration)\n  private async simulateAgentResponse(\n    conversation: AutoGenConversation,\n    triggeringAgentId: string\n  ): Promise<void> {\n    // Find the next agent to respond\n    const currentAgentIndex = conversation.agents.findIndex(\n      (agent) => agent.id === triggeringAgentId\n    )\n    const nextAgentIndex = (currentAgentIndex + 1) % conversation.agents.length\n    const nextAgent = conversation.agents[nextAgentIndex]\n\n    // Simulate processing delay\n    await new Promise((resolve) => setTimeout(resolve, 1000))\n\n    const responseMessage: AutoGenMessage = {\n      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n      agentId: nextAgent.id,\n      content: this.generateSimulatedResponse(nextAgent, conversation),\n      timestamp: new Date(),\n      type: 'text',\n    }\n\n    conversation.messages.push(responseMessage)\n  }\n\n  // Generate a simulated response based on agent role\n  private generateSimulatedResponse(\n    agent: AutoGenAgent,\n    conversation: AutoGenConversation\n  ): string {\n    const responses = {\n      assistant: [\n        \"I understand your request. Let me analyze this and provide a solution.\",\n        \"Based on the information provided, I recommend the following approach:\",\n        \"I've processed your input and here's my analysis:\",\n        \"Let me break this down into actionable steps:\",\n      ],\n      user: [\n        \"That looks good. Can you elaborate on the implementation details?\",\n        \"I have a follow-up question about this approach.\",\n        \"This is helpful. What would be the next steps?\",\n        \"Can you provide more specific examples?\",\n      ],\n      system: [\n        \"System: Task completed successfully.\",\n        \"System: Processing workflow step...\",\n        \"System: Validation passed. Proceeding to next stage.\",\n        \"System: Workflow execution in progress.\",\n      ],\n    }\n\n    const roleResponses = responses[agent.role] || responses.assistant\n    return roleResponses[Math.floor(Math.random() * roleResponses.length)]\n  }\n\n  // Get conversation by ID\n  getConversation(conversationId: string): AutoGenConversation | undefined {\n    return this.conversations.get(conversationId)\n  }\n\n  // Get all conversations\n  getAllConversations(): AutoGenConversation[] {\n    return Array.from(this.conversations.values())\n  }\n\n  // End a conversation\n  endConversation(conversationId: string): void {\n    const conversation = this.conversations.get(conversationId)\n    if (conversation) {\n      conversation.status = 'completed'\n    }\n  }\n}\n\n// Global AutoGen manager instance\nexport const autoGenManager = new AutoGenManager()\n\n// Predefined agent templates\nexport const AGENT_TEMPLATES = {\n  contentAnalyzer: {\n    name: 'Content Analyzer',\n    role: 'assistant' as const,\n    systemMessage: 'You are a content analysis expert. Analyze text for quality, sentiment, and key insights.',\n    model: 'gpt-4',\n    temperature: 0.3,\n    maxTokens: 1000,\n  },\n  contentWriter: {\n    name: 'Content Writer',\n    role: 'assistant' as const,\n    systemMessage: 'You are a professional content writer. Create engaging, well-structured content.',\n    model: 'gpt-4',\n    temperature: 0.7,\n    maxTokens: 1500,\n  },\n  qualityReviewer: {\n    name: 'Quality Reviewer',\n    role: 'assistant' as const,\n    systemMessage: 'You are a quality assurance expert. Review content for accuracy, clarity, and compliance.',\n    model: 'gpt-4',\n    temperature: 0.2,\n    maxTokens: 800,\n  },\n  projectManager: {\n    name: 'Project Manager',\n    role: 'assistant' as const,\n    systemMessage: 'You are a project manager. Coordinate tasks, track progress, and ensure deliverables.',\n    model: 'gpt-4',\n    temperature: 0.5,\n    maxTokens: 1200,\n  },\n}\n"], "names": [], "mappings": "AAAA,6BAA6B;AAC7B,wEAAwE;;;;;;AA2BjE,MAAM;IACH,gBAAkD,IAAI,MAAK;IAEnE,6BAA6B;IAC7B,YAAY,MAAgC,EAAgB;QAC1D,OAAO;YACL,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACpE,GAAG,MAAM;QACX;IACF;IAEA,0CAA0C;IAC1C,kBAAkB,MAAsB,EAAuB;QAC7D,MAAM,eAAoC;YACxC,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YACnE;YACA,UAAU,EAAE;YACZ,QAAQ;QACV;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE;QACxC,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,YACJ,cAAsB,EACtB,OAAe,EACf,OAAe,EACU;QACzB,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAC5C,IAAI,CAAC,cAAc;YACjB,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,eAAe,UAAU,CAAC;QAC5D;QAEA,MAAM,UAA0B;YAC9B,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClE;YACA;YACA,WAAW,IAAI;YACf,MAAM;QACR;QAEA,aAAa,QAAQ,CAAC,IAAI,CAAC;QAE3B,yEAAyE;QACzE,IAAI,YAAY,QAAQ;YACtB,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc;QACjD;QAEA,OAAO;IACT;IAEA,0EAA0E;IAC1E,MAAc,sBACZ,YAAiC,EACjC,iBAAyB,EACV;QACf,iCAAiC;QACjC,MAAM,oBAAoB,aAAa,MAAM,CAAC,SAAS,CACrD,CAAC,QAAU,MAAM,EAAE,KAAK;QAE1B,MAAM,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,aAAa,MAAM,CAAC,MAAM;QAC3E,MAAM,YAAY,aAAa,MAAM,CAAC,eAAe;QAErD,4BAA4B;QAC5B,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QAEnD,MAAM,kBAAkC;YACtC,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClE,SAAS,UAAU,EAAE;YACrB,SAAS,IAAI,CAAC,yBAAyB,CAAC,WAAW;YACnD,WAAW,IAAI;YACf,MAAM;QACR;QAEA,aAAa,QAAQ,CAAC,IAAI,CAAC;IAC7B;IAEA,oDAAoD;IAC5C,0BACN,KAAmB,EACnB,YAAiC,EACzB;QACR,MAAM,YAAY;YAChB,WAAW;gBACT;gBACA;gBACA;gBACA;aACD;YACD,MAAM;gBACJ;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;gBACA;gBACA;gBACA;aACD;QACH;QAEA,MAAM,gBAAgB,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,UAAU,SAAS;QAClE,OAAO,aAAa,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc,MAAM,EAAE;IACxE;IAEA,yBAAyB;IACzB,gBAAgB,cAAsB,EAAmC;QACvE,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;IAChC;IAEA,wBAAwB;IACxB,sBAA6C;QAC3C,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM;IAC7C;IAEA,qBAAqB;IACrB,gBAAgB,cAAsB,EAAQ;QAC5C,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAC5C,IAAI,cAAc;YAChB,aAAa,MAAM,GAAG;QACxB;IACF;AACF;AAGO,MAAM,iBAAiB,IAAI;AAG3B,MAAM,kBAAkB;IAC7B,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,eAAe;QACf,OAAO;QACP,aAAa;QACb,WAAW;IACb;IACA,eAAe;QACb,MAAM;QACN,MAAM;QACN,eAAe;QACf,OAAO;QACP,aAAa;QACb,WAAW;IACb;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,eAAe;QACf,OAAO;QACP,aAAa;QACb,WAAW;IACb;IACA,gBAAgB;QACd,MAAM;QACN,MAAM;QACN,eAAe;QACf,OAAO;QACP,aAAa;QACb,WAAW;IACb;AACF", "debugId": null}}, {"offset": {"line": 3708, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/lib/context7.ts"], "sourcesContent": ["// Context7 Integration Module\n// This module provides integration with Context7 for up-to-date documentation and code context\n\nexport interface Context7Library {\n  id: string\n  name: string\n  description: string\n  codeSnippets: number\n  trustScore: number\n  versions?: string[]\n}\n\nexport interface Context7Documentation {\n  title: string\n  description: string\n  source: string\n  language: string\n  code: string\n}\n\nexport interface Context7SearchResult {\n  libraries: Context7Library[]\n  selectedLibrary?: Context7Library\n}\n\nexport class Context7Manager {\n  private baseUrl = 'https://mcp.context7.com'\n  \n  // Simulate library resolution (in real implementation, this would call Context7 API)\n  async resolveLibraryId(libraryName: string): Promise<Context7SearchResult> {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 500))\n    \n    // Mock library search results based on common libraries\n    const mockLibraries: Context7Library[] = [\n      {\n        id: '/vercel/next.js',\n        name: 'Next.js',\n        description: 'The React Framework for Production',\n        codeSnippets: 2500,\n        trustScore: 10,\n        versions: ['v14.3.0', 'v13.5.0', 'v12.3.0']\n      },\n      {\n        id: '/facebook/react',\n        name: 'React',\n        description: 'A JavaScript library for building user interfaces',\n        codeSnippets: 3200,\n        trustScore: 10,\n        versions: ['v18.2.0', 'v17.0.2', 'v16.14.0']\n      },\n      {\n        id: '/microsoft/autogen',\n        name: 'AutoGen',\n        description: 'Multi-agent conversation framework',\n        codeSnippets: 850,\n        trustScore: 9,\n        versions: ['v0.2.0', 'v0.1.14']\n      },\n      {\n        id: '/openai/openai-node',\n        name: 'OpenAI Node.js',\n        description: 'Node.js library for the OpenAI API',\n        codeSnippets: 1200,\n        trustScore: 9,\n        versions: ['v4.20.0', 'v3.3.0']\n      }\n    ]\n    \n    // Filter libraries based on search term\n    const filteredLibraries = mockLibraries.filter(lib => \n      lib.name.toLowerCase().includes(libraryName.toLowerCase()) ||\n      lib.description.toLowerCase().includes(libraryName.toLowerCase())\n    )\n    \n    return {\n      libraries: filteredLibraries,\n      selectedLibrary: filteredLibraries[0] // Auto-select the first match\n    }\n  }\n  \n  // Simulate getting library documentation\n  async getLibraryDocs(\n    libraryId: string, \n    topic?: string, \n    tokens: number = 10000\n  ): Promise<Context7Documentation[]> {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 800))\n    \n    // Mock documentation based on library ID\n    const mockDocs: Record<string, Context7Documentation[]> = {\n      '/vercel/next.js': [\n        {\n          title: 'Next.js App Router Setup',\n          description: 'Setting up a new Next.js project with App Router',\n          source: 'https://nextjs.org/docs/app',\n          language: 'javascript',\n          code: `// app/page.tsx\nexport default function Home() {\n  return (\n    <div>\n      <h1>Welcome to Next.js!</h1>\n    </div>\n  )\n}`\n        },\n        {\n          title: 'Next.js API Routes',\n          description: 'Creating API routes in Next.js App Router',\n          source: 'https://nextjs.org/docs/app/building-your-application/routing/route-handlers',\n          language: 'typescript',\n          code: `// app/api/hello/route.ts\nexport async function GET() {\n  return Response.json({ message: 'Hello World' })\n}`\n        }\n      ],\n      '/microsoft/autogen': [\n        {\n          title: 'AutoGen Multi-Agent Setup',\n          description: 'Setting up multiple agents for conversation',\n          source: 'https://microsoft.github.io/autogen/docs/tutorial/introduction',\n          language: 'python',\n          code: `import autogen\n\nconfig_list = [\n    {\n        \"model\": \"gpt-4\",\n        \"api_key\": \"your-api-key\",\n    }\n]\n\nassistant = autogen.AssistantAgent(\n    name=\"assistant\",\n    llm_config={\"config_list\": config_list},\n)\n\nuser_proxy = autogen.UserProxyAgent(\n    name=\"user_proxy\",\n    human_input_mode=\"TERMINATE\",\n    max_consecutive_auto_reply=10,\n)\n\nuser_proxy.initiate_chat(assistant, message=\"Hello!\")`\n        },\n        {\n          title: 'AutoGen Group Chat',\n          description: 'Creating a group chat with multiple agents',\n          source: 'https://microsoft.github.io/autogen/docs/tutorial/conversation-patterns',\n          language: 'python',\n          code: `import autogen\n\n# Create multiple agents\nmanager = autogen.GroupChatManager(groupchat=groupchat, llm_config=llm_config)\ncoder = autogen.AssistantAgent(name=\"Coder\", llm_config=llm_config)\nreviewer = autogen.AssistantAgent(name=\"Reviewer\", llm_config=llm_config)\n\n# Start group conversation\ngroupchat = autogen.GroupChat(agents=[coder, reviewer], messages=[], max_round=12)\nmanager.initiate_chat(groupchat, message=\"Write a Python function to calculate fibonacci numbers.\")`\n        }\n      ]\n    }\n    \n    const docs = mockDocs[libraryId] || [\n      {\n        title: 'Library Documentation',\n        description: `Documentation for ${libraryId}`,\n        source: 'https://example.com',\n        language: 'text',\n        code: `// Documentation for ${libraryId} is not available in this mock implementation.\n// In a real implementation, this would fetch actual documentation from Context7.`\n      }\n    ]\n    \n    // Filter by topic if provided\n    if (topic) {\n      return docs.filter(doc => \n        doc.title.toLowerCase().includes(topic.toLowerCase()) ||\n        doc.description.toLowerCase().includes(topic.toLowerCase())\n      )\n    }\n    \n    return docs\n  }\n  \n  // Get enhanced context for AI agents\n  async getEnhancedContext(\n    libraries: string[], \n    topic?: string\n  ): Promise<string> {\n    const allDocs: Context7Documentation[] = []\n    \n    for (const libraryId of libraries) {\n      const docs = await this.getLibraryDocs(libraryId, topic)\n      allDocs.push(...docs)\n    }\n    \n    // Format documentation for AI context\n    const contextString = allDocs.map(doc => `\n## ${doc.title}\n**Source:** ${doc.source}\n**Description:** ${doc.description}\n\n\\`\\`\\`${doc.language}\n${doc.code}\n\\`\\`\\`\n`).join('\\n')\n    \n    return contextString\n  }\n  \n  // Check if Context7 is available\n  async checkAvailability(): Promise<boolean> {\n    try {\n      // In a real implementation, this would ping the Context7 API\n      await new Promise(resolve => setTimeout(resolve, 100))\n      return true\n    } catch (error) {\n      console.error('Context7 not available:', error)\n      return false\n    }\n  }\n}\n\n// Global Context7 manager instance\nexport const context7Manager = new Context7Manager()\n\n// Helper function to enhance AI prompts with Context7\nexport async function enhancePromptWithContext7(\n  prompt: string,\n  libraries: string[] = [],\n  topic?: string\n): Promise<string> {\n  try {\n    const context = await context7Manager.getEnhancedContext(libraries, topic)\n    \n    return `${prompt}\n\n## Context7 Enhanced Documentation:\n${context}\n\nPlease use the above documentation as reference when providing your response.`\n  } catch (error) {\n    console.error('Failed to enhance prompt with Context7:', error)\n    return prompt\n  }\n}\n\n// Predefined library sets for common use cases\nexport const LIBRARY_SETS = {\n  webDevelopment: ['/vercel/next.js', '/facebook/react', '/tailwindlabs/tailwindcss'],\n  aiAgents: ['/microsoft/autogen', '/openai/openai-node', '/langchain-ai/langchain'],\n  backend: ['/expressjs/express', '/nestjs/nest', '/fastify/fastify'],\n  database: ['/mongodb/docs', '/supabase/supabase', '/prisma/prisma'],\n  testing: ['/jestjs/jest', '/testing-library/react-testing-library', '/cypress-io/cypress']\n}\n"], "names": [], "mappings": "AAAA,8BAA8B;AAC9B,+FAA+F;;;;;;;AAwBxF,MAAM;IACH,UAAU,2BAA0B;IAE5C,qFAAqF;IACrF,MAAM,iBAAiB,WAAmB,EAAiC;QACzE,0BAA0B;QAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,wDAAwD;QACxD,MAAM,gBAAmC;YACvC;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,UAAU;oBAAC;oBAAW;oBAAW;iBAAU;YAC7C;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,UAAU;oBAAC;oBAAW;oBAAW;iBAAW;YAC9C;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,UAAU;oBAAC;oBAAU;iBAAU;YACjC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,UAAU;oBAAC;oBAAW;iBAAS;YACjC;SACD;QAED,wCAAwC;QACxC,MAAM,oBAAoB,cAAc,MAAM,CAAC,CAAA,MAC7C,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,IAAI,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAGhE,OAAO;YACL,WAAW;YACX,iBAAiB,iBAAiB,CAAC,EAAE,CAAC,8BAA8B;QACtE;IACF;IAEA,yCAAyC;IACzC,MAAM,eACJ,SAAiB,EACjB,KAAc,EACd,SAAiB,KAAK,EACY;QAClC,0BAA0B;QAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,yCAAyC;QACzC,MAAM,WAAoD;YACxD,mBAAmB;gBACjB;oBACE,OAAO;oBACP,aAAa;oBACb,QAAQ;oBACR,UAAU;oBACV,MAAM,CAAC;;;;;;;CAOhB,CAAC;gBACM;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,QAAQ;oBACR,UAAU;oBACV,MAAM,CAAC;;;CAGhB,CAAC;gBACM;aACD;YACD,sBAAsB;gBACpB;oBACE,OAAO;oBACP,aAAa;oBACb,QAAQ;oBACR,UAAU;oBACV,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;qDAoBoC,CAAC;gBAC9C;gBACA;oBACE,OAAO;oBACP,aAAa;oBACb,QAAQ;oBACR,UAAU;oBACV,MAAM,CAAC;;;;;;;;;mGASkF,CAAC;gBAC5F;aACD;QACH;QAEA,MAAM,OAAO,QAAQ,CAAC,UAAU,IAAI;YAClC;gBACE,OAAO;gBACP,aAAa,CAAC,kBAAkB,EAAE,WAAW;gBAC7C,QAAQ;gBACR,UAAU;gBACV,MAAM,CAAC,qBAAqB,EAAE,UAAU;iFACiC,CAAC;YAC5E;SACD;QAED,8BAA8B;QAC9B,IAAI,OAAO;YACT,OAAO,KAAK,MAAM,CAAC,CAAA,MACjB,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OAClD,IAAI,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;QAE5D;QAEA,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,mBACJ,SAAmB,EACnB,KAAc,EACG;QACjB,MAAM,UAAmC,EAAE;QAE3C,KAAK,MAAM,aAAa,UAAW;YACjC,MAAM,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW;YAClD,QAAQ,IAAI,IAAI;QAClB;QAEA,sCAAsC;QACtC,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAA,MAAO,CAAC;GAC3C,EAAE,IAAI,KAAK,CAAC;YACH,EAAE,IAAI,MAAM,CAAC;iBACR,EAAE,IAAI,WAAW,CAAC;;MAE7B,EAAE,IAAI,QAAQ,CAAC;AACrB,EAAE,IAAI,IAAI,CAAC;;AAEX,CAAC,EAAE,IAAI,CAAC;QAEJ,OAAO;IACT;IAEA,iCAAiC;IACjC,MAAM,oBAAsC;QAC1C,IAAI;YACF,6DAA6D;YAC7D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF;AACF;AAGO,MAAM,kBAAkB,IAAI;AAG5B,eAAe,0BACpB,MAAc,EACd,YAAsB,EAAE,EACxB,KAAc;IAEd,IAAI;QACF,MAAM,UAAU,MAAM,gBAAgB,kBAAkB,CAAC,WAAW;QAEpE,OAAO,GAAG,OAAO;;;AAGrB,EAAE,QAAQ;;6EAEmE,CAAC;IAC5E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;IACT;AACF;AAGO,MAAM,eAAe;IAC1B,gBAAgB;QAAC;QAAmB;QAAmB;KAA4B;IACnF,UAAU;QAAC;QAAsB;QAAuB;KAA0B;IAClF,SAAS;QAAC;QAAsB;QAAgB;KAAmB;IACnE,UAAU;QAAC;QAAiB;QAAsB;KAAiB;IACnE,SAAS;QAAC;QAAgB;QAA0C;KAAsB;AAC5F", "debugId": null}}, {"offset": {"line": 3954, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Demo/AutoGenDemo.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { autoGenManager, AGENT_TEMPLATES } from '@/lib/autogen'\nimport { context7Manager, enhancePromptWithContext7, LIBRARY_SETS } from '@/lib/context7'\nimport { Bot, Play, Square, Loader2, Code, Book } from 'lucide-react'\n\nexport function AutoGenDemo() {\n  const [isRunning, setIsRunning] = useState(false)\n  const [conversation, setConversation] = useState<any>(null)\n  const [messages, setMessages] = useState<any[]>([])\n  const [context7Enabled, setContext7Enabled] = useState(true)\n  const [selectedLibraries, setSelectedLibraries] = useState<string[]>(LIBRARY_SETS.aiAgents)\n\n  const startDemo = async () => {\n    setIsRunning(true)\n    setMessages([])\n\n    try {\n      // Create AutoGen agents\n      const contentAnalyzer = autoGenManager.createAgent(AGENT_TEMPLATES.contentAnalyzer)\n      const contentWriter = autoGenManager.createAgent(AGENT_TEMPLATES.contentWriter)\n      const qualityReviewer = autoGenManager.createAgent(AGENT_TEMPLATES.qualityReviewer)\n\n      // Start conversation\n      const conv = autoGenManager.startConversation([contentAnalyzer, contentWriter, qualityReviewer])\n      setConversation(conv)\n\n      // Initial prompt\n      let prompt = \"Create a comprehensive guide for building AI-powered workflows with AutoGen. Include code examples and best practices.\"\n\n      // Enhance with Context7 if enabled\n      if (context7Enabled) {\n        prompt = await enhancePromptWithContext7(prompt, selectedLibraries, 'workflows')\n      }\n\n      // Send initial message\n      await autoGenManager.sendMessage(conv.id, 'user', prompt)\n\n      // Simulate conversation flow\n      const conversationSteps = [\n        { agent: contentAnalyzer.id, delay: 2000 },\n        { agent: contentWriter.id, delay: 3000 },\n        { agent: qualityReviewer.id, delay: 2500 },\n        { agent: contentWriter.id, delay: 2000 },\n      ]\n\n      for (const step of conversationSteps) {\n        await new Promise(resolve => setTimeout(resolve, step.delay))\n        \n        if (!isRunning) break // Check if demo was stopped\n        \n        const updatedConv = autoGenManager.getConversation(conv.id)\n        if (updatedConv) {\n          setMessages([...updatedConv.messages])\n        }\n      }\n\n    } catch (error) {\n      console.error('Demo error:', error)\n    } finally {\n      setIsRunning(false)\n    }\n  }\n\n  const stopDemo = () => {\n    setIsRunning(false)\n    if (conversation) {\n      autoGenManager.endConversation(conversation.id)\n    }\n  }\n\n  return (\n    <div className=\"h-full flex flex-col bg-white\">\n      {/* Header */}\n      <div className=\"border-b border-gray-200 p-6\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n          AutoGen + Context7 Demo\n        </h2>\n        <p className=\"text-gray-600 mb-4\">\n          Experience multi-agent conversations enhanced with up-to-date documentation\n        </p>\n        \n        {/* Controls */}\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={isRunning ? stopDemo : startDemo}\n            disabled={isRunning && !conversation}\n            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${\n              isRunning \n                ? 'bg-red-600 hover:bg-red-700 text-white' \n                : 'bg-blue-600 hover:bg-blue-700 text-white'\n            }`}\n          >\n            {isRunning ? (\n              <>\n                <Square className=\"w-4 h-4\" />\n                <span>Stop Demo</span>\n              </>\n            ) : (\n              <>\n                <Play className=\"w-4 h-4\" />\n                <span>Start Demo</span>\n              </>\n            )}\n          </button>\n          \n          <div className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              id=\"context7-toggle\"\n              checked={context7Enabled}\n              onChange={(e) => setContext7Enabled(e.target.checked)}\n              className=\"rounded\"\n            />\n            <label htmlFor=\"context7-toggle\" className=\"text-sm text-gray-700\">\n              Enable Context7 Enhancement\n            </label>\n          </div>\n        </div>\n        \n        {/* Library Selection */}\n        {context7Enabled && (\n          <div className=\"mt-4\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Context7 Libraries:\n            </label>\n            <div className=\"flex flex-wrap gap-2\">\n              {Object.entries(LIBRARY_SETS).map(([key, libraries]) => (\n                <button\n                  key={key}\n                  onClick={() => setSelectedLibraries(libraries)}\n                  className={`px-3 py-1 text-xs rounded-full transition-colors ${\n                    selectedLibraries === libraries\n                      ? 'bg-blue-100 text-blue-800 border border-blue-300'\n                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                  }`}\n                >\n                  {key}\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-6\">\n        {messages.length === 0 && !isRunning ? (\n          <div className=\"flex flex-col items-center justify-center h-full text-center\">\n            <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4\">\n              <Bot className=\"w-8 h-8 text-blue-600\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n              Ready to Start Demo\n            </h3>\n            <p className=\"text-gray-600 max-w-md\">\n              Click \"Start Demo\" to see AutoGen agents collaborate on creating \n              AI workflow documentation, enhanced with Context7's up-to-date libraries.\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {messages.map((message, index) => {\n              const agent = conversation?.agents.find((a: any) => a.id === message.agentId)\n              const isUser = message.agentId === 'user'\n              \n              return (\n                <div\n                  key={message.id}\n                  className={`flex items-start space-x-3 ${\n                    isUser ? 'justify-end' : 'justify-start'\n                  }`}\n                >\n                  {!isUser && (\n                    <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                      <Bot className=\"w-4 h-4 text-blue-600\" />\n                    </div>\n                  )}\n                  \n                  <div\n                    className={`max-w-2xl rounded-lg p-4 ${\n                      isUser\n                        ? 'bg-blue-600 text-white'\n                        : 'bg-gray-100 border border-gray-200'\n                    }`}\n                  >\n                    {!isUser && (\n                      <div className=\"flex items-center space-x-2 mb-2\">\n                        <span className=\"font-semibold text-sm\">\n                          {agent?.name || 'Agent'}\n                        </span>\n                        <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded\">\n                          {agent?.role || 'assistant'}\n                        </span>\n                      </div>\n                    )}\n                    \n                    <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n                    \n                    <div className={`text-xs mt-2 ${\n                      isUser ? 'text-blue-100' : 'text-gray-500'\n                    }`}>\n                      {message.timestamp.toLocaleTimeString()}\n                    </div>\n                  </div>\n                  \n                  {isUser && (\n                    <div className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                      <span className=\"text-sm font-medium text-gray-600\">U</span>\n                    </div>\n                  )}\n                </div>\n              )\n            })}\n            \n            {isRunning && (\n              <div className=\"flex items-center justify-center py-4\">\n                <Loader2 className=\"w-6 h-6 animate-spin text-blue-600\" />\n                <span className=\"ml-2 text-sm text-gray-600\">\n                  Agents are collaborating...\n                </span>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      {/* Status Bar */}\n      <div className=\"border-t border-gray-200 p-4 bg-gray-50\">\n        <div className=\"flex items-center justify-between text-sm\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Code className=\"w-4 h-4 text-gray-500\" />\n              <span className=\"text-gray-600\">\n                AutoGen: {conversation ? 'Active' : 'Inactive'}\n              </span>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Book className=\"w-4 h-4 text-gray-500\" />\n              <span className=\"text-gray-600\">\n                Context7: {context7Enabled ? 'Enabled' : 'Disabled'}\n              </span>\n            </div>\n          </div>\n          <div className=\"text-gray-500\">\n            Messages: {messages.length}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,yHAAA,CAAA,eAAY,CAAC,QAAQ;IAE1F,MAAM,YAAY;QAChB,aAAa;QACb,YAAY,EAAE;QAEd,IAAI;YACF,wBAAwB;YACxB,MAAM,kBAAkB,wHAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,wHAAA,CAAA,kBAAe,CAAC,eAAe;YAClF,MAAM,gBAAgB,wHAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,wHAAA,CAAA,kBAAe,CAAC,aAAa;YAC9E,MAAM,kBAAkB,wHAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,wHAAA,CAAA,kBAAe,CAAC,eAAe;YAElF,qBAAqB;YACrB,MAAM,OAAO,wHAAA,CAAA,iBAAc,CAAC,iBAAiB,CAAC;gBAAC;gBAAiB;gBAAe;aAAgB;YAC/F,gBAAgB;YAEhB,iBAAiB;YACjB,IAAI,SAAS;YAEb,mCAAmC;YACnC,IAAI,iBAAiB;gBACnB,SAAS,MAAM,CAAA,GAAA,yHAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,mBAAmB;YACtE;YAEA,uBAAuB;YACvB,MAAM,wHAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,QAAQ;YAElD,6BAA6B;YAC7B,MAAM,oBAAoB;gBACxB;oBAAE,OAAO,gBAAgB,EAAE;oBAAE,OAAO;gBAAK;gBACzC;oBAAE,OAAO,cAAc,EAAE;oBAAE,OAAO;gBAAK;gBACvC;oBAAE,OAAO,gBAAgB,EAAE;oBAAE,OAAO;gBAAK;gBACzC;oBAAE,OAAO,cAAc,EAAE;oBAAE,OAAO;gBAAK;aACxC;YAED,KAAK,MAAM,QAAQ,kBAAmB;gBACpC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,KAAK,KAAK;gBAE3D,IAAI,CAAC,WAAW,OAAM,4BAA4B;gBAElD,MAAM,cAAc,wHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,KAAK,EAAE;gBAC1D,IAAI,aAAa;oBACf,YAAY;2BAAI,YAAY,QAAQ;qBAAC;gBACvC;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;QAC/B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW;QACf,aAAa;QACb,IAAI,cAAc;YAChB,wHAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,aAAa,EAAE;QAChD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAKlC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,YAAY,WAAW;gCAChC,UAAU,aAAa,CAAC;gCACxB,WAAW,CAAC,mEAAmE,EAC7E,YACI,2CACA,4CACJ;0CAED,0BACC;;sDACE,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;iEAGR;;sDACE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,SAAS;wCACT,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,OAAO;wCACpD,WAAU;;;;;;kDAEZ,6LAAC;wCAAM,SAAQ;wCAAkB,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;oBAOtE,iCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,yHAAA,CAAA,eAAY,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,UAAU,iBACjD,6LAAC;wCAEC,SAAS,IAAM,qBAAqB;wCACpC,WAAW,CAAC,iDAAiD,EAC3D,sBAAsB,YAClB,qDACA,+CACJ;kDAED;uCARI;;;;;;;;;;;;;;;;;;;;;;0BAiBjB,6LAAC;gBAAI,WAAU;0BACZ,SAAS,MAAM,KAAK,KAAK,CAAC,0BACzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;;;;;;yCAMxC,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4BACtB,MAAM,QAAQ,cAAc,OAAO,KAAK,CAAC,IAAW,EAAE,EAAE,KAAK,QAAQ,OAAO;4BAC5E,MAAM,SAAS,QAAQ,OAAO,KAAK;4BAEnC,qBACE,6LAAC;gCAEC,WAAW,CAAC,2BAA2B,EACrC,SAAS,gBAAgB,iBACzB;;oCAED,CAAC,wBACA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAInB,6LAAC;wCACC,WAAW,CAAC,yBAAyB,EACnC,SACI,2BACA,sCACJ;;4CAED,CAAC,wBACA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,OAAO,QAAQ;;;;;;kEAElB,6LAAC;wDAAK,WAAU;kEACb,OAAO,QAAQ;;;;;;;;;;;;0DAKtB,6LAAC;gDAAE,WAAU;0DAA+B,QAAQ,OAAO;;;;;;0DAE3D,6LAAC;gDAAI,WAAW,CAAC,aAAa,EAC5B,SAAS,kBAAkB,iBAC3B;0DACC,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;oCAIxC,wBACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;+BAxCnD,QAAQ,EAAE;;;;;wBA6CrB;wBAEC,2BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAUvD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;;gDAAgB;gDACpB,eAAe,WAAW;;;;;;;;;;;;;8CAGxC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;;gDAAgB;gDACnB,kBAAkB,YAAY;;;;;;;;;;;;;;;;;;;sCAI/C,6LAAC;4BAAI,WAAU;;gCAAgB;gCAClB,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;GArPgB;KAAA", "debugId": null}}, {"offset": {"line": 4468, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Views/AgentsView.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { useStore } from '@/store/useStore'\nimport { Plus, Search, Bot, Settings, Play, Pause, Code, X } from 'lucide-react'\nimport { AutoGenDemo } from '@/components/Demo/AutoGenDemo'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport * as Dialog from '@radix-ui/react-dialog'\nimport { Agent } from '@/store/useStore'\n\nexport function AgentsView() {\n  const { agents, addAgent, aiModels } = useStore()\n  const t = useTranslation()\n  const [activeTab, setActiveTab] = useState<'agents' | 'demo'>('agents')\n  const [isAgentDialogOpen, setIsAgentDialogOpen] = useState(false)\n  const [agentForm, setAgentForm] = useState({\n    name: '',\n    description: '',\n    type: 'autogen' as 'autogen' | 'custom',\n    modelId: '',\n    systemMessage: '',\n    temperature: 0.7,\n    maxTokens: 1000\n  })\n\n  const handleAddAgent = () => {\n    setAgentForm({\n      name: '',\n      description: '',\n      type: 'autogen',\n      modelId: '',\n      systemMessage: '',\n      temperature: 0.7,\n      maxTokens: 1000\n    })\n    setIsAgentDialogOpen(true)\n  }\n\n  const handleSaveAgent = () => {\n    const selectedModel = aiModels.find(m => m.id === agentForm.modelId)\n\n    const newAgent: Agent = {\n      id: `agent-${Date.now()}`,\n      name: agentForm.name,\n      description: agentForm.description,\n      type: agentForm.type,\n      config: {\n        modelId: agentForm.modelId,\n        modelName: selectedModel?.name || 'Unknown Model',\n        systemMessage: agentForm.systemMessage,\n        temperature: agentForm.temperature,\n        maxTokens: agentForm.maxTokens,\n        provider: selectedModel?.provider || 'Unknown'\n      },\n      createdAt: new Date()\n    }\n\n    addAgent(newAgent)\n    setIsAgentDialogOpen(false)\n  }\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">{t('agents.title')}</h1>\n            <p className=\"text-gray-600 mt-1\">\n              {t('agents.description')}\n            </p>\n          </div>\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex bg-gray-100 rounded-lg p-1\">\n              <button\n                onClick={() => setActiveTab('agents')}\n                className={`px-3 py-1 text-sm rounded-md transition-colors ${\n                  activeTab === 'agents'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                {t('agents.title')}\n              </button>\n              <button\n                onClick={() => setActiveTab('demo')}\n                className={`px-3 py-1 text-sm rounded-md transition-colors ${\n                  activeTab === 'demo'\n                    ? 'bg-white text-gray-900 shadow-sm'\n                    : 'text-gray-600 hover:text-gray-900'\n                }`}\n              >\n                <Code className=\"w-4 h-4 mr-1\" />\n                Demo\n              </button>\n            </div>\n            {activeTab === 'agents' && (\n              <button\n                onClick={handleAddAgent}\n                className=\"flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\"\n              >\n                <Plus className=\"w-4 h-4\" />\n                <span>新建代理</span>\n              </button>\n            )}\n          </div>\n        </div>\n\n        {/* Search - only show for agents tab */}\n        {activeTab === 'agents' && (\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Search agents...\"\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto\">\n        {activeTab === 'demo' ? (\n          <AutoGenDemo />\n        ) : (\n          <div className=\"p-6\">\n            {agents.length === 0 ? (\n          /* Empty State */\n          <div className=\"flex flex-col items-center justify-center h-full text-center\">\n            <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n              <Bot className=\"w-12 h-12 text-gray-400\" />\n            </div>\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              {t('agents.noAgents')}\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-md\">\n              {t('agents.noAgentsDescription')}\n            </p>\n            <button\n              onClick={handleAddAgent}\n              className=\"flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors\"\n            >\n              <Plus className=\"w-5 h-5\" />\n              <span>{t('agents.createFirstAgent')}</span>\n            </button>\n          </div>\n        ) : (\n          /* Agents List */\n          <div className=\"space-y-4\">\n            {agents.map((agent) => (\n              <div\n                key={agent.id}\n                className=\"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow\"\n              >\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center\">\n                      <Bot className=\"w-6 h-6 text-blue-600\" />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-gray-900 mb-1\">\n                        {agent.name}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 mb-2\">\n                        {agent.description}\n                      </p>\n                      <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                        <span className=\"bg-gray-100 px-2 py-1 rounded\">\n                          {agent.type}\n                        </span>\n                        <span>\n                          Created {agent.createdAt.toLocaleDateString()}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center space-x-2\">\n                    <button className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\">\n                      <Settings className=\"w-4 h-4\" />\n                    </button>\n                    <button className=\"p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors\">\n                      <Play className=\"w-4 h-4\" />\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n          </div>\n        )}\n      </div>\n\n      {/* Agent Creation Dialog */}\n      <Dialog.Root open={isAgentDialogOpen} onOpenChange={setIsAgentDialogOpen}>\n        <Dialog.Portal>\n          <Dialog.Overlay className=\"fixed inset-0 bg-black/50 z-50\" />\n          <Dialog.Content className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-xl z-50 w-full max-w-lg p-6 max-h-[90vh] overflow-y-auto\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <Dialog.Title className=\"text-lg font-semibold text-gray-900\">\n                创建新的智能代理\n              </Dialog.Title>\n              <Dialog.Close className=\"text-gray-400 hover:text-gray-600\">\n                <X className=\"w-5 h-5\" />\n              </Dialog.Close>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  代理名称\n                </label>\n                <input\n                  type=\"text\"\n                  value={agentForm.name}\n                  onChange={(e) => setAgentForm({ ...agentForm, name: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  placeholder=\"例如：内容分析助手\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  描述\n                </label>\n                <textarea\n                  value={agentForm.description}\n                  onChange={(e) => setAgentForm({ ...agentForm, description: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  rows={3}\n                  placeholder=\"描述这个代理的功能...\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  代理类型\n                </label>\n                <select\n                  value={agentForm.type}\n                  onChange={(e) => setAgentForm({ ...agentForm, type: e.target.value as 'autogen' | 'custom' })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black\"\n                >\n                  <option value=\"autogen\" className=\"text-black\">AutoGen 代理</option>\n                  <option value=\"custom\" className=\"text-black\">自定义代理</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  AI模型\n                </label>\n                <select\n                  value={agentForm.modelId}\n                  onChange={(e) => setAgentForm({ ...agentForm, modelId: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black\"\n                >\n                  <option value=\"\" className=\"text-black\">选择AI模型</option>\n                  {aiModels.map((model) => (\n                    <option key={model.id} value={model.id} className=\"text-black\">\n                      {model.name} ({model.provider})\n                    </option>\n                  ))}\n                </select>\n                {aiModels.length === 0 && (\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    暂无可用的AI模型。请先在设置 → Integrations 中添加模型。\n                  </p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  系统消息\n                </label>\n                <textarea\n                  value={agentForm.systemMessage}\n                  onChange={(e) => setAgentForm({ ...agentForm, systemMessage: e.target.value })}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                  rows={4}\n                  placeholder=\"你是一个有用的助手，专门用于...\"\n                />\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    温度\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={agentForm.temperature}\n                    onChange={(e) => setAgentForm({ ...agentForm, temperature: parseFloat(e.target.value) })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    min=\"0\"\n                    max=\"2\"\n                    step=\"0.1\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    最大令牌数\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={agentForm.maxTokens}\n                    onChange={(e) => setAgentForm({ ...agentForm, maxTokens: parseInt(e.target.value) })}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    min=\"1\"\n                    max=\"32000\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex justify-end space-x-3 mt-6\">\n              <Dialog.Close className=\"px-4 py-2 text-gray-600 hover:text-gray-700 border border-gray-300 rounded-lg transition-colors\">\n                取消\n              </Dialog.Close>\n              <button\n                onClick={handleSaveAgent}\n                disabled={!agentForm.name || !agentForm.modelId}\n                className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors\"\n              >\n                创建代理\n              </button>\n            </div>\n          </Dialog.Content>\n        </Dialog.Portal>\n      </Dialog.Root>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAUO,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAC9C,MAAM,IAAI,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC9D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM;QACN,aAAa;QACb,MAAM;QACN,SAAS;QACT,eAAe;QACf,aAAa;QACb,WAAW;IACb;IAEA,MAAM,iBAAiB;QACrB,aAAa;YACX,MAAM;YACN,aAAa;YACb,MAAM;YACN,SAAS;YACT,eAAe;YACf,aAAa;YACb,WAAW;QACb;QACA,qBAAqB;IACvB;IAEA,MAAM,kBAAkB;QACtB,MAAM,gBAAgB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO;QAEnE,MAAM,WAAkB;YACtB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB,MAAM,UAAU,IAAI;YACpB,aAAa,UAAU,WAAW;YAClC,MAAM,UAAU,IAAI;YACpB,QAAQ;gBACN,SAAS,UAAU,OAAO;gBAC1B,WAAW,eAAe,QAAQ;gBAClC,eAAe,UAAU,aAAa;gBACtC,aAAa,UAAU,WAAW;gBAClC,WAAW,UAAU,SAAS;gBAC9B,UAAU,eAAe,YAAY;YACvC;YACA,WAAW,IAAI;QACjB;QAEA,SAAS;QACT,qBAAqB;IACvB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoC,EAAE;;;;;;kDACpD,6LAAC;wCAAE,WAAU;kDACV,EAAE;;;;;;;;;;;;0CAGP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,+CAA+C,EACzD,cAAc,WACV,qCACA,qCACJ;0DAED,EAAE;;;;;;0DAEL,6LAAC;gDACC,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAC,+CAA+C,EACzD,cAAc,SACV,qCACA,qCACJ;;kEAEF,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;oCAIpC,cAAc,0BACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;oBAOb,cAAc,0BACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC;gBAAI,WAAU;0BACZ,cAAc,uBACb,6LAAC,4IAAA,CAAA,cAAW;;;;yCAEZ,6LAAC;oBAAI,WAAU;8BACZ,OAAO,MAAM,KAAK,IACrB,eAAe,iBACf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;0CAEL,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;kDAAM,EAAE;;;;;;;;;;;;;;;;;+BAIb,eAAe,iBACf,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gCAEC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,MAAM,IAAI;;;;;;sEAEb,6LAAC;4DAAE,WAAU;sEACV,MAAM,WAAW;;;;;;sEAEpB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EACb,MAAM,IAAI;;;;;;8EAEb,6LAAC;;wEAAK;wEACK,MAAM,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;sDAMnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAO,WAAU;8DAChB,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA/BjB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;0BA4CvB,6LAAC,qKAAA,CAAA,OAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,6LAAC,qKAAA,CAAA,SAAa;;sCACZ,6LAAC,qKAAA,CAAA,UAAc;4BAAC,WAAU;;;;;;sCAC1B,6LAAC,qKAAA,CAAA,UAAc;4BAAC,WAAU;;8CACxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qKAAA,CAAA,QAAY;4CAAC,WAAU;sDAAsC;;;;;;sDAG9D,6LAAC,qKAAA,CAAA,QAAY;4CAAC,WAAU;sDACtB,cAAA,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO,UAAU,IAAI;oDACrB,UAAU,CAAC,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACnE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO,UAAU,WAAW;oDAC5B,UAAU,CAAC,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC1E,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO,UAAU,IAAI;oDACrB,UAAU,CAAC,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAyB;oDAC3F,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;4DAAU,WAAU;sEAAa;;;;;;sEAC/C,6LAAC;4DAAO,OAAM;4DAAS,WAAU;sEAAa;;;;;;;;;;;;;;;;;;sDAIlD,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO,UAAU,OAAO;oDACxB,UAAU,CAAC,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACtE,WAAU;;sEAEV,6LAAC;4DAAO,OAAM;4DAAG,WAAU;sEAAa;;;;;;wDACvC,SAAS,GAAG,CAAC,CAAC,sBACb,6LAAC;gEAAsB,OAAO,MAAM,EAAE;gEAAE,WAAU;;oEAC/C,MAAM,IAAI;oEAAC;oEAAG,MAAM,QAAQ;oEAAC;;+DADnB,MAAM,EAAE;;;;;;;;;;;gDAKxB,SAAS,MAAM,KAAK,mBACnB,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAM9C,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,OAAO,UAAU,aAAa;oDAC9B,UAAU,CAAC,IAAM,aAAa;4DAAE,GAAG,SAAS;4DAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC5E,WAAU;oDACV,MAAM;oDACN,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,UAAU,WAAW;4DAC5B,UAAU,CAAC,IAAM,aAAa;oEAAE,GAAG,SAAS;oEAAE,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAE;4DACtF,WAAU;4DACV,KAAI;4DACJ,KAAI;4DACJ,MAAK;;;;;;;;;;;;8DAGT,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,UAAU,SAAS;4DAC1B,UAAU,CAAC,IAAM,aAAa;oEAAE,GAAG,SAAS;oEAAE,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAE;4DAClF,WAAU;4DACV,KAAI;4DACJ,KAAI;;;;;;;;;;;;;;;;;;;;;;;;8CAMZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qKAAA,CAAA,QAAY;4CAAC,WAAU;sDAAkG;;;;;;sDAG1H,6LAAC;4CACC,SAAS;4CACT,UAAU,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,OAAO;4CAC/C,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAtUgB;;QACyB,2HAAA,CAAA,WAAQ;QACrC,iIAAA,CAAA,iBAAc;;;KAFV", "debugId": null}}, {"offset": {"line": 5296, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Views/SettingsView.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { User, Bell, Shield, Database, Palette, Globe, Plus, Edit, Trash2, X } from 'lucide-react'\nimport * as Tabs from '@radix-ui/react-tabs'\nimport * as Dialog from '@radix-ui/react-dialog'\nimport { useStore } from '@/store/useStore'\nimport { useTranslation } from '@/hooks/useTranslation'\nimport { Language } from '@/lib/i18n'\nimport { AIModel } from '@/store/useStore'\n\nexport function SettingsView() {\n  const { language, setLanguage, aiModels, addAIModel, updateAIModel, deleteAIModel } = useStore()\n  const t = useTranslation()\n  const [isModelDialogOpen, setIsModelDialogOpen] = useState(false)\n  const [editingModel, setEditingModel] = useState<AIModel | null>(null)\n  const [modelForm, setModelForm] = useState({\n    name: '',\n    provider: '',\n    apiKey: '',\n    endpoint: '',\n    maxTokens: 4000,\n    temperature: 0.7\n  })\n\n  const handleAddModel = () => {\n    setEditingModel(null)\n    setModelForm({\n      name: '',\n      provider: '',\n      apiKey: '',\n      endpoint: '',\n      maxTokens: 4000,\n      temperature: 0.7\n    })\n    setIsModelDialogOpen(true)\n  }\n\n  const handleEditModel = (model: AIModel) => {\n    setEditingModel(model)\n    setModelForm({\n      name: model.name,\n      provider: model.provider,\n      apiKey: model.apiKey,\n      endpoint: model.endpoint || '',\n      maxTokens: model.maxTokens,\n      temperature: model.temperature\n    })\n    setIsModelDialogOpen(true)\n  }\n\n  const handleSaveModel = () => {\n    if (editingModel) {\n      updateAIModel(editingModel.id, {\n        ...modelForm,\n        endpoint: modelForm.endpoint || undefined\n      })\n    } else {\n      const newModel: AIModel = {\n        id: `model-${Date.now()}`,\n        ...modelForm,\n        endpoint: modelForm.endpoint || undefined,\n        createdAt: new Date()\n      }\n      addAIModel(newModel)\n    }\n    setIsModelDialogOpen(false)\n  }\n\n  const handleDeleteModel = (id: string) => {\n    if (confirm('确定要删除这个AI模型吗？')) {\n      deleteAIModel(id)\n    }\n  }\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 p-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">{t('settings.title')}</h1>\n        <p className=\"text-gray-600 mt-1\">\n          {t('settings.description')}\n        </p>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto\">\n        <Tabs.Root defaultValue=\"general\" className=\"h-full\">\n          <div className=\"flex h-full\">\n            {/* Sidebar */}\n            <div className=\"w-64 bg-white border-r border-gray-200 p-4\">\n              <Tabs.List className=\"space-y-1\">\n                <Tabs.Trigger\n                  value=\"profile\"\n                  className=\"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>{t('settings.profile')}</span>\n                </Tabs.Trigger>\n                <Tabs.Trigger\n                  value=\"notifications\"\n                  className=\"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors\"\n                >\n                  <Bell className=\"w-4 h-4\" />\n                  <span>{t('settings.notifications')}</span>\n                </Tabs.Trigger>\n                <Tabs.Trigger\n                  value=\"security\"\n                  className=\"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors\"\n                >\n                  <Shield className=\"w-4 h-4\" />\n                  <span>{t('settings.security')}</span>\n                </Tabs.Trigger>\n                <Tabs.Trigger\n                  value=\"integrations\"\n                  className=\"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors\"\n                >\n                  <Database className=\"w-4 h-4\" />\n                  <span>{t('settings.integrations')}</span>\n                </Tabs.Trigger>\n                <Tabs.Trigger\n                  value=\"general\"\n                  className=\"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors\"\n                >\n                  <Globe className=\"w-4 h-4\" />\n                  <span>{t('settings.general')}</span>\n                </Tabs.Trigger>\n                <Tabs.Trigger\n                  value=\"appearance\"\n                  className=\"w-full flex items-center space-x-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-100 rounded-lg data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 transition-colors\"\n                >\n                  <Palette className=\"w-4 h-4\" />\n                  <span>{t('settings.appearance')}</span>\n                </Tabs.Trigger>\n              </Tabs.List>\n            </div>\n\n            {/* Content */}\n            <div className=\"flex-1 p-6\">\n              <Tabs.Content value=\"general\" className=\"space-y-6\">\n                <div>\n                  <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">{t('settings.general')}</h2>\n                  <div className=\"space-y-6\">\n                    {/* Language Setting */}\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        {t('settings.language')}\n                      </label>\n                      <p className=\"text-sm text-gray-600 mb-3\">\n                        {t('settings.languageDescription')}\n                      </p>\n                      <select\n                        value={language}\n                        onChange={(e) => setLanguage(e.target.value as Language)}\n                        className=\"w-full max-w-xs px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black\"\n                      >\n                        <option value=\"en\" className=\"text-black\">{t('settings.english')}</option>\n                        <option value=\"zh\" className=\"text-black\">{t('settings.chinese')}</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              </Tabs.Content>\n\n              <Tabs.Content value=\"profile\" className=\"space-y-6\">\n                <div>\n                  <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Profile Information</h2>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Full Name\n                      </label>\n                      <input\n                        type=\"text\"\n                        defaultValue=\"John Doe\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                        Email\n                      </label>\n                      <input\n                        type=\"email\"\n                        defaultValue=\"<EMAIL>\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      />\n                    </div>\n                  </div>\n                </div>\n              </Tabs.Content>\n\n              <Tabs.Content value=\"notifications\" className=\"space-y-6\">\n                <div>\n                  <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Notification Preferences</h2>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h3 className=\"font-medium text-gray-900\">Workflow Notifications</h3>\n                        <p className=\"text-sm text-gray-600\">Get notified when workflows complete</p>\n                      </div>\n                      <input type=\"checkbox\" defaultChecked className=\"rounded\" />\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <h3 className=\"font-medium text-gray-900\">Agent Updates</h3>\n                        <p className=\"text-sm text-gray-600\">Receive updates about agent status</p>\n                      </div>\n                      <input type=\"checkbox\" defaultChecked className=\"rounded\" />\n                    </div>\n                  </div>\n                </div>\n              </Tabs.Content>\n\n              <Tabs.Content value=\"security\" className=\"space-y-6\">\n                <div>\n                  <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Security Settings</h2>\n                  <div className=\"space-y-4\">\n                    <button className=\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                      <h3 className=\"font-medium text-gray-900\">Change Password</h3>\n                      <p className=\"text-sm text-gray-600\">Update your account password</p>\n                    </button>\n                    <button className=\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\">\n                      <h3 className=\"font-medium text-gray-900\">Two-Factor Authentication</h3>\n                      <p className=\"text-sm text-gray-600\">Add an extra layer of security</p>\n                    </button>\n                  </div>\n                </div>\n              </Tabs.Content>\n\n              <Tabs.Content value=\"integrations\" className=\"space-y-6\">\n                <div>\n                  <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Connected Services</h2>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n                      <div>\n                        <h3 className=\"font-medium text-gray-900\">OpenAI API</h3>\n                        <p className=\"text-sm text-gray-600\">Connected</p>\n                      </div>\n                      <button className=\"text-sm text-red-600 hover:text-red-700\">Disconnect</button>\n                    </div>\n                  </div>\n                </div>\n              </Tabs.Content>\n\n              <Tabs.Content value=\"appearance\" className=\"space-y-6\">\n                <div>\n                  <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Appearance</h2>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Theme\n                      </label>\n                      <select className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black\">\n                        <option className=\"text-black\">Light</option>\n                        <option className=\"text-black\">Dark</option>\n                        <option className=\"text-black\">System</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n              </Tabs.Content>\n            </div>\n          </div>\n        </Tabs.Root>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;;AAPA;;;;;;AAWO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAC7F,MAAM,IAAI,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,MAAM;QACN,UAAU;QACV,QAAQ;QACR,UAAU;QACV,WAAW;QACX,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,aAAa;YACX,MAAM;YACN,UAAU;YACV,QAAQ;YACR,UAAU;YACV,WAAW;YACX,aAAa;QACf;QACA,qBAAqB;IACvB;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;QAChB,aAAa;YACX,MAAM,MAAM,IAAI;YAChB,UAAU,MAAM,QAAQ;YACxB,QAAQ,MAAM,MAAM;YACpB,UAAU,MAAM,QAAQ,IAAI;YAC5B,WAAW,MAAM,SAAS;YAC1B,aAAa,MAAM,WAAW;QAChC;QACA,qBAAqB;IACvB;IAEA,MAAM,kBAAkB;QACtB,IAAI,cAAc;YAChB,cAAc,aAAa,EAAE,EAAE;gBAC7B,GAAG,SAAS;gBACZ,UAAU,UAAU,QAAQ,IAAI;YAClC;QACF,OAAO;YACL,MAAM,WAAoB;gBACxB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;gBACzB,GAAG,SAAS;gBACZ,UAAU,UAAU,QAAQ,IAAI;gBAChC,WAAW,IAAI;YACjB;YACA,WAAW;QACb;QACA,qBAAqB;IACvB;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,QAAQ,kBAAkB;YAC5B,cAAc;QAChB;IACF;IACA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC,EAAE;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAKP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,mKAAA,CAAA,OAAS;oBAAC,cAAa;oBAAU,WAAU;8BAC1C,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mKAAA,CAAA,OAAS;oCAAC,WAAU;;sDACnB,6LAAC,mKAAA,CAAA,UAAY;4CACX,OAAM;4CACN,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAEX,6LAAC,mKAAA,CAAA,UAAY;4CACX,OAAM;4CACN,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAEX,6LAAC,mKAAA,CAAA,UAAY;4CACX,OAAM;4CACN,WAAU;;8DAEV,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAEX,6LAAC,mKAAA,CAAA,UAAY;4CACX,OAAM;4CACN,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAEX,6LAAC,mKAAA,CAAA,UAAY;4CACX,OAAM;4CACN,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;sDAEX,6LAAC,mKAAA,CAAA,UAAY;4CACX,OAAM;4CACN,WAAU;;8DAEV,6LAAC,2MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6LAAC;8DAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAMf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mKAAA,CAAA,UAAY;wCAAC,OAAM;wCAAU,WAAU;kDACtC,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA4C,EAAE;;;;;;8DAC5D,6LAAC;oDAAI,WAAU;8DAEb,cAAA,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EACd,EAAE;;;;;;0EAEL,6LAAC;gEAAE,WAAU;0EACV,EAAE;;;;;;0EAEL,6LAAC;gEACC,OAAO;gEACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC3C,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;wEAAK,WAAU;kFAAc,EAAE;;;;;;kFAC7C,6LAAC;wEAAO,OAAM;wEAAK,WAAU;kFAAc,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOvD,6LAAC,mKAAA,CAAA,UAAY;wCAAC,OAAM;wCAAU,WAAU;kDACtC,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,cAAa;oEACb,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,cAAa;oEACb,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOpB,6LAAC,mKAAA,CAAA,UAAY;wCAAC,OAAM;wCAAgB,WAAU;kDAC5C,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA4B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,6LAAC;oEAAM,MAAK;oEAAW,cAAc;oEAAC,WAAU;;;;;;;;;;;;sEAElD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA4B;;;;;;sFAC1C,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,6LAAC;oEAAM,MAAK;oEAAW,cAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMxD,6LAAC,mKAAA,CAAA,UAAY;wCAAC,OAAM;wCAAW,WAAU;kDACvC,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC;oEAAG,WAAU;8EAA4B;;;;;;8EAC1C,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,6LAAC;4DAAO,WAAU;;8EAChB,6LAAC;oEAAG,WAAU;8EAA4B;;;;;;8EAC1C,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,6LAAC,mKAAA,CAAA,UAAY;wCAAC,OAAM;wCAAe,WAAU;kDAC3C,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA4B;;;;;;kFAC1C,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAEvC,6LAAC;gEAAO,WAAU;0EAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMpE,6LAAC,mKAAA,CAAA,UAAY;wCAAC,OAAM;wCAAa,WAAU;kDACzC,cAAA,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DACzD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC;wEAAO,WAAU;kFAAa;;;;;;kFAC/B,6LAAC;wEAAO,WAAU;kFAAa;;;;;;kFAC/B,6LAAC;wEAAO,WAAU;kFAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvD;GAhQgB;;QACwE,2HAAA,CAAA,WAAQ;QACpF,iIAAA,CAAA,iBAAc;;;KAFV", "debugId": null}}, {"offset": {"line": 6143, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Layout/CenterContent.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useStore } from '@/store/useStore'\nimport { ChatView } from '@/components/Views/ChatView'\nimport { WorkflowsView } from '@/components/Views/WorkflowsView'\nimport { MarketplaceView } from '@/components/Views/MarketplaceView'\nimport { AgentsView } from '@/components/Views/AgentsView'\nimport { SettingsView } from '@/components/Views/SettingsView'\n\nexport function CenterContent() {\n  const { currentView } = useStore()\n\n  const renderView = () => {\n    switch (currentView) {\n      case 'chat':\n        return <ChatView />\n      case 'workflows':\n        return <WorkflowsView />\n      case 'marketplace':\n        return <MarketplaceView />\n      case 'agents':\n        return <AgentsView />\n      case 'settings':\n        return <SettingsView />\n      default:\n        return <WorkflowsView />\n    }\n  }\n\n  return (\n    <div className=\"h-full bg-gray-50\">\n      {renderView()}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAUO,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAE/B,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,0IAAA,CAAA,WAAQ;;;;;YAClB,KAAK;gBACH,qBAAO,6LAAC,+IAAA,CAAA,gBAAa;;;;;YACvB,KAAK;gBACH,qBAAO,6LAAC,iJAAA,CAAA,kBAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,4IAAA,CAAA,aAAU;;;;;YACpB,KAAK;gBACH,qBAAO,6LAAC,8IAAA,CAAA,eAAY;;;;;YACtB;gBACE,qBAAO,6LAAC,+IAAA,CAAA,gBAAa;;;;;QACzB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;GAzBgB;;QACU,2HAAA,CAAA,WAAQ;;;KADlB", "debugId": null}}, {"offset": {"line": 6231, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Layout/RightResults.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useStore } from '@/store/useStore'\nimport { Activity, Clock, CheckCircle, AlertCircle, Play, Pause } from 'lucide-react'\n\nexport function RightResults() {\n  const { currentView } = useStore()\n\n  return (\n    <div className=\"h-full flex flex-col\">\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <h2 className=\"text-lg font-semibold text-gray-900\">结果与活动</h2>\n        <p className=\"text-sm text-gray-500 mt-1\">\n          实时工作流执行和结果\n        </p>\n      </div>\n      \n      {/* Content */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {/* Execution Status */}\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n          <div className=\"flex items-center justify-between mb-3\">\n            <h3 className=\"font-medium text-gray-900\">执行状态</h3>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n              <span className=\"text-xs text-gray-500\">活跃</span>\n            </div>\n          </div>\n\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center justify-between text-sm\">\n              <span className=\"text-gray-600\">运行中的工作流</span>\n              <span className=\"font-medium\">3</span>\n            </div>\n            <div className=\"flex items-center justify-between text-sm\">\n              <span className=\"text-gray-600\">今日完成</span>\n              <span className=\"font-medium\">12</span>\n            </div>\n            <div className=\"flex items-center justify-between text-sm\">\n              <span className=\"text-gray-600\">成功率</span>\n              <span className=\"font-medium text-green-600\">98.5%</span>\n            </div>\n          </div>\n        </div>\n        \n        {/* Recent Activity */}\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n          <h3 className=\"font-medium text-gray-900 mb-3\">最近活动</h3>\n\n          <div className=\"space-y-3\">\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                <CheckCircle className=\"w-4 h-4 text-green-600\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  数据处理工作流\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  成功完成 • 2分钟前\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                <Play className=\"w-4 h-4 text-blue-600\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  邮件营销代理\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  开始执行 • 5分钟前\n                </p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                <Clock className=\"w-4 h-4 text-yellow-600\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  内容分析\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  等待输入 • 10分钟前\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                <AlertCircle className=\"w-4 h-4 text-red-600\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  API集成测试\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  执行失败 • 15分钟前\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* Quick Actions */}\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n          <h3 className=\"font-medium text-gray-900 mb-3\">快速操作</h3>\n\n          <div className=\"space-y-2\">\n            <button className=\"w-full text-left p-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors\">\n              查看所有日志\n            </button>\n            <button className=\"w-full text-left p-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors\">\n              导出结果\n            </button>\n            <button className=\"w-full text-left p-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors\">\n              计划报告\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;AAMO,SAAS;;IACd,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAE/B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAM5C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;;kCAMnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAE/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAMzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAMzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAMzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEAGjD,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAE/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAA2F;;;;;;kDAG7G,6LAAC;wCAAO,WAAU;kDAA2F;;;;;;kDAG7G,6LAAC;wCAAO,WAAU;kDAA2F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzH;GA3HgB;;QACU,2HAAA,CAAA,WAAQ;;;KADlB", "debugId": null}}, {"offset": {"line": 6711, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/Code/Tests/AgentHub/agenthub/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { useStore } from '@/store/useStore'\nimport { TopMenuBar } from './TopMenuBar'\nimport { LeftNavigation } from './LeftNavigation'\nimport { CenterContent } from './CenterContent'\nimport { RightResults } from './RightResults'\n\nexport function MainLayout() {\n  const { sidebarCollapsed } = useStore()\n\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50\">\n      {/* Top Menu Bar */}\n      <TopMenuBar />\n      \n      {/* Main Content Area */}\n      <div className=\"flex-1 flex overflow-hidden\">\n        {/* Left Navigation */}\n        <LeftNavigation />\n        \n        {/* Center Content */}\n        <div className={`flex-1 transition-all duration-300 ${\n          sidebarCollapsed ? 'ml-16' : 'ml-64'\n        }`}>\n          <CenterContent />\n        </div>\n        \n        {/* Right Results Panel */}\n        <div className=\"w-80 border-l border-gray-200 bg-white\">\n          <RightResults />\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;AASO,SAAS;;IACd,MAAM,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6IAAA,CAAA,aAAU;;;;;0BAGX,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,iJAAA,CAAA,iBAAc;;;;;kCAGf,6LAAC;wBAAI,WAAW,CAAC,mCAAmC,EAClD,mBAAmB,UAAU,SAC7B;kCACA,cAAA,6LAAC,gJAAA,CAAA,gBAAa;;;;;;;;;;kCAIhB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;AAKvB;GA3BgB;;QACe,2HAAA,CAAA,WAAQ;;;KADvB", "debugId": null}}]}