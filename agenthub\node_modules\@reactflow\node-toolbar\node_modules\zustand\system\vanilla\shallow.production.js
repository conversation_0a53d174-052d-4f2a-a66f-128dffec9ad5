System.register([],function(f){"use strict";return{execute:function(){f("shallow",i);function i(t,r){if(Object.is(t,r))return!0;if(typeof t!="object"||t===null||typeof r!="object"||r===null)return!1;if(t instanceof Map&&r instanceof Map){if(t.size!==r.size)return!1;for(const[e,o]of t)if(!Object.is(o,r.get(e)))return!1;return!0}if(t instanceof Set&&r instanceof Set){if(t.size!==r.size)return!1;for(const e of t)if(!r.has(e))return!1;return!0}const n=Object.keys(t);if(n.length!==Object.keys(r).length)return!1;for(const e of n)if(!Object.prototype.hasOwnProperty.call(r,e)||!Object.is(t[e],r[e]))return!1;return!0}}}});
