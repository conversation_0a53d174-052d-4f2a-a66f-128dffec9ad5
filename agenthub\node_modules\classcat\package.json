{"name": "classcat", "version": "5.0.5", "type": "module", "main": "index.cjs", "module": "index.js", "types": "index.d.ts", "description": "Build a class attribute string quickly.", "repository": "jorgebucaran/classcat", "license": "MIT", "exports": {"./package.json": "./package.json", ".": {"require": "./index.cjs", "import": "./index.js"}}, "files": ["*.*(c)[tj]s*"], "author": "<PERSON>", "keywords": ["classnames", "classlist", "class"], "scripts": {"test": "c8 twist tests/*.js", "build": "node -e \"fs.writeFileSync('index.cjs',fs.readFileSync('index.js','utf8').replace(/export default/,'module.exports ='),'utf8')\"", "deploy": "npm test && git commit --all --message $tag && git tag --sign $tag --message $tag && git push && git push --tags", "release": "tag=$npm_package_version npm run deploy && npm publish --access public", "prepare": "npm run build"}, "devDependencies": {"c8": "*", "twist": "*"}}