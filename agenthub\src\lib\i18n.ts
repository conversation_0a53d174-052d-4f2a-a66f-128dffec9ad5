// 多语言支持配置
export type Language = 'en' | 'zh'

export interface Translations {
  // 导航菜单
  nav: {
    chat: string
    workflows: string
    marketplace: string
    agents: string
    settings: string
  }
  
  // 聊天页面
  chat: {
    title: string
    newChat: string
    searchChats: string
    noChats: string
    noChatsDescription: string
    startFirstChat: string
    typeMessage: string
    send: string
    thinking: string
  }
  
  // 工作流页面
  workflows: {
    title: string
    description: string
    newWorkflow: string
    searchWorkflows: string
    noWorkflows: string
    noWorkflowsDescription: string
    createFirstWorkflow: string
    editor: string
    list: string
    save: string
    run: string
  }
  
  // 市场页面
  marketplace: {
    title: string
    description: string
    searchWorkflows: string
    featured: string
    popular: string
    recent: string
    saveToMyWorkflows: string
    viewDetails: string
    author: string
    downloads: string
    rating: string
    tags: string
    description_label: string
    close: string
  }
  
  // 智能体页面
  agents: {
    title: string
    description: string
    newAgent: string
    searchAgents: string
    noAgents: string
    noAgentsDescription: string
    createFirstAgent: string
    demo: string
    agents: string
    type: string
    model: string
    temperature: string
    context7: string
    enabled: string
    disabled: string
    startDemo: string
    stopDemo: string
    enableContext7: string
    libraries: string
    status: string
    messages: string
    agentsCollaborating: string
    readyToStart: string
    readyDescription: string
  }
  
  // 设置页面
  settings: {
    title: string
    description: string
    language: string
    languageDescription: string
    english: string
    chinese: string
    general: string
    appearance: string
    advanced: string
  }
  
  // 通用
  common: {
    save: string
    cancel: string
    delete: string
    edit: string
    create: string
    search: string
    loading: string
    error: string
    success: string
    confirm: string
    back: string
    next: string
    previous: string
    close: string
    open: string
    view: string
    download: string
    upload: string
    copy: string
    paste: string
    cut: string
    undo: string
    redo: string
  }
  
  // 工作流编辑器
  workflowEditor: {
    title: string
    addNode: string
    deleteNode: string
    connectNodes: string
    nodeTypes: {
      input: string
      output: string
      aiAgent: string
      condition: string
      action: string
    }
    properties: string
    configuration: string
  }
}

// 英文翻译
export const enTranslations: Translations = {
  nav: {
    chat: 'Chat',
    workflows: 'Workflows',
    marketplace: 'Marketplace',
    agents: 'Agents',
    settings: 'Settings'
  },
  
  chat: {
    title: 'Chat',
    newChat: 'New Chat',
    searchChats: 'Search chats...',
    noChats: 'No conversations yet',
    noChatsDescription: 'Start your first conversation with an AI agent to begin collaborating on your projects.',
    startFirstChat: 'Start Your First Chat',
    typeMessage: 'Type your message...',
    send: 'Send',
    thinking: 'AI is thinking...'
  },
  
  workflows: {
    title: 'My Workflows',
    description: 'Create and manage your custom workflows',
    newWorkflow: 'New Workflow',
    searchWorkflows: 'Search workflows...',
    noWorkflows: 'No workflows created',
    noWorkflowsDescription: 'Create your first workflow to automate tasks with AI agents.',
    createFirstWorkflow: 'Create Your First Workflow',
    editor: 'Editor',
    list: 'List',
    save: 'Save',
    run: 'Run'
  },
  
  marketplace: {
    title: 'Workflow Marketplace',
    description: 'Discover and use workflows created by the community',
    searchWorkflows: 'Search workflows...',
    featured: 'Featured',
    popular: 'Popular',
    recent: 'Recent',
    saveToMyWorkflows: 'Save to My Workflows',
    viewDetails: 'View Details',
    author: 'Author',
    downloads: 'Downloads',
    rating: 'Rating',
    tags: 'Tags',
    description_label: 'Description',
    close: 'Close'
  },
  
  agents: {
    title: 'AI Agents',
    description: 'Manage and configure your AI agents',
    newAgent: 'New Agent',
    searchAgents: 'Search agents...',
    noAgents: 'No agents configured',
    noAgentsDescription: 'Create your first AI agent to start automating tasks. Choose from AutoGen templates or build custom agents.',
    createFirstAgent: 'Create Your First Agent',
    demo: 'Demo',
    agents: 'Agents',
    type: 'Type',
    model: 'Model',
    temperature: 'Temperature',
    context7: 'Context7',
    enabled: 'Enabled',
    disabled: 'Disabled',
    startDemo: 'Start Demo',
    stopDemo: 'Stop Demo',
    enableContext7: 'Enable Context7 Enhancement',
    libraries: 'Context7 Libraries',
    status: 'Status',
    messages: 'Messages',
    agentsCollaborating: 'Agents are collaborating...',
    readyToStart: 'Ready to Start Demo',
    readyDescription: 'Click "Start Demo" to see AutoGen agents collaborate on creating AI workflow documentation, enhanced with Context7\'s up-to-date libraries.'
  },
  
  settings: {
    title: 'Settings',
    description: 'Configure your preferences and application settings',
    language: 'Language',
    languageDescription: 'Choose your preferred language for the interface',
    english: 'English',
    chinese: '中文',
    general: 'General',
    appearance: 'Appearance',
    advanced: 'Advanced',
    profile: 'Profile',
    notifications: 'Notifications',
    security: 'Security',
    integrations: 'Integrations',
    // Profile page
    profileInformation: 'Profile Information',
    fullName: 'Full Name',
    email: 'Email',
    // Notifications page
    notificationPreferences: 'Notification Preferences',
    workflowNotifications: 'Workflow Notifications',
    workflowNotificationsDesc: 'Get notified when workflows complete',
    agentNotifications: 'Agent Notifications',
    agentNotificationsDesc: 'Receive updates about agent activities',
    // Security page
    securitySettings: 'Security Settings',
    changePassword: 'Change Password',
    changePasswordDesc: 'Update your account password',
    twoFactorAuth: 'Two-Factor Authentication',
    twoFactorAuthDesc: 'Add an extra layer of security',
    // Integrations page
    connectedServices: 'Connected Services',
    aiModels: 'AI Models',
    aiModelsDesc: 'Manage your AI models and configurations',
    addModel: 'Add Model',
    editModel: 'Edit Model',
    deleteModel: 'Delete Model',
    modelName: 'Model Name',
    provider: 'Provider',
    apiKey: 'API Key',
    endpoint: 'Endpoint',
    maxTokens: 'Max Tokens',
    temperature: 'Temperature'
  },
  
  common: {
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    create: 'Create',
    search: 'Search',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    confirm: 'Confirm',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    close: 'Close',
    open: 'Open',
    view: 'View',
    download: 'Download',
    upload: 'Upload',
    copy: 'Copy',
    paste: 'Paste',
    cut: 'Cut',
    undo: 'Undo',
    redo: 'Redo'
  },
  
  workflowEditor: {
    title: 'Workflow Editor',
    addNode: 'Add Node',
    deleteNode: 'Delete Node',
    connectNodes: 'Connect Nodes',
    nodeTypes: {
      input: 'Input',
      output: 'Output',
      aiAgent: 'AI Agent',
      condition: 'Condition',
      action: 'Action'
    },
    properties: 'Properties',
    configuration: 'Configuration'
  }
}

// 中文翻译
export const zhTranslations: Translations = {
  nav: {
    chat: '聊天',
    workflows: '我的工作流',
    marketplace: '工作流市场',
    agents: '我的AI-Agent',
    settings: '设置'
  },

  chat: {
    title: '聊天',
    newChat: '新建聊天',
    searchChats: '搜索聊天记录...',
    noChats: '暂无对话',
    noChatsDescription: '开始与AI智能体的第一次对话，开始协作处理您的项目。',
    startFirstChat: '开始第一次聊天',
    typeMessage: '输入您的消息...',
    send: '发送',
    thinking: 'AI正在思考中...'
  },

  workflows: {
    title: '我的工作流',
    description: '创建和管理您的自定义工作流',
    newWorkflow: '新建工作流',
    searchWorkflows: '搜索工作流...',
    noWorkflows: '暂无工作流',
    noWorkflowsDescription: '创建您的第一个工作流，使用AI智能体自动化任务。',
    createFirstWorkflow: '创建第一个工作流',
    editor: '编辑器',
    list: '列表',
    save: '保存',
    run: '运行'
  },

  marketplace: {
    title: '工作流市场',
    description: '发现并使用社区创建的工作流',
    searchWorkflows: '搜索工作流...',
    featured: '精选',
    popular: '热门',
    recent: '最新',
    saveToMyWorkflows: '保存到我的工作流',
    viewDetails: '查看详情',
    author: '作者',
    downloads: '下载量',
    rating: '评分',
    tags: '标签',
    description_label: '描述',
    close: '关闭'
  },

  agents: {
    title: '我的AI-Agent',
    description: '管理和配置您的AI智能体',
    newAgent: '新建智能体',
    searchAgents: '搜索智能体...',
    noAgents: '暂无智能体',
    noAgentsDescription: '创建您的第一个AI智能体来开始自动化任务。从AutoGen模板中选择或构建自定义智能体。',
    createFirstAgent: '创建第一个智能体',
    demo: '演示',
    agents: '智能体',
    type: '类型',
    model: '模型',
    temperature: '温度',
    context7: 'Context7',
    enabled: '已启用',
    disabled: '已禁用',
    startDemo: '开始演示',
    stopDemo: '停止演示',
    enableContext7: '启用Context7增强',
    libraries: 'Context7库',
    status: '状态',
    messages: '消息',
    agentsCollaborating: '智能体正在协作中...',
    readyToStart: '准备开始演示',
    readyDescription: '点击"开始演示"查看AutoGen智能体协作创建AI工作流文档，并使用Context7的最新库进行增强。'
  },

  settings: {
    title: '设置',
    description: '配置您的偏好和应用程序设置',
    language: '语言',
    languageDescription: '选择您偏好的界面语言',
    english: 'English',
    chinese: '中文',
    general: '通用',
    appearance: '外观',
    advanced: '高级',
    profile: '个人资料',
    notifications: '通知',
    security: '安全',
    integrations: '集成',
    // Profile page
    profileInformation: '个人信息',
    fullName: '全名',
    email: '邮箱',
    // Notifications page
    notificationPreferences: '通知偏好',
    workflowNotifications: '工作流通知',
    workflowNotificationsDesc: '工作流完成时接收通知',
    agentNotifications: '代理通知',
    agentNotificationsDesc: '接收代理活动更新',
    // Security page
    securitySettings: '安全设置',
    changePassword: '更改密码',
    changePasswordDesc: '更新您的账户密码',
    twoFactorAuth: '双因素认证',
    twoFactorAuthDesc: '添加额外的安全层',
    // Integrations page
    connectedServices: '已连接服务',
    aiModels: 'AI模型',
    aiModelsDesc: '管理您的AI模型和配置',
    addModel: '添加模型',
    editModel: '编辑模型',
    deleteModel: '删除模型',
    modelName: '模型名称',
    provider: '提供商',
    apiKey: 'API密钥',
    endpoint: '端点',
    maxTokens: '最大令牌数',
    temperature: '温度'
  },

  common: {
    save: '保存',
    cancel: '取消',
    delete: '删除',
    edit: '编辑',
    create: '创建',
    search: '搜索',
    loading: '加载中...',
    error: '错误',
    success: '成功',
    confirm: '确认',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    close: '关闭',
    open: '打开',
    view: '查看',
    download: '下载',
    upload: '上传',
    copy: '复制',
    paste: '粘贴',
    cut: '剪切',
    undo: '撤销',
    redo: '重做'
  },

  workflowEditor: {
    title: '工作流编辑器',
    addNode: '添加节点',
    deleteNode: '删除节点',
    connectNodes: '连接节点',
    nodeTypes: {
      input: '输入',
      output: '输出',
      aiAgent: 'AI智能体',
      condition: '条件',
      action: '动作'
    },
    properties: '属性',
    configuration: '配置'
  }
}

// 语言配置
export const translations = {
  en: enTranslations,
  zh: zhTranslations
}

// 获取翻译文本的辅助函数
export function getTranslation(language: Language, key: string): string {
  const keys = key.split('.')
  let value: any = translations[language]

  for (const k of keys) {
    value = value?.[k]
  }

  return value || key
}

// 创建翻译钩子
export function createTranslationHook(language: Language) {
  return function t(key: string): string {
    return getTranslation(language, key)
  }
}
