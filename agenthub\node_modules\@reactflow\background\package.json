{"name": "@reactflow/background", "version": "11.3.14", "description": "Background component with different variants for React Flow", "keywords": ["react", "node-based UI", "graph", "diagram", "workflow", "react-flow"], "files": ["dist"], "source": "src/index.tsx", "main": "dist/umd/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.mjs", "require": "./dist/umd/index.js"}}, "sideEffects": ["*.css"], "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/xyflow/xyflow.git", "directory": "packages/background"}, "dependencies": {"classcat": "^5.0.3", "zustand": "^4.4.1", "@reactflow/core": "11.11.4"}, "devDependencies": {"@types/node": "^18.7.16", "@types/react": ">=17", "react": "^18.2.0", "typescript": "^4.9.4", "@reactflow/eslint-config": "0.0.0", "@reactflow/tsconfig": "0.0.0", "@reactflow/rollup-config": "0.0.0"}, "peerDependencies": {"react": ">=17", "react-dom": ">=17"}, "rollup": {"globals": {"zustand": "Zustand", "zustand/shallow": "zustandShallow", "classcat": "cc"}, "name": "ReactFlowBackground"}, "scripts": {"dev": "rollup --config node:@reactflow/rollup-config --watch", "build": "rollup --config node:@reactflow/rollup-config --environment NODE_ENV:production", "lint": "eslint --ext .js,.jsx,.ts,.tsx src", "typecheck": "tsc --noEmit", "clean": "rimraf dist .turbo"}}